<?php
/**
 * Generated by PHPUnit_SkeletonGenerator on 2012-08-24 at 15:45:18.
 */
class Pix_Array_MergerTest extends PHPUnit_Framework_TestCase
{
    /**
     * @dataProvider arrayProvider
     */
    public function testOrder($array_male, $array_female)
    {
        $array_male = Pix_Array::factory($array_male);
        $array_female = Pix_Array::factory($array_female);

        $array_merged = new Pix_Array_Merger($array_male, $array_female);

        $actual_result = array();

        foreach ($array_merged->order('age, name DESC') as $person) {
            $actual_result[] = $person['name'];
        }

        $excepted_result = array('fox', 'alice', 'bob', 'eva', 'david', 'carole');
        $this->assertEquals($actual_result, $excepted_result);
    }

    public function testChunkSize()
    {
        $array = new Pix_Array_Merger();
        $this->assertEquals($array->chunkSize(5)->chunkSize(), 5);
        $this->assertEquals($array->chunkSize(10)->chunkSize(), 10);
    }

    /**
     * @dataProvider arrayProvider
     */
    public function testObjectOrder($array_male, $array_female)
    {
        // associate array to object array
        $array_male = Pix_Array::factory(json_decode(json_encode($array_male)));
        $array_female = Pix_Array::factory(json_decode(json_encode($array_female)));

        $array_merged = new Pix_Array_Merger($array_male, $array_female);

        $actual_result = array();

        foreach ($array_merged->order('age, name DESC') as $key => $person) {
            $actual_result[] = $person->name;
        }

        $excepted_result = array('fox', 'alice', 'bob', 'eva', 'david', 'carole');
        $this->assertEquals($actual_result, $excepted_result);
    }

    /**
     * @dataProvider arrayProvider
     */
    public function testAfter($array_male, $array_female)
    {
        $array_male = Pix_Array::factory($array_male);
        $array_female = Pix_Array::factory($array_female);
        $array_merged = new Pix_Array_Merger($array_male, $array_female);

        // age > 26
        $actual_result = array();
        foreach ($array_merged->order('age, name DESC')->after(array('age' => 26)) as $person) {
            $actual_result[] = $person['name'];
        }
        $this->assertEquals($actual_result, array('eva', 'david', 'carole')); // 27, 27, 30

        // age > 27
        $actual_result = array();
        foreach ($array_merged->order('age, name DESC')->after(array('age' => 27)) as $person) {
            $actual_result[] = $person['name'];
        }
        $this->assertEquals($actual_result, array('carole')); // 30

        // age >= 27
        $actual_result = array();
        foreach ($array_merged->order('age, name DESC')->after(array('age' => 27), true) as $person) {
            $actual_result[] = $person['name'];
        }
        $this->assertEquals($actual_result, array('eva', 'david', 'carole')); // 27, 27, 30
    }

    /**
     * @dataProvider arrayProvider
     */
    public function testKeyAndAfter($array_male, $array_female)
    {
        $array_male = Pix_Array::factory($array_male);
        $array_female = Pix_Array::factory($array_female);
        $array_merged = new Pix_Array_Merger($array_male, $array_female);

        $actual_result = array();
        $i = 0;
        foreach ($array_merged->order('age, name DESC') as $key => $person) {
            $actual_result[] = $person['name'];
            $i ++;
            if ($i > 1) {
                break;
            }
        }
        $this->assertEquals($actual_result, array('fox', 'alice')); // 18, 18

        $actual_result = array();
        $i = 0;
        foreach ($array_merged->order('age, name DESC')->after($key) as $key => $person) {
            $actual_result[] = $person['name'];
            $i ++;
            if ($i > 1) {
                break;
            }
        }
        $this->assertEquals($actual_result, array('bob', 'eva')); // 24, 27

        $actual_result = array();
        $i = 0;
        foreach ($array_merged->order('age, name DESC')->after($key) as $key => $person) {
            $actual_result[] = $person['name'];
            $i ++;
            if ($i > 1) {
                break;
            }
        }
        $this->assertEquals($actual_result, array('david', 'carole')); // 27, 30

        $actual_result = array();
        foreach ($array_merged->order('age, name DESC')->after($key) as $key => $person) {
            $actual_result[] = $person['name'];
        }
        $this->assertEquals($actual_result, array());
    }

    /**
     * @dataProvider arrayProvider
     * @expectedException Pix_Exception
     */
    public function testNoOrder($array_male, $array_female)
    {
        $array_male = Pix_Array::factory($array_male);
        $array_female = Pix_Array::factory($array_female);
        $array_merged = new Pix_Array_Merger($array_male, $array_female);

        foreach ($array_merged as $row) {
        }
    }

    public function arrayProvider()
    {
        return array(
            array(
                array(
                    array('name' => 'bob', 'age' => 24, 'height' => 175),
                    array('name' => 'david', 'age' => 27, 'height' => 183),
                    array('name' => 'fox', 'age' => 18, 'height' => 165),
                ),
                array(
                    array('name' => 'alice', 'age' => 18, 'height' => 158),
                    array('name' => 'carole', 'age' => 30, 'height' => 163),
                    array('name' => 'eva', 'age' => 27, 'height' => 171),
                ),
            ),
        );
    }
}
