--
-- PostgreSQL database dump
--

-- Dumped from database version 15.7
-- Dumped by pg_dump version 17.5

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: tender_category_type; Type: TYPE; Schema: public; Owner: bidradaradmin
--

CREATE TYPE public.tender_category_type AS ENUM (
    'goods',
    'services',
    'construction',
    'other'
);


ALTER TYPE public.tender_category_type OWNER TO bidradaradmin;

--
-- Name: tender_status; Type: TYPE; Schema: public; Owner: bidradaradmin
--

CREATE TYPE public.tender_status AS ENUM (
    'bidding',
    'awarded',
    'failed',
    'cancelled',
    'open',
    'closed'
);


ALTER TYPE public.tender_status OWNER TO bidradaradmin;

--
-- Name: update_tender_fts_document(); Type: FUNCTION; Schema: public; Owner: bidradaradmin
--

CREATE FUNCTION public.update_tender_fts_document() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.fts_document :=
        setweight(to_tsvector('pg_catalog.simple', COALESCE(NEW.title, '')), 'A') ||
        setweight(to_tsvector('pg_catalog.simple', COALESCE(NEW.description, '')), 'B') ||
        setweight(
            (SELECT to_tsvector('pg_catalog.simple', COALESCE(agency_name, '')) FROM agencies WHERE agency_code = NEW.agency_code),
            'B'
        );
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.update_tender_fts_document() OWNER TO bidradaradmin;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: agencies; Type: TABLE; Schema: public; Owner: bidradaradmin
--

CREATE TABLE public.agencies (
    agency_code character varying(50) NOT NULL,
    agency_name character varying(255) NOT NULL,
    agency_unit character varying(255),
    agency_address text,
    contact_person character varying(100),
    contact_phone character varying(50),
    contact_fax character varying(50),
    contact_email character varying(255),
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.agencies OWNER TO bidradaradmin;

--
-- Name: TABLE agencies; Type: COMMENT; Schema: public; Owner: bidradaradmin
--

COMMENT ON TABLE public.agencies IS '機關主檔';


--
-- Name: agency_analytics; Type: TABLE; Schema: public; Owner: bidradaradmin
--

CREATE TABLE public.agency_analytics (
    agency_code character varying(50) NOT NULL,
    total_tenders_awarded integer,
    total_budget_amount numeric,
    total_award_amount numeric,
    avg_award_to_budget_ratio numeric(5,4),
    avg_bidder_count numeric(5,2),
    top_vendors jsonb,
    last_updated_at timestamp with time zone
);


ALTER TABLE public.agency_analytics OWNER TO bidradaradmin;

--
-- Name: award_items; Type: TABLE; Schema: public; Owner: bidradaradmin
--

CREATE TABLE public.award_items (
    item_id integer NOT NULL,
    tender_id uuid NOT NULL,
    company_id character varying(20),
    item_name text,
    item_quantity numeric,
    item_unit character varying(50),
    item_price numeric(18,2),
    created_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.award_items OWNER TO bidradaradmin;

--
-- Name: TABLE award_items; Type: COMMENT; Schema: public; Owner: bidradaradmin
--

COMMENT ON TABLE public.award_items IS '決標品項表';


--
-- Name: award_items_item_id_seq; Type: SEQUENCE; Schema: public; Owner: bidradaradmin
--

CREATE SEQUENCE public.award_items_item_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.award_items_item_id_seq OWNER TO bidradaradmin;

--
-- Name: award_items_item_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: bidradaradmin
--

ALTER SEQUENCE public.award_items_item_id_seq OWNED BY public.award_items.item_id;


--
-- Name: committee_members; Type: TABLE; Schema: public; Owner: bidradaradmin
--

CREATE TABLE public.committee_members (
    member_id integer NOT NULL,
    member_name character varying(100) NOT NULL,
    member_company character varying(200),
    member_position character varying(200),
    member_others text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.committee_members OWNER TO bidradaradmin;

--
-- Name: TABLE committee_members; Type: COMMENT; Schema: public; Owner: bidradaradmin
--

COMMENT ON TABLE public.committee_members IS '評選委員主檔';


--
-- Name: committee_members_member_id_seq; Type: SEQUENCE; Schema: public; Owner: bidradaradmin
--

CREATE SEQUENCE public.committee_members_member_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.committee_members_member_id_seq OWNER TO bidradaradmin;

--
-- Name: committee_members_member_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: bidradaradmin
--

ALTER SEQUENCE public.committee_members_member_id_seq OWNED BY public.committee_members.member_id;


--
-- Name: companies; Type: TABLE; Schema: public; Owner: bidradaradmin
--

CREATE TABLE public.companies (
    company_id character varying(20) NOT NULL,
    company_name character varying(255) NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.companies OWNER TO bidradaradmin;

--
-- Name: TABLE companies; Type: COMMENT; Schema: public; Owner: bidradaradmin
--

COMMENT ON TABLE public.companies IS '廠商主檔 (所有投標過或得標的廠商)';


--
-- Name: company_analytics; Type: TABLE; Schema: public; Owner: bidradaradmin
--

CREATE TABLE public.company_analytics (
    company_id character varying(20) NOT NULL,
    total_bids integer,
    total_wins integer,
    win_rate numeric(5,4),
    total_award_amount numeric,
    avg_award_to_budget_ratio_on_wins numeric(5,4),
    top_agencies jsonb,
    last_updated_at timestamp with time zone
);


ALTER TABLE public.company_analytics OWNER TO bidradaradmin;

--
-- Name: tender_bidders; Type: TABLE; Schema: public; Owner: bidradaradmin
--

CREATE TABLE public.tender_bidders (
    tender_id uuid NOT NULL,
    company_id character varying(20) NOT NULL,
    bid_amount numeric(18,2),
    is_winner boolean DEFAULT false NOT NULL,
    rank integer
);


ALTER TABLE public.tender_bidders OWNER TO bidradaradmin;

--
-- Name: TABLE tender_bidders; Type: COMMENT; Schema: public; Owner: bidradaradmin
--

COMMENT ON TABLE public.tender_bidders IS '標案投標紀錄';


--
-- Name: tender_committee_members; Type: TABLE; Schema: public; Owner: bidradaradmin
--

CREATE TABLE public.tender_committee_members (
    tender_id uuid NOT NULL,
    member_id integer NOT NULL
);


ALTER TABLE public.tender_committee_members OWNER TO bidradaradmin;

--
-- Name: TABLE tender_committee_members; Type: COMMENT; Schema: public; Owner: bidradaradmin
--

COMMENT ON TABLE public.tender_committee_members IS '標案評選委員名單';


--
-- Name: tender_history; Type: TABLE; Schema: public; Owner: bidradaradmin
--

CREATE TABLE public.tender_history (
    history_id integer NOT NULL,
    tender_id uuid NOT NULL,
    change_date timestamp with time zone NOT NULL,
    change_description text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.tender_history OWNER TO bidradaradmin;

--
-- Name: TABLE tender_history; Type: COMMENT; Schema: public; Owner: bidradaradmin
--

COMMENT ON TABLE public.tender_history IS '標案歷史異動紀錄';


--
-- Name: tender_history_history_id_seq; Type: SEQUENCE; Schema: public; Owner: bidradaradmin
--

CREATE SEQUENCE public.tender_history_history_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.tender_history_history_id_seq OWNER TO bidradaradmin;

--
-- Name: tender_history_history_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: bidradaradmin
--

ALTER SEQUENCE public.tender_history_history_id_seq OWNED BY public.tender_history.history_id;


--
-- Name: tenders; Type: TABLE; Schema: public; Owner: bidradaradmin
--

CREATE TABLE public.tenders (
    tender_id uuid DEFAULT gen_random_uuid() NOT NULL,
    pcc_main_key character varying(50) NOT NULL,
    case_number character varying(100),
    title text NOT NULL,
    description text,
    category character varying(255),
    category_code character varying(50),
    category_type public.tender_category_type,
    agency_code character varying(50) NOT NULL,
    budget_amount numeric(18,2),
    budget_disclosed boolean DEFAULT true,
    budget_range character varying(100),
    award_amount numeric(18,2),
    bidder_count integer,
    announcement_date date,
    submission_deadline timestamp with time zone,
    opening_time timestamp with time zone,
    award_date date,
    status public.tender_status,
    procurement_method character varying(100),
    award_method character varying(100),
    performance_location character varying(255),
    performance_period text,
    is_wto_gpa boolean DEFAULT false,
    is_multiple_award boolean DEFAULT false,
    is_joint_procurement boolean DEFAULT false,
    is_electronic_bidding boolean DEFAULT false,
    requires_deposit boolean DEFAULT false,
    is_turnkey boolean DEFAULT false,
    has_follow_up_expansion boolean DEFAULT false,
    source_url text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    fts_document tsvector
);


ALTER TABLE public.tenders OWNER TO bidradaradmin;

--
-- Name: TABLE tenders; Type: COMMENT; Schema: public; Owner: bidradaradmin
--

COMMENT ON TABLE public.tenders IS '標案主表';


--
-- Name: award_items item_id; Type: DEFAULT; Schema: public; Owner: bidradaradmin
--

ALTER TABLE ONLY public.award_items ALTER COLUMN item_id SET DEFAULT nextval('public.award_items_item_id_seq'::regclass);


--
-- Name: committee_members member_id; Type: DEFAULT; Schema: public; Owner: bidradaradmin
--

ALTER TABLE ONLY public.committee_members ALTER COLUMN member_id SET DEFAULT nextval('public.committee_members_member_id_seq'::regclass);


--
-- Name: tender_history history_id; Type: DEFAULT; Schema: public; Owner: bidradaradmin
--

ALTER TABLE ONLY public.tender_history ALTER COLUMN history_id SET DEFAULT nextval('public.tender_history_history_id_seq'::regclass);


--
-- Name: agencies agencies_pkey; Type: CONSTRAINT; Schema: public; Owner: bidradaradmin
--

ALTER TABLE ONLY public.agencies
    ADD CONSTRAINT agencies_pkey PRIMARY KEY (agency_code);


--
-- Name: agency_analytics agency_analytics_pkey; Type: CONSTRAINT; Schema: public; Owner: bidradaradmin
--

ALTER TABLE ONLY public.agency_analytics
    ADD CONSTRAINT agency_analytics_pkey PRIMARY KEY (agency_code);


--
-- Name: award_items award_items_pkey; Type: CONSTRAINT; Schema: public; Owner: bidradaradmin
--

ALTER TABLE ONLY public.award_items
    ADD CONSTRAINT award_items_pkey PRIMARY KEY (item_id);


--
-- Name: committee_members committee_members_member_name_member_company_member_positio_key; Type: CONSTRAINT; Schema: public; Owner: bidradaradmin
--

ALTER TABLE ONLY public.committee_members
    ADD CONSTRAINT committee_members_member_name_member_company_member_positio_key UNIQUE (member_name, member_company, member_position);


--
-- Name: committee_members committee_members_pkey; Type: CONSTRAINT; Schema: public; Owner: bidradaradmin
--

ALTER TABLE ONLY public.committee_members
    ADD CONSTRAINT committee_members_pkey PRIMARY KEY (member_id);


--
-- Name: companies companies_company_name_key; Type: CONSTRAINT; Schema: public; Owner: bidradaradmin
--

ALTER TABLE ONLY public.companies
    ADD CONSTRAINT companies_company_name_key UNIQUE (company_name);


--
-- Name: companies companies_pkey; Type: CONSTRAINT; Schema: public; Owner: bidradaradmin
--

ALTER TABLE ONLY public.companies
    ADD CONSTRAINT companies_pkey PRIMARY KEY (company_id);


--
-- Name: company_analytics company_analytics_pkey; Type: CONSTRAINT; Schema: public; Owner: bidradaradmin
--

ALTER TABLE ONLY public.company_analytics
    ADD CONSTRAINT company_analytics_pkey PRIMARY KEY (company_id);


--
-- Name: tender_bidders tender_bidders_pkey; Type: CONSTRAINT; Schema: public; Owner: bidradaradmin
--

ALTER TABLE ONLY public.tender_bidders
    ADD CONSTRAINT tender_bidders_pkey PRIMARY KEY (tender_id, company_id);


--
-- Name: tender_committee_members tender_committee_members_pkey; Type: CONSTRAINT; Schema: public; Owner: bidradaradmin
--

ALTER TABLE ONLY public.tender_committee_members
    ADD CONSTRAINT tender_committee_members_pkey PRIMARY KEY (tender_id, member_id);


--
-- Name: tender_history tender_history_pkey; Type: CONSTRAINT; Schema: public; Owner: bidradaradmin
--

ALTER TABLE ONLY public.tender_history
    ADD CONSTRAINT tender_history_pkey PRIMARY KEY (history_id);


--
-- Name: tenders tenders_pcc_main_key_key; Type: CONSTRAINT; Schema: public; Owner: bidradaradmin
--

ALTER TABLE ONLY public.tenders
    ADD CONSTRAINT tenders_pcc_main_key_key UNIQUE (pcc_main_key);


--
-- Name: tenders tenders_pkey; Type: CONSTRAINT; Schema: public; Owner: bidradaradmin
--

ALTER TABLE ONLY public.tenders
    ADD CONSTRAINT tenders_pkey PRIMARY KEY (tender_id);


--
-- Name: idx_tender_bidders_company_id; Type: INDEX; Schema: public; Owner: bidradaradmin
--

CREATE INDEX idx_tender_bidders_company_id ON public.tender_bidders USING btree (company_id);


--
-- Name: idx_tender_committee_members_member_id; Type: INDEX; Schema: public; Owner: bidradaradmin
--

CREATE INDEX idx_tender_committee_members_member_id ON public.tender_committee_members USING btree (member_id);


--
-- Name: idx_tenders_agency_code; Type: INDEX; Schema: public; Owner: bidradaradmin
--

CREATE INDEX idx_tenders_agency_code ON public.tenders USING btree (agency_code);


--
-- Name: idx_tenders_announcement_date; Type: INDEX; Schema: public; Owner: bidradaradmin
--

CREATE INDEX idx_tenders_announcement_date ON public.tenders USING btree (announcement_date DESC);


--
-- Name: idx_tenders_award_date; Type: INDEX; Schema: public; Owner: bidradaradmin
--

CREATE INDEX idx_tenders_award_date ON public.tenders USING btree (award_date DESC);


--
-- Name: idx_tenders_award_method; Type: INDEX; Schema: public; Owner: bidradaradmin
--

CREATE INDEX idx_tenders_award_method ON public.tenders USING btree (award_method);


--
-- Name: idx_tenders_budget_range; Type: INDEX; Schema: public; Owner: bidradaradmin
--

CREATE INDEX idx_tenders_budget_range ON public.tenders USING btree (budget_range);


--
-- Name: idx_tenders_category; Type: INDEX; Schema: public; Owner: bidradaradmin
--

CREATE INDEX idx_tenders_category ON public.tenders USING btree (category);


--
-- Name: idx_tenders_fts_document; Type: INDEX; Schema: public; Owner: bidradaradmin
--

CREATE INDEX idx_tenders_fts_document ON public.tenders USING gin (fts_document);


--
-- Name: idx_tenders_performance_location; Type: INDEX; Schema: public; Owner: bidradaradmin
--

CREATE INDEX idx_tenders_performance_location ON public.tenders USING btree (performance_location);


--
-- Name: idx_tenders_status; Type: INDEX; Schema: public; Owner: bidradaradmin
--

CREATE INDEX idx_tenders_status ON public.tenders USING btree (status);


--
-- Name: tenders trg_update_tender_fts; Type: TRIGGER; Schema: public; Owner: bidradaradmin
--

CREATE TRIGGER trg_update_tender_fts BEFORE INSERT OR UPDATE ON public.tenders FOR EACH ROW EXECUTE FUNCTION public.update_tender_fts_document();


--
-- Name: agency_analytics agency_analytics_agency_code_fkey; Type: FK CONSTRAINT; Schema: public; Owner: bidradaradmin
--

ALTER TABLE ONLY public.agency_analytics
    ADD CONSTRAINT agency_analytics_agency_code_fkey FOREIGN KEY (agency_code) REFERENCES public.agencies(agency_code) ON DELETE CASCADE;


--
-- Name: award_items award_items_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: bidradaradmin
--

ALTER TABLE ONLY public.award_items
    ADD CONSTRAINT award_items_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(company_id);


--
-- Name: award_items award_items_tender_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: bidradaradmin
--

ALTER TABLE ONLY public.award_items
    ADD CONSTRAINT award_items_tender_id_fkey FOREIGN KEY (tender_id) REFERENCES public.tenders(tender_id) ON DELETE CASCADE;


--
-- Name: company_analytics company_analytics_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: bidradaradmin
--

ALTER TABLE ONLY public.company_analytics
    ADD CONSTRAINT company_analytics_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(company_id) ON DELETE CASCADE;


--
-- Name: tender_bidders tender_bidders_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: bidradaradmin
--

ALTER TABLE ONLY public.tender_bidders
    ADD CONSTRAINT tender_bidders_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(company_id);


--
-- Name: tender_bidders tender_bidders_tender_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: bidradaradmin
--

ALTER TABLE ONLY public.tender_bidders
    ADD CONSTRAINT tender_bidders_tender_id_fkey FOREIGN KEY (tender_id) REFERENCES public.tenders(tender_id) ON DELETE CASCADE;


--
-- Name: tender_committee_members tender_committee_members_member_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: bidradaradmin
--

ALTER TABLE ONLY public.tender_committee_members
    ADD CONSTRAINT tender_committee_members_member_id_fkey FOREIGN KEY (member_id) REFERENCES public.committee_members(member_id);


--
-- Name: tender_committee_members tender_committee_members_tender_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: bidradaradmin
--

ALTER TABLE ONLY public.tender_committee_members
    ADD CONSTRAINT tender_committee_members_tender_id_fkey FOREIGN KEY (tender_id) REFERENCES public.tenders(tender_id) ON DELETE CASCADE;


--
-- Name: tender_history tender_history_tender_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: bidradaradmin
--

ALTER TABLE ONLY public.tender_history
    ADD CONSTRAINT tender_history_tender_id_fkey FOREIGN KEY (tender_id) REFERENCES public.tenders(tender_id) ON DELETE CASCADE;


--
-- Name: tenders tenders_agency_code_fkey; Type: FK CONSTRAINT; Schema: public; Owner: bidradaradmin
--

ALTER TABLE ONLY public.tenders
    ADD CONSTRAINT tenders_agency_code_fkey FOREIGN KEY (agency_code) REFERENCES public.agencies(agency_code);


--
-- PostgreSQL database dump complete
--

