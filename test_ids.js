const { Transformer } = require('./dist/transformer.js');
const data = require('./test-data/sample-data-20240625.json');

const transformer = new Transformer();
const result = transformer.transformToTenderData(data.records[0]);

console.log('=== 修正後的結果 ===');
console.log('tender_id:', result.tender_id);
console.log('project_id:', result.project_id);
console.log('case_number:', result.case_number);
console.log('pcc_main_key:', result.pcc_main_key);
console.log('agency_code:', result.agency_code);
console.log('agency_name:', result.agency_name);
console.log('');
console.log('=== 原始數據比較 ===');
console.log('job_number:', data.records[0].job_number);
console.log('unit_id:', data.records[0].unit_id);
console.log('pkPmsMain:', data.records[0].detail?.pkPmsMain);
console.log('標案案號:', data.records[0].detail?.["採購資料:標案案號"]);

// 測試第二筆記錄
const result2 = transformer.transformToTenderData(data.records[1]);
console.log('');
console.log('=== 第二筆記錄測試 ===');
console.log('tender_id:', result2.tender_id);
console.log('project_id:', result2.project_id);
console.log('case_number:', result2.case_number);
console.log('pcc_main_key:', result2.pcc_main_key);
console.log('agency_code:', result2.agency_code);
console.log('');
console.log('原始 job_number:', data.records[1].job_number);
console.log('原始 unit_id:', data.records[1].unit_id);
console.log('原始 pkPmsMain:', data.records[1].detail?.pkPmsMain); 