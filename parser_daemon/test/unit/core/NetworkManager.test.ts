import { describe, it, expect, jest, beforeEach, afterEach } from "@jest/globals";
import { NetworkManager } from "../../../src/core/NetworkManager";

// Mock fetch globally
const mockFetch = jest.fn() as jest.MockedFunction<typeof fetch>;
global.fetch = mockFetch;

describe("NetworkManager Unit Test", () => {
  let networkManager: NetworkManager;

  beforeEach(() => {
    networkManager = new NetworkManager();
    mockFetch.mockClear();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe("request method test", () => {
    it("should be able to successfully fetch a page", async () => {
      // Mock content needs to be longer than 100 characters to pass validation
      const mockHtml = `<html><head><title>Test Page</title></head><body>
        <h1>Test Content</h1>
        <p>This is a test page with enough content to pass the validation check.</p>
        <div>Additional content to ensure the page is considered valid.</div>
      </body></html>`;

      const mockResponse = {
        ok: true,
        status: 200,
        statusText: "OK",
        text: jest.fn<() => Promise<string>>().mockResolvedValue(mockHtml),
      } as unknown as Response;

      mockFetch.mockResolvedValueOnce(mockResponse);

      const result = await networkManager.request({ url: "https://example.com" });

      expect(result.success).toBe(true);
      expect(result.data).toBe(mockHtml);
      expect(mockFetch).toHaveBeenCalledWith(
        "https://example.com",
        expect.objectContaining({
          method: "GET",
          headers: expect.objectContaining({
            "User-Agent": expect.any(String),
          }),
        })
      );
    });

    it("should correctly handle network errors", async () => {
      const networkError = new Error("Network Error");
      mockFetch.mockRejectedValueOnce(networkError);

      const result = await networkManager.request({ url: "https://invalid-url.com" });

      expect(result.success).toBe(false);
      expect(result.error).toContain("Network Error");
    });
  });

  describe("batchRequest method test", () => {
    it("should correctly handle concurrent requests", async () => {
      // Mock content needs to be longer than 100 characters to pass validation
      const mockHtml = `<html><head><title>Test Page</title></head><body>
        <h1>Test Content</h1>
        <p>This is a test page with enough content to pass the validation check.</p>
        <div>Additional content to ensure the page is considered valid.</div>
      </body></html>`;

      const mockResponse = {
        ok: true,
        status: 200,
        statusText: "OK",
        text: jest.fn<() => Promise<string>>().mockResolvedValue(mockHtml),
      } as unknown as Response;

      mockFetch.mockResolvedValue(mockResponse);

      const urls = [
        "https://example1.com",
        "https://example2.com",
        "https://example3.com",
      ];

      const results = await networkManager.batchRequest(
        urls.map((url) => ({ url })),
        2
      ); // Max concurrency of 2

      expect(results).toHaveLength(3);
      expect(mockFetch).toHaveBeenCalledTimes(3);
      results.forEach((result) => {
        expect(result.success).toBe(true);
      });
    });

    it("should correctly handle partially failed concurrent requests", async () => {
      // Mock content needs to be longer than 100 characters to pass validation
      const mockHtml1 = `<html><head><title>Page 1</title></head><body>
        <h1>Page 1 Content</h1>
        <p>This is page 1 with enough content to pass the validation check.</p>
        <div>Additional content to ensure the page is considered valid.</div>
      </body></html>`;

      const mockHtml3 = `<html><head><title>Page 3</title></head><body>
        <h1>Page 3 Content</h1>
        <p>This is page 3 with enough content to pass the validation check.</p>
        <div>Additional content to ensure the page is considered valid.</div>
      </body></html>`;

      const mockResponse1 = {
        ok: true,
        status: 200,
        statusText: "OK",
        text: jest.fn<() => Promise<string>>().mockResolvedValue(mockHtml1),
      } as unknown as Response;

      const mockResponse3 = {
        ok: true,
        status: 200,
        statusText: "OK",
        text: jest.fn<() => Promise<string>>().mockResolvedValue(mockHtml3),
      } as unknown as Response;

      mockFetch
        .mockResolvedValueOnce(mockResponse1)
        .mockRejectedValueOnce(new Error("page2 failed"))
        .mockResolvedValueOnce(mockResponse3);

      const urls = [
        "https://example1.com",
        "https://example2.com",
        "https://example3.com",
      ];

      const results = await networkManager.batchRequest(
        urls.map((url) => ({ url }))
      );

      expect(results).toHaveLength(3);
      expect(results[0].success).toBe(true);
      expect(results[1].success).toBe(false);
      expect(results[2].success).toBe(true);
    });
  });
});
