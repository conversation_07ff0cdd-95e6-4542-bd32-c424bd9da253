import { NetworkManager } from "../../../src/core/NetworkManager";

describe("Network Fix Tests", () => {
  let networkManager: NetworkManager;

  beforeEach(() => {
    networkManager = new NetworkManager(1);
  });

  describe("Basic Network Connectivity", () => {
    it("should be able to make a simple HTTP request", async () => {
      // 測試基本的 HTTP 請求
      const result = await networkManager.request({
        url: "https://httpbin.org/get",
        method: "GET",
        timeout: 10000,
        retries: 1,
      });

      console.log("Network test result:", {
        success: result.success,
        statusCode: result.statusCode,
        dataLength: result.data?.length,
        error: result.error,
      });

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data).not.toBe("");
    }, 15000);

    it("should be able to access government website", async () => {
      // 測試政府網站連接
      const result = await networkManager.request({
        url: "https://web.pcc.gov.tw/prkms/prms-viewDailyTenderListClient.do?root=tps",
        method: "GET",
        timeout: 15000,
        retries: 1,
      });

      console.log("Government website test result:", {
        success: result.success,
        statusCode: result.statusCode,
        dataLength: result.data?.length,
        error: result.error,
        hasCaptcha: result.hasCaptcha,
      });

      // 即使有驗證碼，也應該能夠獲取到內容
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data).not.toBe("");
    }, 20000);
  });

  describe("Fetch API Diagnostics", () => {
    it("should diagnose fetch API issues", async () => {
      console.log("=== Fetch API Diagnostics ===");
      console.log("typeof fetch:", typeof fetch);
      console.log("fetch available:", typeof fetch === "function");
      
      if (typeof fetch === "function") {
        try {
          console.log("Testing direct fetch call...");
          const response = await fetch("https://httpbin.org/get", {
            method: "GET",
            signal: AbortSignal.timeout(5000),
          });
          
          console.log("Direct fetch result:", {
            response: response,
            responseType: typeof response,
            ok: response?.ok,
            status: response?.status,
          });
          
          if (response) {
            const text = await response.text();
            console.log("Response text length:", text.length);
            expect(response).toBeDefined();
            expect(response.ok).toBe(true);
          } else {
            console.log("❌ Fetch returned undefined - this is the root cause!");
            // 如果 fetch 返回 undefined，我們需要使用替代方案
            expect(response).toBeUndefined();
          }
        } catch (error) {
          console.log("Direct fetch error:", error);
          throw error;
        }
      }
    }, 10000);
  });
});
