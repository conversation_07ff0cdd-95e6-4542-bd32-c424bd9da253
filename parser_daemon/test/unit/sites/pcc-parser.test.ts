import { describe, it, expect, beforeAll } from "@jest/globals";
import { readFileSync } from "fs";
import { join } from "path";
import { PCCParser } from "../../../src/sites/pcc";
import { TenderData } from "../../../../shared/types";

describe("PCCParser TDD Implementation", () => {
  let parser: PCCParser;
  let testHtmlFiles: string[];

  beforeAll(() => {
    parser = new PCCParser();
    
    // 載入測試用的 HTML 檔案
    testHtmlFiles = [
      "detail_pcc_1750922200347_dyyymc.html",
      "detail_pcc_1750922200348_0ywjse.html", 
      "detail_pcc_1750922200348_4bw462.html",
      "detail_pcc_1750922200348_usc75f.html",
      "detail_pcc_1750922200348_yfpyfj.html"
    ];
  });

  describe("parseDetail method", () => {
    it("should parse basic tender information from HTML", async () => {
      const htmlPath = join(__dirname, "../../../temp", testHtmlFiles[0]);
      const html = readFileSync(htmlPath, "utf-8");
      
      const result = await parser.parseDetail(html, "https://test.com");
      
      // 基本結構檢查
      expect(result).toBeDefined();
      expect(typeof result).toBe("object");
      
      // 必要欄位檢查
      expect(result.tender_id).toBeDefined();
      expect(typeof result.tender_id).toBe("string");
      expect(result.tender_id.length).toBeGreaterThan(0);
      
      expect(result.title).toBeDefined();
      expect(typeof result.title).toBe("string");
      expect(result.title.length).toBeGreaterThan(0);
      
      expect(result.agency_name).toBeDefined();
      expect(typeof result.agency_name).toBe("string");
      expect(result.agency_name.length).toBeGreaterThan(0);
    });

    it("should extract budget amount correctly", async () => {
      const htmlPath = join(__dirname, "../../../temp", testHtmlFiles[0]);
      const html = readFileSync(htmlPath, "utf-8");
      
      const result = await parser.parseDetail(html, "https://test.com");
      
      // 預算金額檢查
      expect(result.budget_amount).toBeDefined();
      expect(typeof result.budget_amount).toBe("number");
      expect(result.budget_amount).toBeGreaterThan(0);
    });

    it("should extract agency information correctly", async () => {
      const htmlPath = join(__dirname, "../../../temp", testHtmlFiles[0]);
      const html = readFileSync(htmlPath, "utf-8");
      
      const result = await parser.parseDetail(html, "https://test.com");
      
      // 機關資訊檢查
      expect(result.agency_code).toBeDefined();
      expect(typeof result.agency_code).toBe("string");
      
      expect(result.agency_name).toBeDefined();
      expect(typeof result.agency_name).toBe("string");
      expect(result.agency_name).not.toBe("政府電子採購網");
    });

    it("should set correct tender type", async () => {
      const htmlPath = join(__dirname, "../../../temp", testHtmlFiles[0]);
      const html = readFileSync(htmlPath, "utf-8");
      
      const result = await parser.parseDetail(html, "https://test.com");
      
      // 標案類型檢查
      expect(result.tender_type).toBeDefined();
      expect(["tender", "award", "failure", "amendment", "cancellation"]).toContain(result.tender_type);
    });

    it("should handle multiple test files consistently", async () => {
      const results: TenderData[] = [];
      
      for (const filename of testHtmlFiles.slice(0, 3)) {
        const htmlPath = join(__dirname, "../../../temp", filename);
        const html = readFileSync(htmlPath, "utf-8");
        
        try {
          const result = await parser.parseDetail(html, "https://test.com");
          results.push(result);
        } catch (error) {
          // 記錄但不讓測試失敗，因為有些檔案可能有問題
          console.warn(`Failed to parse ${filename}:`, (error as Error).message);
        }
      }
      
      // 至少應該成功解析一些檔案
      expect(results.length).toBeGreaterThan(0);
      
      // 每個成功解析的結果都應該有基本欄位
      results.forEach((result, index) => {
        expect(result.tender_id).toBeDefined();
        expect(result.title).toBeDefined();
        expect(result.agency_name).toBeDefined();
        expect(result.budget_amount).toBeGreaterThan(0);
      });
    });

    it("should extract publication and deadline dates", async () => {
      const htmlPath = join(__dirname, "../../../temp", testHtmlFiles[0]);
      const html = readFileSync(htmlPath, "utf-8");
      
      const result = await parser.parseDetail(html, "https://test.com");
      
      // 日期欄位檢查
      if (result.announcement_date) {
        expect(result.announcement_date instanceof Date).toBe(true);
      }

      if (result.submission_deadline) {
        expect(result.submission_deadline instanceof Date).toBe(true);
      }
    });

    it("should extract tender category", async () => {
      const htmlPath = join(__dirname, "../../../temp", testHtmlFiles[0]);
      const html = readFileSync(htmlPath, "utf-8");
      
      const result = await parser.parseDetail(html, "https://test.com");
      
      // 標的分類檢查
      if (result.category) {
        expect(typeof result.category).toBe("string");
        expect(result.category.length).toBeGreaterThan(0);
        expect(result.category).not.toBe("");
      }
    });

    it("should extract specific values correctly from test file", async () => {
      const htmlPath = join(__dirname, "../../../temp", testHtmlFiles[0]);
      const html = readFileSync(htmlPath, "utf-8");

      const result = await parser.parseDetail(html, "https://test.com");

      // 檢查具體的提取值
      console.log("Extracted values:");
      console.log("- Title:", result.title);
      console.log("- Tender ID:", result.tender_id);
      console.log("- Agency Name:", result.agency_name);
      console.log("- Budget Amount:", result.budget_amount);
      console.log("- Category:", result.category);
      console.log("- Category Type:", result.category_type);
      console.log("- Announcement Date:", result.announcement_date);
      console.log("- Submission Deadline:", result.submission_deadline);

      // 基於我們看到的 HTML 內容進行驗證
      expect(result.title).toContain("114年度高雄市農地重劃區緊急農水路改善工程");
      expect(result.tender_id).toBe("1140605");
      expect(result.agency_name).toContain("高雄市政府地政局土地開發處");
      expect(result.budget_amount).toBe(40000000);
      expect(result.category).toContain("工程");
      expect(result.category_type).toBe("construction");
      expect(result.announcement_date).toBeInstanceOf(Date);
      expect(result.submission_deadline).toBeInstanceOf(Date);
    });
  });
});
