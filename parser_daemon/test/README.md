# Parser Daemon 測試架構總結

## 📁 測試目錄結構

```
test/
├── config/                     # 測試配置
│   ├── vitest.config.ts        # Vitest 配置
│   └── test-setup.ts           # 測試環境設置
├── unit/                       # 單元測試
│   ├── core/                   # 核心模組測試
│   │   ├── NetworkManager.test.ts
│   │   ├── FileManager.test.ts
│   │   ├── CaptchaHandler.test.ts
│   │   ├── Parser.test.ts
│   │   └── utils.test.ts
│   ├── sites/                  # 站點解析器測試
│   │   └── pcc/
│   │       ├── detail.test.ts
│   │       ├── list.test.ts
│   │       └── index.test.ts
│   └── types/                  # 類型系統測試
│       └── TenderData.test.ts  ✅
├── integration/                # 集成測試
│   ├── parser-workflow.test.ts
│   ├── real-url.test.ts       ✅
│   └── end-to-end.test.ts
├── fixtures/                   # 測試數據
│   ├── html/                   # HTML 樣本
│   ├── json/                   # JSON 樣本
│   └── mock-responses/         # Mock 響應
└── helpers/                    # 測試工具
    ├── test-utils.ts          ✅
    └── mock-server.ts         ✅
```

## ✅ 已完成的測試

### 1. TenderData 類型系統測試 (15 個測試通過)

- ✅ 枚舉值驗證
- ✅ 狀態映射測試
- ✅ 資料驗證函數測試
- ✅ 不同標案類型的結構驗證
- ✅ 特殊字符欄位測試
- ✅ 資料標準化測試

### 2. 真實 URL 集成測試

- ✅ 架構設計完成，包含招標/決標/流標頁面測試
- ✅ 網路錯誤處理測試
- ✅ 資料完整性驗證測試

### 3. 測試工具和輔助函數

- ✅ TestDataGenerator: 測試資料生成器
- ✅ FixturesLoader: 檔案載入工具
- ✅ TestAssertions: 測試斷言函數
- ✅ TimeUtils: 時間工具函數

## 📋 測試配置

### 依賴管理

- ✅ 使用 pnpm 代替 bun
- ✅ Vitest 作為測試框架
- ✅ MSW 用於 API 模擬
- ✅ Axios 網路請求測試

### 配置設置

- ✅ TypeScript 支持
- ✅ 路徑別名設置 (@, @test)
- ✅ 測試覆蓋率配置 (75% 目標)
- ✅ 30 秒測試超時（適合網路請求）

## 🎯 真實 URL 測試目標

基於提供的 URL，測試架構支持：

### 招標頁面

```
https://web.pcc.gov.tw/tps/QueryTender/query/searchTenderDetail?pkPmsMain=NzA5Mzg4Mjg=
```

- 機關資料解析
- 採購資料驗證
- 招標資料驗證
- 領投開標資訊驗證

### 決標頁面（預期）

```
https://web.pcc.gov.tw/tps/atm/AtmAwardWithoutSso/QueryAtmAwardDetail?pkAtmMain=xxx
```

- 決標資料解析
- 得標廠商資訊
- 決標金額驗證

### 流標頁面（預期）

```
https://web.pcc.gov.tw/tps/atm/AtmNonAwardWithoutSso/query/showAtmNonAwardHistory?fkPmsMainHist=xxx
```

- 流標原因解析
- 無法決標資訊

## 🔧 測試執行指令

```bash
# 執行所有測試
pnpm test

# 執行單元測試
pnpm test:unit

# 執行集成測試
pnpm test:integration

# 監視模式
pnpm test:watch

# 生成覆蓋率報告
pnpm test:coverage

# 類型檢查
pnpm type-check
```

## 📊 當前測試狀態

- ✅ **類型系統測試**: 15/15 通過
- ⚠️ **NetworkManager 測試**: 0/13 通過（需要根據實際 API 調整）
- 📝 **真實 URL 測試**: 已架構完成，待實際測試
- 📋 **其他核心模組**: 待實作

## 🚧 下一步工作

1. **修正 NetworkManager 測試**

   - 根據實際 NetworkManager API 調整測試用例
   - 確保 mock 設置正確

2. **完成核心模組測試**

   - FileManager 測試
   - Parser 測試
   - CaptchaHandler 測試

3. **站點解析器測試**

   - PCC 解析器單元測試
   - 各類頁面解析測試

4. **端到端測試**
   - 完整工作流程測試
   - 性能測試

## ⚡ 測試特色

### 繁體中文支持

- 所有測試描述使用繁體中文
- 錯誤信息本地化
- 中文資料結構測試

### 網路友好設計

- 真實 URL 測試支援網路不可達時跳過
- 合理的超時設定
- 重試機制測試

### 覆蓋率目標

- 語句覆蓋率: 75%
- 分支覆蓋率: 65%
- 函數覆蓋率: 75%
- 行覆蓋率: 75%

## 📈 測試品質指標

1. **完整性**: 涵蓋招標、決標、流標三種狀態
2. **可靠性**: 支援網路錯誤和異常處理
3. **實用性**: 使用真實 URL 進行測試
4. **維護性**: 清晰的測試結構和工具函數
5. **效能**: 30 秒超時設定適合網路請求
