import { beforeAll, afterAll, beforeEach, afterEach, jest } from "@jest/globals";

// 設置全域測試環境
beforeAll(() => {
  // 設置測試環境變數
  process.env.NODE_ENV = "test";
  process.env.LOG_LEVEL = "error";

  // 模擬全域對象
  if (typeof global.fetch === "undefined") {
    global.fetch = jest.fn();
  }
});

afterAll(() => {
  // 清理
});

beforeEach(() => {
  // 每個測試前的設置
});

afterEach(() => {
  // 清理測試副作用
  jest.clearAllMocks();
  jest.restoreAllMocks();
});
