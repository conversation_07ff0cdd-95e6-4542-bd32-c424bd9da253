import { setupServer } from "msw/node";
import { http, HttpResponse } from "msw";

// 定義 Mock API 處理程序
const handlers = [
  // Mock PCC 網站的招標頁面
  http.get(
    "https://web.pcc.gov.tw/tps/QueryTender/query/searchTenderDetail",
    () => {
      return HttpResponse.html(`
      <html>
        <head><title>招標詳情測試頁面</title></head>
        <body>
          <div class="tender-detail">招標測試內容</div>
        </body>
      </html>
    `);
    }
  ),

  // Mock PCC 網站的決標頁面
  http.get(
    "https://web.pcc.gov.tw/tps/atm/AtmAwardWithoutSso/QueryAtmAwardDetail",
    () => {
      return HttpResponse.html(`
      <html>
        <head><title>決標詳情測試頁面</title></head>
        <body>
          <div class="award-detail">決標測試內容</div>
        </body>
      </html>
    `);
    }
  ),

  // Mock PCC 網站的流標頁面
  http.get(
    "https://web.pcc.gov.tw/tps/atm/AtmNonAwardWithoutSso/query/showAtmNonAwardHistory",
    () => {
      return HttpResponse.html(`
      <html>
        <head><title>流標詳情測試頁面</title></head>
        <body>
          <div class="non-award-detail">流標測試內容</div>
        </body>
      </html>
    `);
    }
  ),

  // Mock 通用網路錯誤
  http.get("https://error.test.com/*", () => {
    return HttpResponse.error();
  }),
];

// 創建 Mock 服務器
export const server = setupServer(...handlers);
