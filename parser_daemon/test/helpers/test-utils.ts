import { readFile } from "fs/promises";
import { join } from "path";
import { expect } from "@jest/globals";
import { TenderData } from "@shared/types";

// Test data generator
export class TestDataGenerator {
  /**
   * Generates basic tender data.
   */
  static generateBasicTenderData(
    overrides: Partial<TenderData> = {}
  ): TenderData {
    const baseData: TenderData = {
      id: "TEST-001",
      title: "Test Tender",
      agency: "Test Agency",
      budget: 1000000,
      deadline: "2024-12-31",
      publishDate: "2024-01-01",
      projectId: "PRJ-001",
      status: "bidding",
      category: "Construction",
      location: "Taipei City",
      description: "Test tender description",
      aiScore: 85,
      tenderType: "tender",
      agencyInfo: {
        agencyCode: "A.19.6",
        agencyName: "Test Agency",
        unitName: "Test Unit",
        agencyAddress: "123 Test Rd, Taipei City",
        contactPerson: "Test Contact",
        contactPhone: "02-12345678",
        faxNumber: "02-87654321",
        email: "<EMAIL>",
      },
      ...overrides,
    };

    return baseData;
  }

  /**
   * Generates tender data in bidding status.
   */
  static generateBiddingTenderData(
    overrides: Partial<TenderData> = {}
  ): TenderData {
    return this.generateBasicTenderData({
      tenderType: "tender",
      status: "bidding",
      tenderInfo: {
        tenderMethod: "Open Tender",
        awardMethod: "Lowest Bid",
        announcementCount: 1,
        announcementDate: "2024-01-01",
        hasReservePrice: true,
      },
      biddingInfo: {
        providesElectronicDocuments: true,
        providesOnsiteDocuments: false,
        biddingDeadline: "2024-02-01 17:00",
        documentSubmissionLocation: "123 Test Rd, Taipei City",
        openingTime: "2024-02-02 10:00",
        openingLocation: "Meeting Room, 123 Test Rd, Taipei City",
        requiresBidBond: true,
      },
      ...overrides,
    });
  }

  /**
   * Generates tender data in awarded status.
   */
  static generateAwardedTenderData(
    overrides: Partial<TenderData> = {}
  ): TenderData {
    return this.generateBasicTenderData({
      tenderType: "award",
      status: "awarded",
      awardInfo: {
        awardDate: "2024-02-10",
        awardAnnouncementDate: "2024-02-10",
        totalAwardAmount: 950000,
        bidderCount: 3,
        bidders: [
          {
            vendorCode: "12345678",
            vendorName: "Test Winning Vendor",
            isWinner: true,
            awardAmount: 950000,
            isSmallMediumEnterprise: true,
          },
        ],
      },
      ...overrides,
    });
  }

  /**
   * Generates tender data in failed status.
   */
  static generateFailedTenderData(
    overrides: Partial<TenderData> = {}
  ): TenderData {
    return this.generateBasicTenderData({
      tenderType: "failure",
      status: "failed",
      failedTenderInfo: {
        failureAnnouncementDate: "2024-02-15",
        failureReason: "No vendors submitted bids",
        continueWithSameCaseNumber: true,
      },
      ...overrides,
    });
  }
}

// Fixture loader utility
export class FixturesLoader {
  private static readonly FIXTURES_DIR = join(__dirname, "../fixtures");

  /**
   * Loads an HTML fixture file.
   */
  static async loadHtml(filename: string): Promise<string> {
    const filePath = join(this.FIXTURES_DIR, "html", filename);
    return await readFile(filePath, "utf-8");
  }

  /**
   * Loads a JSON fixture file.
   */
  static async loadJson<T = any>(filename: string): Promise<T> {
    const filePath = join(this.FIXTURES_DIR, "json", filename);
    const content = await readFile(filePath, "utf-8");
    return JSON.parse(content);
  }

  /**
   * Loads a mock response file.
   */
  static async loadMockResponse(filename: string): Promise<string> {
    const filePath = join(this.FIXTURES_DIR, "mock-responses", filename);
    return await readFile(filePath, "utf-8");
  }
}

// Test assertion helpers
export class TestAssertions {
  /**
   * Asserts the basic structure of tender data.
   */
  static assertTenderDataStructure(data: any) {
    expect(data).toBeDefined();
    expect(data.id).toBeDefined();
    expect(data.title).toBeDefined();
    expect(data.agencyInfo).toBeDefined();
    expect(data.tenderType).toMatch(/^(tender|award|failure)$/);
  }

  /**
   * Asserts the structure of bidding tender data.
   */
  static assertBiddingTenderData(data: TenderData) {
    this.assertTenderDataStructure(data);
    expect(data.tenderType).toBe("tender");
    expect(data.tenderInfo).toBeDefined();
    expect(data.biddingInfo).toBeDefined();
  }

  /**
   * Asserts the structure of awarded tender data.
   */
  static assertAwardedTenderData(data: TenderData) {
    this.assertTenderDataStructure(data);
    expect(data.tenderType).toBe("award");
    expect(data.awardInfo).toBeDefined();
  }

  /**
   * Asserts the structure of failed tender data.
   */
  static assertFailedTenderData(data: TenderData) {
    this.assertTenderDataStructure(data);
    expect(data.tenderType).toBe("failure");
    expect(data.failedTenderInfo).toBeDefined();
  }
}

// Time utilities
export class TimeUtils {
  /**
   * Creates a delay for testing.
   */
  static delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Creates a formatted date string for testing.
   */
  static formatTestDate(date: Date = new Date()): string {
    return date.toISOString().split("T")[0];
  }
}
