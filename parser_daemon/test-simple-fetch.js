// 簡單測試 fetch 功能
console.log("=== 測試 fetch 功能 ===");
console.log("Node.js 版本:", process.version);
console.log("typeof fetch:", typeof fetch);

async function testFetch() {
  try {
    console.log("\n1. 測試 Google...");
    const response1 = await fetch("https://www.google.com");
    console.log("Google 結果:", {
      response: !!response1,
      ok: response1?.ok,
      status: response1?.status,
      statusText: response1?.statusText
    });

    console.log("\n2. 測試 httpbin...");
    const response2 = await fetch("https://httpbin.org/get");
    console.log("httpbin 結果:", {
      response: !!response2,
      ok: response2?.ok,
      status: response2?.status,
      statusText: response2?.statusText
    });

    console.log("\n3. 測試政府網站...");
    const response3 = await fetch("https://web.pcc.gov.tw/prkms/prms-viewDailyTenderListClient.do?root=tps");
    console.log("政府網站結果:", {
      response: !!response3,
      ok: response3?.ok,
      status: response3?.status,
      statusText: response3?.statusText
    });

    if (response3) {
      const text = await response3.text();
      console.log("政府網站內容長度:", text.length);
      console.log("內容預覽:", text.substring(0, 200));
    }

  } catch (error) {
    console.error("測試失敗:", error);
  }
}

testFetch();
