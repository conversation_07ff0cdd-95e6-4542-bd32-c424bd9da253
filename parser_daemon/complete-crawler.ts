import { PCCParser } from "./src/sites/pcc";
import { FileManager } from "./src/core/FileManager";
import { TenderData, ListItem } from "./src/types";

const BATCH_SIZE = 10; // 每批處理的標案數量
const DELAY_BETWEEN_REQUESTS = 2000; // 請求間隔 (毫秒)
const DELAY_BETWEEN_BATCHES = 5000; // 批次間隔 (毫秒)

async function completeCrawler() {
  console.log("🕷️ 開始完整爬取流程...");

  const parser = new PCCParser();

  try {
    // 第一步：讀取已解析的 list items
    console.log("📄 第一步：讀取標案列表...");

    const listData = await FileManager.readFile("parsed/114-06-26.json");
    const parsedData = JSON.parse(listData);
    const items: ListItem[] = parsedData.items;

    console.log(`📋 找到 ${items.length} 個標案項目`);

    if (items.length === 0) {
      console.log("⚠️ 沒有找到標案項目");
      return;
    }

    // 第二步：批量爬取詳細頁面
    console.log(
      `🔍 第二步：批量爬取詳細頁面 (${items.length} 個，每批 ${BATCH_SIZE} 個)...`
    );

    const allTenders: TenderData[] = [];
    const errors: string[] = [];

    for (let i = 0; i < items.length; i += BATCH_SIZE) {
      const batch = items.slice(i, i + BATCH_SIZE);
      const batchNumber = Math.floor(i / BATCH_SIZE) + 1;
      const totalBatches = Math.ceil(items.length / BATCH_SIZE);

      console.log(
        `\n📦 處理批次 ${batchNumber}/${totalBatches} (${batch.length} 個項目)`
      );

      // 處理此批次的所有項目
      for (let j = 0; j < batch.length; j++) {
        const item = batch[j];
        const itemIndex = i + j + 1;

        try {
          console.log(
            `📄 [${itemIndex}/${items.length}] 爬取: ${item.title.substring(
              0,
              50
            )}...`
          );

          // 檢查 URL 是否是 redirectPublic 類型，如果是則需要實際訪問獲得真正的詳細頁面 URL
          let detailUrl = item.url;

          if (item.url.includes("redirectPublic")) {
            console.log(`🔗 [${itemIndex}] 正在解析重定向 URL...`);

            // 訪問 redirectPublic URL 獲得真正的詳細頁面
            const redirectResult = await parser["networkManager"].request({
              url: item.url,
              useProxy: false,
              retries: 3,
            });

            if (redirectResult.success) {
              // 檢查是否有重定向
              const html = redirectResult.data!;

              // 尋找頁面中的實際詳細頁面連結
              // 政府電子採購網可能會用 JavaScript 重定向或提供實際連結
              const realUrl = extractRealDetailUrl(html, item.url);
              if (realUrl) {
                detailUrl = realUrl;
                console.log(
                  `✅ [${itemIndex}] 找到真實 URL: ${realUrl.substring(
                    0,
                    80
                  )}...`
                );
              }
            }
          }

          // 爬取詳細頁面
          const detailResult = await parser["networkManager"].request({
            url: detailUrl,
            useProxy: false,
            retries: 3,
          });

          if (detailResult.success) {
            const detailHtml = detailResult.data!;

            // 解析詳細頁面
            try {
              const tenderData = await parser.parseDetail(
                detailHtml,
                detailUrl
              );

              // 補充 list 中的基本資訊
              tenderData.id = item.id;
              tenderData.title = item.title || tenderData.title;
              tenderData.agency = item.agency || tenderData.agency;

              allTenders.push(tenderData);
              console.log(
                `✅ [${itemIndex}] 成功: 預算 ${tenderData.budget.toLocaleString()} 元`
              );
            } catch (parseError) {
              console.log(`⚠️ [${itemIndex}] 解析失敗: ${parseError}`);
              errors.push(`${item.id}: 解析失敗 - ${parseError}`);
            }
          } else {
            console.log(`❌ [${itemIndex}] 爬取失敗: ${detailResult.error}`);
            errors.push(`${item.id}: 爬取失敗 - ${detailResult.error}`);
          }

          // 延遲避免被封
          if (j < batch.length - 1) {
            await new Promise((resolve) =>
              setTimeout(resolve, DELAY_BETWEEN_REQUESTS)
            );
          }
        } catch (error) {
          console.log(`❌ [${itemIndex}] 處理失敗: ${error}`);
          errors.push(`${item.id}: 處理失敗 - ${error}`);
        }
      }

      // 批次間延遲
      if (i + BATCH_SIZE < items.length) {
        console.log(`⏱️ 批次完成，等待 ${DELAY_BETWEEN_BATCHES / 1000} 秒...`);
        await new Promise((resolve) =>
          setTimeout(resolve, DELAY_BETWEEN_BATCHES)
        );
      }
    }

    // 第三步：保存完整資料
    console.log("\n💾 第三步：保存完整資料...");

    const completeData = {
      sourceFile: "list/114-06-26.html.gz + details",
      processedAt: new Date().toISOString(),
      itemCount: items.length,
      tenderCount: allTenders.length,
      successRate: `${((allTenders.length / items.length) * 100).toFixed(1)}%`,
      items: items,
      tenders: allTenders,
      errors: errors,
    };

    await FileManager.writeJsonFile(
      "parsed/114-06-26-complete.json",
      completeData
    );

    console.log("\n🎯 完整爬取流程完成！");
    console.log(`📊 統計資料:`);
    console.log(`   總標案數: ${items.length}`);
    console.log(`   成功爬取: ${allTenders.length}`);
    console.log(`   失敗數量: ${errors.length}`);
    console.log(
      `   成功率: ${((allTenders.length / items.length) * 100).toFixed(1)}%`
    );
    console.log(`   輸出檔案: parsed/114-06-26-complete.json`);

    if (errors.length > 0) {
      console.log(`\n❌ 錯誤列表 (前10個):`);
      errors.slice(0, 10).forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`);
      });
    }
  } catch (error) {
    console.error(`❌ 爬取失敗: ${error}`);
  }
}

// 從重定向頁面提取真正的詳細頁面 URL
function extractRealDetailUrl(
  html: string,
  originalUrl: string
): string | null {
  // 檢查是否包含 JavaScript 重定向
  const jsRedirectMatch = html.match(
    /window\.location\.href\s*=\s*['"]([^'"]+)['"]/
  );
  if (jsRedirectMatch) {
    return jsRedirectMatch[1];
  }

  // 檢查是否包含 meta refresh
  const metaRefreshMatch = html.match(
    /<meta[^>]+http-equiv\s*=\s*['"]refresh['"][^>]+content\s*=\s*['"][^;]+;\s*url=([^'"]+)['"]/i
  );
  if (metaRefreshMatch) {
    return metaRefreshMatch[1];
  }

  // 檢查是否包含詳細頁面的直接連結
  const detailLinkMatch =
    html.match(
      /href\s*=\s*['"]([^'"]*(?:searchTenderDetail|QueryAtmAwardDetail|showAtmNonAwardHistory)[^'"]*)['"]/
    ) ||
    html.match(/href\s*=\s*['"]([^'"]*pkPmsMain[^'"]*)['"]/) ||
    html.match(/href\s*=\s*['"]([^'"]*pkAtmMain[^'"]*)['"]/) ||
    html.match(/href\s*=\s*['"]([^'"]*fkPmsMainHist[^'"]*)['"]/);

  if (detailLinkMatch) {
    let url = detailLinkMatch[1];
    // 如果是相對 URL，加上 base URL
    if (url.startsWith("/")) {
      url = "https://web.pcc.gov.tw" + url;
    }
    return url;
  }

  return null;
}

completeCrawler();
