<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">





<html lang="zh-tw" xml:lang="zh-tw">
<head>
<meta charset="UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta property="og:image" content="/pis/images/icon.gif" />
<title>政府電子採購網</title>
<link rel="icon" href="/pis/images/icon.gif">

<!-- 首頁第二版練習區滑鼠跟隨游標CSS設定及是否「練習區」-->


<!-- 首頁第二版練習區滑鼠跟隨游標CSS設定及是否「練習區」-->

<style>
input:focus {
    color: #495057;
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgb(0 123 255 / 25%);
}

textarea:focus {
    color: #495057;
    background-color: #fff;
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgb(0 123 255 / 25%);
}

select:focus-visible {
    color: #495057;
    background-color: #fff;
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgb(0 123 255 / 25%);
}

</style>




<link href="/pis/css/v2/bootstrap.css" rel="stylesheet" type="text/css" />
<link href="/pis/css/v2/font-awesome/css/font-awesome.css" rel="stylesheet"/>
<link href="/pis/css/v2/navbar.css" rel="stylesheet" type="text/css" />
<link href="/pis/css/v2/style.css" rel="stylesheet" type="text/css" />
<!--首頁最上方下拉選單-->
<link href="/pis/css/v2/index_tab.css" type="text/css" rel="stylesheet" /> 




<!--日期 -->
<script src="/pis/js/calendar.js"></script>
<!--函式庫 -->
<script src="/pis/js/jquery-3.5.0.min.js"></script>
<!--頁簽 -->
<script src="/pis/js/tab.js"></script>
<!--下拉選單 -->
<script src="/pis/js/menu.js" type="text/javascript"></script>
<script src="/pis/js/jquery.blockUI.js"></script>
<script src="/pis/js/jquery.validate.min.js" type="text/javascript"></script>
<script src="/pis/js/additional-methods.js" type="text/javascript"></script>
<script src="/pis/js/v2/index_tab.js" type="text/javascript"></script>
<!--首頁最上方下拉選單-->
<script src="/pis/js/v2/bootstrap.js" type="text/javascript"></script>




	<script src="/pis/scripts/prkms.js" type="text/javascript"></script>




<meta name="google-site-verification" content="_RsXySMeLvSPD4xblNnXL5YTiToz0qucp2NflC2YnO8" />
</head>

<body>
	<!------------------ 桌面版導覽列 1-------------------->
	


<noscript>
<td style="color:red;">
您的瀏覽器不支援JavaScript功能，若網頁功能無法正常使用時，請開啟瀏覽器JavaScript狀態
</td>
</noscript>
<style>
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only-focusable:active, .sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  overflow: visible;
  clip: auto;
  white-space: normal;
}
</style>
<!------------------ 桌面版導覽列 1-------------------->
<div class="nav100 d-md-none d-sm-none d-none d-lg-block">
	<div class="container">
		<div class="top_a_r">
			<div class="top_a_r_1">
				<a href="https://web.pcc.gov.tw/pis/" title="前往正式區的按鈕">
					<img src="/pis/images/v2/r_btn1.png" alt="前往正式區的按鈕">
				</a>
			</div>
			<div class="top_a_r_2">
				<a href="https://webtest.pcc.gov.tw/pis/" title="前往練習區的按鈕">
					<img src="/pis/images/v2/r_btn2.png" alt="前往練習區的按鈕">
				</a>
			</div>
		</div>

		<div class="nav-header">
			<div class="header-left">
				<div class="t_btn1">
					<a href="#content" tabindex="5" class="sr-only sr-only-focusable"><span style="color:#FFFFFF;background-color:#81b1c9;margin-right:5px;Opacity:80;">中央內容區塊</span></a>
				
					<a href="#U" id="AU" name="U" title="上方功能區塊" class="guide" accesskey="U" tabindex="0">:::</a>
					<a  href="/pis/" title="回首頁">回首頁</a>
					<a  href="/pis/prac/sitemapClient/readG3Sitemap" title="網站導覽">網站導覽</a>
	        		<a  href="/qdcs/webmobile/Home" title="行動版">行動版</a>
					<a  href="/tps/QueryTender/query/queryEng?mode=" title="ENGLISH">ENGLISH</a>
					<a  href="/pis/pia/client/mail" title="意見信箱">意見信箱</a>
<!-- 					<a  href="/csci/pf/feedback" title="試辦問題回報">試辦問題回報</a> -->
				</div>
			</div>
			<div class="header-right">
				<div class="t_btn2"></div>
			</div>
		</div>
	</div>
</div>

<div class="pc">
	<div class="inbanner">
		<div class="container">
			<h1>政府電子採購網</h1>
		</div>
	</div>
</div>

<div class="mobile">
	<div class="top_a_r">
		<div class="top_a_r_1">
			<a href="https://web.pcc.gov.tw/pis/" title="前往正式區的按鈕">
				<img src="/pis/images/v2/r_btn1.png" alt="前往正式區的按鈕">
			</a>
		</div>
		<div class="top_a_r_2">
			<a href="https://webtest.pcc.gov.tw/pis/" title="前往練習區的按鈕">
				<img src="/pis/images/v2/r_btn2.png" alt="前往練習區的按鈕">
			</a>
		</div>
	</div>
	<div>政府電子採購網</div>
	<div class="t_btn1">
		<a href="#content" tabindex="5" class="sr-only sr-only-focusable"><span style="color:#FFFFFF;background-color:#81b1c9;margin-right:5px;Opacity:80;">中央內容區塊</span></a>
	
		<a href="#U" id="AU" name="U" title="上方功能區塊" class="guide" accesskey="U" tabindex="0">:::</a>
		<a  href="/pis/" title="回首頁">回首頁</a>
		<a  href="/pis/prac/sitemapClient/readG3Sitemap" title="網站導覽">網站導覽</a>
		<a  href="/qdcs/webmobile/Home" title="行動版">行動版</a>
		<a  href="/tps/QueryTender/query/queryEng?mode=" title="ENGLISH">ENGLISH</a>
		<a  href="/pis/pia/client/mail" title="意見信箱">意見信箱</a>
<!-- 		<a  href="/csci/pf/feedback" title="試辦問題回報">試辦問題回報</a> -->
	</div>
</div>
<!-------------------NAVBAR----------------->
	<header id="header" class="header-2">
		





<!--BANNER--> <div class="pc"> 	<div class="top_c"> 		<div class="top_ca"> 			<div class="nav"> 				<ul class="menu">	 										 			    		<!--第一階選項 --> 						<li class="li_tp"> 							<a href="#" class="bind_menu_a" data-layerid="layer1">網站導覽</a> 						</li> 						<!--第一階選項結束 --> 					 						<div class="sub_menu bind_menu_a" id="layer1" data-layerid="layer1" style="display: none;"> 							<i></i> 							<!--第二階選項 --> 							<div class="sub_area clearfix"> 							    			<ul class="sub_nav list"> 													<li class="li_tp tm"> 														<a class="tmc">網站導覽</a> 														 														<!--第三階選項 --> 														<ul class="sub02"> 																		<li class="tpb_a"> 																		 																				<a href="/pis/prac/sitemapClient/readG3Sitemap" class="tmd" title="前往網站導覽(三代)">網站導覽(三代)</a> 																		 																		</li> 																		<li class="tpb_a"> 																		 																				<a href="/pis/prac/sitemapClient/readG2Sitemap" class="tmd" title="前往網站導覽(二代)">網站導覽(二代)</a> 																		 																		</li> 														</ul> 														<!--第三階選項結束 --> 													</li> 													 							    			</ul> 							</div> 							<!--第二階選項結束 --> 						</div> 					 			    		<!--第一階選項 --> 						<li class="li_tp"> 							<a href="#" class="bind_menu_a" data-layerid="layer2">學習資源</a> 						</li> 						<!--第一階選項結束 --> 					 						<div class="sub_menu bind_menu_a" id="layer2" data-layerid="layer2" style="display: none;"> 							<i></i> 							<!--第二階選項 --> 							<div class="sub_area clearfix"> 							    			<ul class="sub_nav list"> 													<li class="li_tp tm"> 														<a class="tmc">教育訓練</a> 														 														<!--第三階選項 --> 														<ul class="sub02"> 																		<li class="tpb_a"> 																		 																				<a href="/ol/entityCourse/index" class="tmd" title="前往機關端(線上報名)">機關端(線上報名)</a> 																		 																		</li> 																		<li class="tpb_a"> 																		 																				<a href="/ol/trgEnroll/index" class="tmd" title="前往廠商端(線上報名)">廠商端(線上報名)</a> 																		 																		</li> 														</ul> 														<!--第三階選項結束 --> 													</li> 													 							    			</ul> 							    			<ul class="sub_nav list"> 													<li class="li_tp tm"> 														<a class="tmc">線上學習</a> 														 														<!--第三階選項 --> 														<ul class="sub02"> 																		<li class="tpb_a"> 																		 																				<a href="/qdcs/trainingIndex" class="tmd" title="前往線上教學">線上教學</a> 																		 																		</li> 														</ul> 														<!--第三階選項結束 --> 													</li> 													 							    			</ul> 							    			<ul class="sub_nav list"> 													<li class="li_tp tm"> 														<a class="tmc">教學資料(下載專區)</a> 														 														<!--第三階選項 --> 														<ul class="sub02"> 																		<li class="tpb_a"> 																		 																				<a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=20000003" class="tmd" title="前往系統使用手冊">系統使用手冊</a> 																		 																		</li> 																		<li class="tpb_a"> 																		 																				<a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=60000731" class="tmd" title="前往三代採購網教育訓練教材">三代採購網教育訓練教材</a> 																		 																		</li> 																		<li class="tpb_a"> 																		 																				<a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=60000736" class="tmd" title="前往三代教學影片">三代教學影片</a> 																		 																		</li> 																		<li class="tpb_a"> 																		 																				<a href="/pis/prac/downloadGroupClient/getClientDownloadForDownloadFile?id=70000016" class="tmd" title="下載環境檢測安裝步驟及FAQ_v2.0_正式版.pdf">環境檢測安裝步驟及FAQ</a> 																		 																		</li> 																		<li class="tpb_a"> 																		 																				<a href="/pis/prac/downloadGroupClient/getClientDownloadForDownloadFile?id=70000015" class="tmd" title="下載「環境檢測招領投開用戶端程式偵測失敗」障礙排除說明.pdf">用戶端程式偵測障礙排除</a> 																		 																		</li> 																		<li class="tpb_a"> 																		 																				<a href="/pis/prac/downloadGroupClient/readPreparation" class="tmd" title="前往第一次使用三代政府電子採購網之前置作業">第一次使用三代政府電子採購網之前置作業</a> 																		 																		</li> 														</ul> 														<!--第三階選項結束 --> 													</li> 													 							    			</ul> 							    			<ul class="sub_nav list"> 													<li class="li_tp tm"> 														<a class="tmc">採購專業人員訓練</a> 														 														<!--第三階選項 --> 														<ul class="sub02"> 																		<li class="tpb_a"> 																		 																				<a href="/psms/pptom/pptomPublic/indexRead" class="tmd" title="前往代訓機構名單">代訓機構名單</a> 																		 																		</li> 																		<li class="tpb_a"> 																		 																				<a href="/psms/pptcm/pptcmPublic/indexReadPptcm" class="tmd" title="前往代訓機構開課">代訓機構開課</a> 																		 																		</li> 																		<li class="tpb_a"> 																		 																				<a href="/psms/plrtqdm/questionPublic/indexReadQuestion" class="tmd" title="前往採購法規題庫">採購法規題庫</a> 																		 																		</li> 																		<li class="tpb_a"> 																		 																				<a href="/psms/plrtm/plrtmQuery/indexReadCertificate" class="tmd" title="前往採購專業人員及格證書查詢">採購專業人員及格證書查詢</a> 																		 																		</li> 																		<li class="tpb_a"> 																		 																				<a href="/psms/plrtm/plrtmReissue/certificateReissue" class="tmd" title="前往採購專業人員及格證書補發申請">採購專業人員及格證書補發申請</a> 																		 																		</li> 														</ul> 														<!--第三階選項結束 --> 													</li> 													 							    			</ul> 							</div> 							<!--第二階選項結束 --> 						</div> 					 			    		<!--第一階選項 --> 						<li class="li_tp"> 							<a href="#" class="bind_menu_a" data-layerid="layer3">採購作業</a> 						</li> 						<!--第一階選項結束 --> 					 						<div class="sub_menu bind_menu_a" id="layer3" data-layerid="layer3" style="display: none;"> 							<i></i> 							<!--第二階選項 --> 							<div class="sub_area clearfix"> 							    			<ul class="sub_nav list"> 													<li class="li_tp tm"> 														<a class="tmc">標案查詢</a> 														 														<!--第三階選項 --> 														<ul class="sub02"> 																		<li class="tpb_a"> 																		 																				<a href="#" onclick="goTender()" class="tmd" title="前往標案查詢">標案查詢</a> 																			 																		</li> 														</ul> 														<!--第三階選項結束 --> 													</li> 													 							    			</ul> 							    			<ul class="sub_nav list"> 													<li class="li_tp tm"> 														<a class="tmc">等標期</a> 														 														<!--第三階選項 --> 														<ul class="sub02"> 																		<li class="tpb_a"> 																		 																				<a href="/pis/prac/declarationClient/bWait" class="tmd" title="前往等標期">等標期</a> 																		 																		</li> 														</ul> 														<!--第三階選項結束 --> 													</li> 													 							    			</ul> 							    			<ul class="sub_nav list"> 													<li class="li_tp tm"> 														<a class="tmc">電子公報</a> 														 														<!--第三階選項 --> 														<ul class="sub02"> 																		<li class="tpb_a"> 																		 																				<a href="/ebm/public/EbmMain/indexEbmMain" class="tmd" title="前往電子公報">電子公報</a> 																		 																		</li> 														</ul> 														<!--第三階選項結束 --> 													</li> 													 							    			</ul> 							</div> 							<!--第二階選項結束 --> 						</div> 					 			    		<!--第一階選項 --> 						<li class="li_tp"> 							<a href="#" class="bind_menu_a" data-layerid="layer4">查詢服務</a> 						</li> 						<!--第一階選項結束 --> 					 						<div class="sub_menu bind_menu_a" id="layer4" data-layerid="layer4" style="display: none;"> 							<i></i> 							<!--第二階選項 --> 							<div class="sub_area clearfix"> 							    			<ul class="sub_nav list"> 													<li class="li_tp tm"> 														<a class="tmc">標案相關</a> 														 														<!--第三階選項 --> 														<ul class="sub02"> 																		<li class="tpb_a"> 																		 																				<a href="#" onclick="goTender()" class="tmd" title="前往標案查詢">標案查詢</a> 																			 																		</li> 																		<li class="tpb_a"> 																		 																				<a href="/prkms/gpaPredict/common/indexGpaPredict" class="tmd" title="前往採購預告查詢">採購預告查詢</a> 																		 																		</li> 																		<li class="tpb_a"> 																		 																				<a href="/prkms/tpRead/common/indexTpRead" class="tmd" title="前往公開閱覽查詢">公開閱覽查詢</a> 																		 																		</li> 																		<li class="tpb_a"> 																		 																				<a href="/prkms/tpAppeal/common/indexTpAppeal" class="tmd" title="前往公開徵求查詢">公開徵求查詢</a> 																		 																		</li> 																		<li class="tpb_a"> 																		 																				<a href="/opas/pspad/pspadClient/readPublicPspadListInit" class="tmd" title="前往公示送達查詢">公示送達查詢</a> 																		 																		</li> 																		<li class="tpb_a"> 																		 																				<a href="/cscps/ciom/medicineSearch" class="tmd" title="前往衛材藥品查詢">衛材藥品查詢</a> 																		 																		</li> 																		<li class="tpb_a"> 																		 																				<a href="/prkms/priority/common/indexTenderPriority" class="tmd" title="前往優先採購查詢">優先採購查詢</a> 																		 																		</li> 																		<li class="tpb_a"> 																		 																				<a href="/osm/public/proctrg" class="tmd" title="前往採購標的分類">採購標的分類</a> 																		 																		</li> 														</ul> 														<!--第三階選項結束 --> 													</li> 													 							    			</ul> 							    			<ul class="sub_nav list"> 													<li class="li_tp tm"> 														<a class="tmc">財物相關</a> 														 														<!--第三階選項 --> 														<ul class="sub02"> 																		<li class="tpb_a"> 																		 																				<a href="/opas/arpam/public/indexArpam" class="tmd" title="前往財物出租查詢">財物出租查詢</a> 																		 																		</li> 																		<li class="tpb_a"> 																		 																				<a href="/opas/aspam/public/indexAspam" class="tmd" title="前往財物變賣查詢">財物變賣查詢</a> 																		 																		</li> 																		<li class="tpb_a"> 																		 																				<a href="/tps/pia/PiaPriceWithoutSsoController/query/commonPiaPriceSearch" class="tmd" title="前往物調公告查詢">物調公告查詢</a> 																		 																		</li> 														</ul> 														<!--第三階選項結束 --> 													</li> 													 							    			</ul> 							    			<ul class="sub_nav list"> 													<li class="li_tp tm"><a class="tmc">國外採購</a> 														<!--第三階選項 --> 														<ul class="sub02"> 																	<li class="tpb_a"><a href="https://gpa.taiwantrade.com.tw/zh/home"class="tmd"title="前往全球政府採購商機(另開頁面)"rel="noopener noreferrer"target="_blank">全球政府採購商機</a></li> 																	<li class="tpb_a"><a href="https://www.pcc.gov.tw/pcc/content/index?eid=1980&amp;type=C"class="tmd"title="前往外國政府採購網站(另開頁面)"rel="noopener noreferrer"target="_blank">外國政府採購網站</a></li> 																	<li class="tpb_a"><a href="/prkms/foreignGov/common/indexTenderForeignGov"class="tmd"title="前往外國政府採購商情">外國政府採購商情</a></li> 														</ul> 														<!--第三階選項結束 --> 													</li> 													 							    			</ul> 							    			<ul class="sub_nav list"> 													<li class="li_tp tm"> 														<a class="tmc">廠商相關</a> 														 														<!--第三階選項 --> 														<ul class="sub02"> 																		<li class="tpb_a"> 																		 																				<a href="/osm/public/foreSupp" class="tmd" title="前往外國廠商代碼">外國廠商代碼</a> 																		 																		</li> 																		<li class="tpb_a"> 																		 																				<a href="/vms/stiem/stiemPublic/indexSearchPublic" class="tmd" title="前往科技研究機構">科技研究機構</a> 																		 																		</li> 																		<li class="tpb_a"> 																		 																				<a href="/peems/lapeem/lapeemGeneralQuery" class="tmd" title="前往效益評估查詢">效益評估查詢</a> 																		 																		</li> 																		<li class="tpb_a"> 																		 																				<a href="/vms/jgvm/jgvmPublic/indexReadJgvmPublic" class="tmd" title="前往連帶保證廠商">連帶保證廠商</a> 																		 																		</li> 																		<li class="tpb_a"> 																		 																				<a href="/vms/emlm/emlmPublicSearch/indexSearchEmlmPublic" class="tmd" title="前往優良廠商名單">優良廠商名單</a> 																		 																		</li> 																		<li class="tpb_a"> 																		 																				<a href="/vms/rvlm/rvlmPublicSearch/indexSearchRvlmPublic" class="tmd" title="前往拒絕往來廠商">拒絕往來廠商</a> 																		 																		</li> 																		<li class="tpb_a"> 																		 																				<a href="/vms/rvlm/rvlmPublicSearchRevoked/indexSearchRevokedRvlmPublic" class="tmd" title="前往註銷拒絕往來廠商">註銷拒絕往來廠商</a> 																		 																		</li> 																		<li class="tpb_a"> 																		 																				<a href="/vms/gblm/gblmPublicSearch/indexSearchGblmPublic" class="tmd" title="前往全球化廠商名單">全球化廠商名單</a> 																		 																		</li> 																		<li class="tpb_a"> 																		 																				<a href="/vms/gblm/gblmPublicCompositeSearch/indexCompositeSearch" class="tmd" title="前往廠商綜合查詢">廠商綜合查詢</a> 																		 																		</li> 																		<li class="tpb_a"> 																		 																				<a href="/tps/tom/obtainment/common/printTokenPublic/printVerifyToken" class="tmd" title="前往列印領標憑據">列印領標憑據</a> 																		 																		</li> 																		<li class="tpb_a"> 																		 																				<a href="/qdcs/yellowPageQuery" class="tmd" title="前往廠商名錄">廠商名錄</a> 																		 																		</li> 																		<li class="tpb_a"> 																		 																				<a href="/piat/dispute/oneOOne/indexSearch" class="tmd" title="前往政府採購法第101條停權案例">政府採購法第101條停權案例</a> 																		 																		</li> 														</ul> 														<!--第三階選項結束 --> 													</li> 													 							    			</ul> 							    			<ul class="sub_nav list"> 													<li class="li_tp tm"> 														<a class="tmc">其他</a> 														 														<!--第三階選項 --> 														<ul class="sub02"> 																		<li class="tpb_a"> 																		 																				<a href="/prkms/rebuild/common/indexRebuild" class="tmd" title="前往災區重建工程案件查詢">災區重建工程案件查詢</a> 																		 																		</li> 																		<li class="tpb_a"> 																		 																				<a href="/peems/lapeem/lapeemGeneralPolit" class="tmd" title="前往政治獻金法第七條查詢">政治獻金法第七條查詢</a> 																		 																		</li> 																		<li class="tpb_a"> 																		 																				<a href="/tps/common/atmAbroad/commonSearchAtmAbroad/search" class="tmd" title="前往得標外國政府採購案件查詢">得標外國政府採購案件查詢</a> 																		 																		</li> 																		<li class="tpb_a"> 																		 																				<a href="/tps/tom/thdTender/query/enterToThdTender" class="tmd" title="前往歷史文件瀏覽">歷史文件瀏覽</a> 																		 																		</li> 																		<li class="tpb_a"> 																		 																				<a href="/pec/PecQueryPublic/indexPecQueryPublicInit" class="tmd" title="前往採購評選委員名單">採購評選委員名單</a> 																		 																		</li> 																		<li class="tpb_a"> 																		 																				<a href="/wr-report/wr/homeClient/list" class="tmd" title="前往採購統計">採購統計</a> 																		 																		</li> 																		<li class="tpb_a"> 																		 																				<a href="/pis/bsmizone/readBsmizone" class="tmd" title="前往商品檢驗查詢">商品檢驗查詢</a> 																		 																		</li> 														</ul> 														<!--第三階選項結束 --> 													</li> 													 							    			</ul> 							</div> 							<!--第二階選項結束 --> 						</div> 					 			    		<!--第一階選項 --> 						<li class="li_tp"> 							<a href="#" class="bind_menu_a" data-layerid="layer5">下載專區</a> 						</li> 						<!--第一階選項結束 --> 					 						<div class="sub_menu bind_menu_a" id="layer5" data-layerid="layer5" style="display: none;"> 							<i></i> 							<!--第二階選項 --> 							<div class="sub_area clearfix"> 							    			<ul class="sub_nav list"> 													<li class="li_tp tm"><a class="tmc">下載專區</a> 														<!--下載專區 > 下載專區 > 第三階選項 --> 														<ul class="sub02"> 																	<li class="tpb_a"> 																		<a href="/pis/prac/declarationClient/bWait"class="tmd" title="前往等標期(另開頁面)" rel="noopener noreferrer" target="_blank">等標期</a> 																	</li> 																	<li class="tpb_a"> 																		<a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=50003004"class="tmd" title="前往拒絕往來名單">拒絕往來名單</a> 																	</li> 																	<li class="tpb_a"> 																		<a href="https://www.pcc.gov.tw/content/list?eid=9807&lang=1"class="tmd" title="前往招標文件範本(另開頁面)" rel="noopener noreferrer" target="_blank">招標文件範本</a> 																	</li> 																	<li class="tpb_a"> 																		<a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=42000000"class="tmd" title="前往共同供應契約">共同供應契約</a> 																	</li> 																	<li class="tpb_a"> 																		<a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=20000003"class="tmd" title="前往系統使用手冊">系統使用手冊</a> 																	</li> 																	<li class="tpb_a"> 																		<a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=60000736"class="tmd" title="前往三代教學影片">三代教學影片</a> 																	</li> 																	<li class="tpb_a"> 																		<a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=20000004"class="tmd" title="前往常用工具下載">常用工具下載</a> 																	</li> 																	<li class="tpb_a"> 																		<a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=60000731"class="tmd" title="前往教育訓練教材">教育訓練教材</a> 																	</li> 																	<li class="tpb_a"> 																		<a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=70000000"class="tmd" title="前往安裝程式環境檢測及障礙排除說明">安裝程式環境檢測及障礙排除說明</a> 																	</li> 																	<li class="tpb_a"> 																		<a href="/tps/tp/OpenData/showList"class="tmd" title="前往資料集下載(另開頁面)" rel="noopener noreferrer" target="_blank">資料集下載</a> 																	</li> 																	<li class="tpb_a"> 																		<a href="https://web.pcc.gov.tw/tps/tp/OpenData/showGPAList"class="tmd" title="前往近半年GPA資料集下載(另開頁面)" rel="noopener noreferrer" target="_blank">近半年GPA資料集下載</a> 																	</li> 																	<li class="tpb_a"> 																		<a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=60000716"class="tmd" title="前往帳號處理">帳號處理</a> 																	</li> 																	<li class="tpb_a"> 																		<a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=50003000"class="tmd" title="前往重要訊息通告">重要訊息通告</a> 																	</li> 																	<li class="tpb_a"> 																		<a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=70000026"class="tmd" title="前往ODF專區">ODF專區</a> 																	</li> 																	<li class="tpb_a"> 																		<a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=50003006"class="tmd" title="前往中央組改文件 ">中央組改文件 </a> 																	</li> 																	<li class="tpb_a"> 																		<a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=50003002"class="tmd" title="前往５都改制文件">５都改制文件</a> 																	</li> 														</ul> 														<!--下載專區 > 下載專區 > 第三階選項結束 --> 													</li> 							    			</ul> 							</div> 							<!--第二階選項結束 --> 						</div> 					 			    		<!--第一階選項 --> 						<li class="li_tp"> 							<a href="#" class="bind_menu_a" data-layerid="layer6">相關連結</a> 						</li> 						<!--第一階選項結束 --> 					 						<div class="sub_menu bind_menu_a" id="layer6" data-layerid="layer6" style="display: none;"> 							<i></i> 							<!--第二階選項 --> 							<div class="sub_area clearfix"> 							    			<ul class="sub_nav list"> 													<li class="li_tp tm"> 														<a class="tmc">憑證</a> 														 														<!--第三階選項 --> 														<ul class="sub02"> 																		<li class="tpb_a"> 																			<a href="https://gcp.nat.gov.tw/ "class="tmd" title="前往政府憑證管理中心(另開頁面)" rel="noopener noreferrer" target="_blank">政府憑證管理中心</a> 																		</li> 																		<li class="tpb_a"> 																			<a href="https://moeaca.nat.gov.tw/ "class="tmd" title="前往工商憑證管理中心(另開頁面)" rel="noopener noreferrer" target="_blank">工商憑證管理中心</a> 																		</li> 																		<li class="tpb_a"> 																			<a href="https://moica.nat.gov.tw/main.html "class="tmd" title="前往自然人憑證管理中心(另開頁面)" rel="noopener noreferrer" target="_blank">自然人憑證管理中心</a> 																		</li> 																		<li class="tpb_a"> 																			<a href="https://xca.nat.gov.tw/ "class="tmd" title="前往組織及團體憑證管理中心(另開頁面)" rel="noopener noreferrer" target="_blank">組織及團體憑證管理中心</a> 																		</li> 																		<li class="tpb_a"> 																			<a href="https://fido.moi.gov.tw/pt/ "class="tmd" title="前往內政部行動自然人憑證系統(另開頁面)" rel="noopener noreferrer" target="_blank">內政部行動自然人憑證系統</a> 																		</li> 														</ul> 														<!--第三階選項結束 --> 													</li> 													 							    			</ul> 							    			<ul class="sub_nav list"> 													<li class="li_tp tm"> 														<a class="tmc">行政院工程會全球資訊網</a> 														 														<!--第三階選項 --> 														<ul class="sub02"> 																		<li class="tpb_a"> 																			<a href="https://www.pcc.gov.tw/ "class="tmd" title="前往行政院工程會全球資訊網(另開頁面)" rel="noopener noreferrer" target="_blank">行政院工程會全球資訊網</a> 																		</li> 														</ul> 														<!--第三階選項結束 --> 													</li> 													 							    			</ul> 							    			<ul class="sub_nav list"> 													<li class="li_tp tm"><a class="tmc">其他</a> 														 														<!--相關連結 > 其他 > 第三階選項 --> 														<ul class="sub02"> 																	<li class="tpb_a"> 																		<a href="https://pcic.pcc.gov.tw/pwc-web/"class="tmd" title="前往工程標案管理系統(另開頁面)" rel="noopener noreferrer" target="_blank">工程標案管理系統</a> 																	</li> 																	<li class="tpb_a"> 																		<a href="https://pcic.pcc.gov.tw/pwc-web/"class="tmd" title="前往工程延遲付款通報(另開頁面)" rel="noopener noreferrer" target="_blank">工程延遲付款通報</a> 																	</li> 																	<li class="tpb_a"> 																		<a href="https://ppp.mof.gov.tw/WWW/inv_ann.aspx"class="tmd" title="前往促進民間參與公共建設公告(另開頁面)" rel="noopener noreferrer" target="_blank">促進民間參與公共建設公告</a> 																	</li> 																	<li class="tpb_a"> 																		<a href="https://gcis.nat.gov.tw/mainNew/"class="tmd" title="前往經濟部商工行政服務入口網(另開頁面)" rel="noopener noreferrer" target="_blank">經濟部商工行政服務入口網</a> 																	</li> 																	<li class="tpb_a"> 																		<a href="https://pcic.pcc.gov.tw/pwc-web/"class="tmd" title="前往技師與工程技術顧問公司管理資訊系統(另開頁面)" rel="noopener noreferrer" target="_blank">技師與工程技術顧問公司管理資訊系統</a> 																	</li> 																	<li class="tpb_a"> 																		<a href="https://cloudbm.nlma.gov.tw/CPTL/index.jsp"class="tmd" title="前往內政部國土管理署全國建築管理資訊入口網(另開頁面)" rel="noopener noreferrer" target="_blank">內政部國土管理署全國建築管理資訊入口網</a> 																	</li> 																	<li class="tpb_a"> 																		<a href="https://www.etax.nat.gov.tw"class="tmd" title="前往財政部稅務入口網(另開頁面)" rel="noopener noreferrer" target="_blank">財政部稅務入口網</a> 																	</li> 																	<li class="tpb_a"> 																		<a href="https://www.energylabel.org.tw/purchasing/govgreen/list.aspx"class="tmd" title="前往經濟部能源署節能標章綠色採購網(另開頁面)" rel="noopener noreferrer" target="_blank">經濟部能源署節能標章綠色採購網</a> 																	</li> 																	<li class="tpb_a"> 																		<a href="https://dpws.sfaa.gov.tw/index.jsp"class="tmd" title="前往衛生福利部社會及家庭署身心障礙服務入口網(另開頁面)" rel="noopener noreferrer" target="_blank">衛生福利部社會及家庭署身心障礙服務入口網</a> 																	</li> 																	<li class="tpb_a"> 																		<a href="https://socialcom.mohw.gov.tw/swalPublic/front/taIndex"class="tmd" title="前往衛生福利部社福人員勞動申訴及溝通平臺-違規停止補助名單(另開頁面)" rel="noopener noreferrer" target="_blank">衛生福利部社福人員勞動申訴及溝通平臺-違規停止補助名單</a> 																	</li> 																	<li class="tpb_a"> 																		<a href="https://cloudbm.nlma.gov.tw/bccs/login"class="tmd" title="前往內政部國土管理署工程重機械編管及運用管理系統(另開頁面)" rel="noopener noreferrer" target="_blank">內政部國土管理署工程重機械編管及運用管理系統</a> 																	</li> 																	<li class="tpb_a"> 																		<a href="/pis/srml/linkGroupClient/readLinkGroupClient?id=50003006"class="tmd" title="前往經濟部(投資審議司)公告陸資資訊(另開頁面)" rel="noopener noreferrer" target="_blank">經濟部(投資審議司)公告陸資資訊</a> 																	</li> 																	<li class="tpb_a"> 																		<a href="https://www.mine.gov.tw/"class="tmd" title="前往經濟部地質調查及礦業管理中心(另開頁面)" rel="noopener noreferrer" target="_blank">經濟部地質調查及礦業管理中心</a> 																	</li> 																	<li class="tpb_a"> 																		<a href="https://pcic.pcc.gov.tw/pwc-web/service/bidCae001"class="tmd" title="前往廠商承攬公共工程履歷查詢(另開頁面)" rel="noopener noreferrer" target="_blank">廠商承攬公共工程履歷查詢</a> 																	</li> 																	<li class="tpb_a"> 																		<a href="https://pcic.pcc.gov.tw/pwc-web/service/bidCab001"class="tmd" title="前往公共工程人員履歷查詢(另開頁面)" rel="noopener noreferrer" target="_blank">公共工程人員履歷查詢</a> 																	</li> 																	<li class="tpb_a"> 																		<a href="https://pcic.pcc.gov.tw/pwc-web/service/eng0814Query"class="tmd" title="前往技師懲戒資料(另開頁面)" rel="noopener noreferrer" target="_blank">技師懲戒資料</a> 																	</li> 																	<li class="tpb_a"> 																		<a href="https://cloudbm.nlma.gov.tw/CPTL/cpt0503m.do"class="tmd" title="前往內政部國土管理署營造業評鑑結果查詢(另開頁面)" rel="noopener noreferrer" target="_blank">內政部國土管理署營造業評鑑結果查詢</a> 																	</li> 																	<li class="tpb_a"> 																		<a href="/pis/srml/linkGroupClient/readLinkGroupClient?id=50003007"class="tmd" title="前往行政法人相關採購資訊(另開頁面)" rel="noopener noreferrer" target="_blank">行政法人相關採購資訊</a> 																	</li> 																	<li class="tpb_a"> 																		<a href="https://ebuying.hinet.net"class="tmd" title="前往採購網加值服務訂閱(另開頁面)" rel="noopener noreferrer" target="_blank">採購網加值服務訂閱</a> 																	</li> 																	<li class="tpb_a"> 																		<a href="https://ccs.hinet.net/line_pwd_cht_big5.htm"class="tmd" title="前往更改HiNet連線密碼(另開頁面)" rel="noopener noreferrer" target="_blank">更改HiNet連線密碼</a> 																	</li> 																	<li class="tpb_a"> 																		<a href="https://e-pay.hinet.net/"class="tmd" title="前往HiNet點數卡購買(另開頁面)" rel="noopener noreferrer" target="_blank">HiNet點數卡購買</a> 																	</li> 																	<li class="tpb_a"> 																		<a href="https://announcement.mol.gov.tw/"class="tmd" title="前往勞動部「違反勞動法令事業單位(雇主)查詢系統」(另開頁面)" rel="noopener noreferrer" target="_blank">勞動部「違反勞動法令事業單位(雇主)查詢系統」</a> 																	</li> 														</ul> 														<!--相關連結 > 其他 > 第三階選項結束 --> 													</li> 													 							    			</ul> 							</div> 							<!--第二階選項結束 --> 						</div> 					 			    		<!--第一階選項 --> 						<li class="li_tp"> 							<a href="#" class="bind_menu_a" data-layerid="layer7">請求協助</a> 						</li> 						<!--第一階選項結束 --> 					 						<div class="sub_menu bind_menu_a" id="layer7" data-layerid="layer7" style="display: none;"> 							<i></i> 							<!--第二階選項 --> 							<div class="sub_area clearfix"> 							    			<ul class="sub_nav list"> 													<li class="li_tp tm"> 														<a class="tmc">熱門問答</a> 														 														<!--第三階選項 --> 														<ul class="sub02"> 																		<li class="tpb_a"> 																		 																				<a href="/csci/pf/readCsciFaqStatList" class="tmd" title="前往熱門問答">熱門問答</a> 																		 																		</li> 														</ul> 														<!--第三階選項結束 --> 													</li> 													 							    			</ul> 							    			<ul class="sub_nav list"> 													<li class="li_tp tm"> 														<a class="tmc">問題檢索</a> 														 														<!--第三階選項 --> 														<ul class="sub02"> 																		<li class="tpb_a"> 																		 																				<a href="/csci/pf/readCsciFaqDetlList" class="tmd" title="前往問題檢索">問題檢索</a> 																		 																		</li> 														</ul> 														<!--第三階選項結束 --> 													</li> 													 							    			</ul> 							    			<ul class="sub_nav list"> 													<li class="li_tp tm"> 														<a class="tmc">我要發問</a> 														 														<!--第三階選項 --> 														<ul class="sub02"> 																		<li class="tpb_a"> 																		 																				<a href="/csci/pf/feedback" class="tmd" title="前往我要發問">我要發問</a> 																		 																		</li> 														</ul> 														<!--第三階選項結束 --> 													</li> 													 							    			</ul> 							</div> 							<!--第二階選項結束 --> 						</div> 					 			    		<!--第一階選項 --> 						<li class="li_tp"> 							<a href="#" class="bind_menu_a" data-layerid="layer8">最新功能</a> 						</li> 						<!--第一階選項結束 --> 					 						<div class="sub_menu bind_menu_a" id="layer8" data-layerid="layer8" style="display: none;"> 							<i></i> 							<!--第二階選項 --> 							<div class="sub_area clearfix"> 							    			<ul class="sub_nav list"> 													<li class="li_tp tm"> 														<a class="tmc">最新功能</a> 														 														<!--第三階選項 --> 														<ul class="sub02"> 																		<li class="tpb_a"> 																		 																				<a href="/osm/public/newSysOn" class="tmd" title="前往最新功能">最新功能</a> 																		 																		</li> 														</ul> 														<!--第三階選項結束 --> 													</li> 													 							    			</ul> 							</div> 							<!--第二階選項結束 --> 						</div> 				</ul> 			</div> 		</div> 	</div> </div>   <div class="mobile"> 	<div class="col-md-12">         <div class="container pl0">             <nav class="main-nav d-none d-lg-block">                 <ul> 				    	<!--第一階選項 --> 						<li class="drop-down"> 							<a href="#">網站導覽</a> 							<ul> 											 												<!--第三階選項 --> 																<li> 																	<a href="/pis/prac/sitemapClient/readG3Sitemap" title="前往網站導覽(三代)">網站導覽(三代)</a> 																</li> 																<li> 																	<a href="/pis/prac/sitemapClient/readG2Sitemap" title="前往網站導覽(二代)">網站導覽(二代)</a> 																</li> 												<!--第三階選項結束1 -->	 											</li> 				    			</ul> 				    	</li> 				    	<!--第一階選項 --> 						<li class="drop-down"> 							<a href="#">學習資源</a> 							<ul> 											 												<!--第三階選項 --> 																<li> 																	<a href="/ol/entityCourse/index" title="前往機關端(線上報名)">機關端(線上報名)</a> 																</li> 																<li> 																	<a href="/ol/trgEnroll/index" title="前往廠商端(線上報名)">廠商端(線上報名)</a> 																</li> 												<!--第三階選項結束1 -->	 											</li> 											 												<!--第三階選項 --> 																<li> 																	<a href="/qdcs/trainingIndex" title="前往線上教學">線上教學</a> 																</li> 												<!--第三階選項結束1 -->	 											</li> 											 												<!--第三階選項 --> 																<li> 																	<a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=20000003" title="前往系統使用手冊">系統使用手冊</a> 																</li> 																<li> 																	<a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=60000731" title="前往三代採購網教育訓練教材">三代採購網教育訓練教材</a> 																</li> 																<li> 																	<a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=60000736" title="前往三代教學影片">三代教學影片</a> 																</li> 																<li> 																	<a href="/pis/prac/downloadGroupClient/getClientDownloadForDownloadFile?id=70000016" title="前往環境檢測安裝步驟及FAQ">環境檢測安裝步驟及FAQ</a> 																</li> 																<li> 																	<a href="/pis/prac/downloadGroupClient/getClientDownloadForDownloadFile?id=70000015" title="前往用戶端程式偵測障礙排除">用戶端程式偵測障礙排除</a> 																</li> 																<li> 																	<a href="/pis/prac/downloadGroupClient/readPreparation" title="前往第一次使用三代政府電子採購網之前置作業">第一次使用三代政府電子採購網之前置作業</a> 																</li> 												<!--第三階選項結束1 -->	 											</li> 											 												<!--第三階選項 --> 																<li> 																	<a href="/psms/pptom/pptomPublic/indexRead" title="前往代訓機構名單">代訓機構名單</a> 																</li> 																<li> 																	<a href="/psms/pptcm/pptcmPublic/indexReadPptcm" title="前往代訓機構開課">代訓機構開課</a> 																</li> 																<li> 																	<a href="/psms/plrtqdm/questionPublic/indexReadQuestion" title="前往採購法規題庫">採購法規題庫</a> 																</li> 																<li> 																	<a href="/psms/plrtm/plrtmQuery/indexReadCertificate" title="前往採購專業人員及格證書查詢">採購專業人員及格證書查詢</a> 																</li> 																<li> 																	<a href="/psms/plrtm/plrtmReissue/certificateReissue" title="前往採購專業人員及格證書補發申請">採購專業人員及格證書補發申請</a> 																</li> 												<!--第三階選項結束1 -->	 											</li> 				    			</ul> 				    	</li> 				    	<!--第一階選項 --> 						<li class="drop-down"> 							<a href="#">採購作業</a> 							<ul> 											 												<!--第三階選項 --> 																<li> 																	<a href="#" onclick="goTender()" title="前往標案查詢">標案查詢</a> 																</li> 												<!--第三階選項結束1 -->	 											</li> 											 												<!--第三階選項 --> 																<li> 																	<a href="/pis/prac/declarationClient/bWait" title="前往等標期">等標期</a> 																</li> 												<!--第三階選項結束1 -->	 											</li> 											 												<!--第三階選項 --> 																<li> 																	<a href="/ebm/public/EbmMain/indexEbmMain" title="前往電子公報">電子公報</a> 																</li> 												<!--第三階選項結束1 -->	 											</li> 				    			</ul> 				    	</li> 				    	<!--第一階選項 --> 						<li class="drop-down"> 							<a href="#">查詢服務</a> 							<ul> 											 												<!--第三階選項 --> 																<li> 																	<a href="#" onclick="goTender()" title="前往標案查詢">標案查詢</a> 																</li> 																<li> 																	<a href="/prkms/gpaPredict/common/indexGpaPredict" title="前往採購預告查詢">採購預告查詢</a> 																</li> 																<li> 																	<a href="/prkms/tpRead/common/indexTpRead" title="前往公開閱覽查詢">公開閱覽查詢</a> 																</li> 																<li> 																	<a href="/prkms/tpAppeal/common/indexTpAppeal" title="前往公開徵求查詢">公開徵求查詢</a> 																</li> 																<li> 																	<a href="/opas/pspad/pspadClient/readPublicPspadListInit" title="前往公示送達查詢">公示送達查詢</a> 																</li> 																<li> 																	<a href="/cscps/ciom/medicineSearch" title="前往衛材藥品查詢">衛材藥品查詢</a> 																</li> 																<li> 																	<a href="/prkms/priority/common/indexTenderPriority" title="前往優先採購查詢">優先採購查詢</a> 																</li> 																<li> 																	<a href="/osm/public/proctrg" title="前往採購標的分類">採購標的分類</a> 																</li> 												<!--第三階選項結束1 -->	 											</li> 											 												<!--第三階選項 --> 																<li> 																	<a href="/opas/arpam/public/indexArpam" title="前往財物出租查詢">財物出租查詢</a> 																</li> 																<li> 																	<a href="/opas/aspam/public/indexAspam" title="前往財物變賣查詢">財物變賣查詢</a> 																</li> 																<li> 																	<a href="/tps/pia/PiaPriceWithoutSsoController/query/commonPiaPriceSearch" title="前往物調公告查詢">物調公告查詢</a> 																</li> 												<!--第三階選項結束1 -->	 											</li> 											 												<!--第三階選項 --> 																<li> 																	<a href="https://gpa.taiwantrade.com.tw/zh/home" title="前往全球政府採購商機">全球政府採購商機</a> 																</li> 																<li> 																	<a href="https://www.pcc.gov.tw/pcc/content/index?eid=1980&amp;type=C" title="前往外國政府採購網站">外國政府採購網站</a> 																</li> 																<li> 																	<a href="/prkms/foreignGov/common/indexTenderForeignGov" title="前往外國政府採購商情">外國政府採購商情</a> 																</li> 												<!--第三階選項結束1 -->	 											</li> 											 												<!--第三階選項 --> 																<li> 																	<a href="/osm/public/foreSupp" title="前往外國廠商代碼">外國廠商代碼</a> 																</li> 																<li> 																	<a href="/vms/stiem/stiemPublic/indexSearchPublic" title="前往科技研究機構">科技研究機構</a> 																</li> 																<li> 																	<a href="/peems/lapeem/lapeemGeneralQuery" title="前往效益評估查詢">效益評估查詢</a> 																</li> 																<li> 																	<a href="/vms/jgvm/jgvmPublic/indexReadJgvmPublic" title="前往連帶保證廠商">連帶保證廠商</a> 																</li> 																<li> 																	<a href="/vms/emlm/emlmPublicSearch/indexSearchEmlmPublic" title="前往優良廠商名單">優良廠商名單</a> 																</li> 																<li> 																	<a href="/vms/rvlm/rvlmPublicSearch/indexSearchRvlmPublic" title="前往拒絕往來廠商">拒絕往來廠商</a> 																</li> 																<li> 																	<a href="/vms/rvlm/rvlmPublicSearchRevoked/indexSearchRevokedRvlmPublic" title="前往註銷拒絕往來廠商">註銷拒絕往來廠商</a> 																</li> 																<li> 																	<a href="/vms/gblm/gblmPublicSearch/indexSearchGblmPublic" title="前往全球化廠商名單">全球化廠商名單</a> 																</li> 																<li> 																	<a href="/vms/gblm/gblmPublicCompositeSearch/indexCompositeSearch" title="前往廠商綜合查詢">廠商綜合查詢</a> 																</li> 																<li> 																	<a href="/tps/tom/obtainment/common/printTokenPublic/printVerifyToken" title="前往列印領標憑據">列印領標憑據</a> 																</li> 																<li> 																	<a href="/qdcs/yellowPageQuery" title="前往廠商名錄">廠商名錄</a> 																</li> 																<li> 																	<a href="/piat/dispute/oneOOne/indexSearch" title="前往政府採購法第101條停權案例">政府採購法第101條停權案例</a> 																</li> 												<!--第三階選項結束1 -->	 											</li> 											 												<!--第三階選項 --> 																<li> 																	<a href="/prkms/rebuild/common/indexRebuild" title="前往災區重建工程案件查詢">災區重建工程案件查詢</a> 																</li> 																<li> 																	<a href="/peems/lapeem/lapeemGeneralPolit" title="前往政治獻金法第七條查詢">政治獻金法第七條查詢</a> 																</li> 																<li> 																	<a href="/tps/common/atmAbroad/commonSearchAtmAbroad/search" title="前往得標外國政府採購案件查詢">得標外國政府採購案件查詢</a> 																</li> 																<li> 																	<a href="/tps/tom/thdTender/query/enterToThdTender" title="前往歷史文件瀏覽">歷史文件瀏覽</a> 																</li> 																<li> 																	<a href="/pec/PecQueryPublic/indexPecQueryPublicInit" title="前往採購評選委員名單">採購評選委員名單</a> 																</li> 																<li> 																	<a href="/wr-report/wr/homeClient/list" title="前往採購統計">採購統計</a> 																</li> 																<li> 																	<a href="/pis/bsmizone/readBsmizone" title="前往商品檢驗查詢">商品檢驗查詢</a> 																</li> 												<!--第三階選項結束1 -->	 											</li> 				    			</ul> 				    	</li> 				    	<!--第一階選項 --> 						<li class="drop-down"> 							<a href="#">下載專區</a> 							<ul> 											 												<!--下載專區 > 下載專區 > 第三階選項 --> 												 															<li><a href="/pis/prac/declarationClient/bWait" title="前往等標期(另開頁面)" rel="noopener noreferrer" target="_blank">等標期</a></li> 															<li><a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=50003004" title="前往拒絕往來名單">拒絕往來名單</a></li> 															<li><a href="https://www.pcc.gov.tw/content/list?eid=9807&lang=1" title="前往招標文件範本(另開頁面)" rel="noopener noreferrer" target="_blank">招標文件範本</a></li> 															<li><a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=42000000" title="前往共同供應契約">共同供應契約</a></li> 															<li><a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=20000003" title="前往系統使用手冊">系統使用手冊</a></li> 															<li><a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=60000736" title="前往三代教學影片">三代教學影片</a></li> 															<li><a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=20000004" title="前往常用工具下載">常用工具下載</a></li> 															<li><a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=60000731" title="前往教育訓練教材">教育訓練教材</a></li> 															<li><a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=70000000" title="前往安裝程式環境檢測及障礙排除說明">安裝程式環境檢測及障礙排除說明</a></li> 															<li><a href="/tps/tp/OpenData/showList" title="前往資料集下載(另開頁面)" rel="noopener noreferrer" target="_blank">資料集下載</a></li> 															<li><a href="https://web.pcc.gov.tw/tps/tp/OpenData/showGPAList" title="前往近半年GPA資料集下載(另開頁面)" rel="noopener noreferrer" target="_blank">近半年GPA資料集下載</a></li> 															<li><a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=60000716" title="前往帳號處理">帳號處理</a></li> 															<li><a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=50003000" title="前往重要訊息通告">重要訊息通告</a></li> 															<li><a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=70000026" title="前往ODF專區">ODF專區</a></li> 															<li><a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=50003006" title="前往中央組改文件 ">中央組改文件 </a></li> 															<li><a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=50003002" title="前往５都改制文件">５都改制文件</a></li> 												 												<!--下載專區 > 下載專區 > 第三階選項結束 --> 				    			</ul> 				    	</li> 				    	<!--第一階選項 --> 						<li class="drop-down"> 							<a href="#">相關連結</a> 							<ul> 											 												<!--第三階選項 --> 																<li> 																	<a href="https://gcp.nat.gov.tw/" title="前往政府憑證管理中心">政府憑證管理中心</a> 																</li> 																<li> 																	<a href="https://moeaca.nat.gov.tw/" title="前往工商憑證管理中心">工商憑證管理中心</a> 																</li> 																<li> 																	<a href="https://moica.nat.gov.tw/main.html" title="前往自然人憑證管理中心">自然人憑證管理中心</a> 																</li> 																<li> 																	<a href="https://xca.nat.gov.tw/" title="前往組織及團體憑證管理中心">組織及團體憑證管理中心</a> 																</li> 																<li> 																	<a href="https://fido.moi.gov.tw/pt/" title="前往內政部行動自然人憑證系統">內政部行動自然人憑證系統</a> 																</li> 												<!--第三階選項結束1 -->	 											</li> 											 												<!--第三階選項 --> 																<li> 																	<a href="https://www.pcc.gov.tw/" title="前往行政院工程會全球資訊網">行政院工程會全球資訊網</a> 																</li> 												<!--第三階選項結束1 -->	 											</li> 											 												<!--相關連結 > 其他 > 第三階選項 --> 																<li> 																	<a href="https://pcic.pcc.gov.tw/pwc-web/" title="前往工程標案管理系統(另開頁面)" rel="noopener noreferrer" target="_blank">工程標案管理系統</a> 																</li> 																<li> 																	<a href="https://pcic.pcc.gov.tw/pwc-web/" title="前往工程延遲付款通報(另開頁面)" rel="noopener noreferrer" target="_blank">工程延遲付款通報</a> 																</li> 																<li> 																	<a href="https://ppp.mof.gov.tw/WWW/inv_ann.aspx" title="前往促進民間參與公共建設公告(另開頁面)" rel="noopener noreferrer" target="_blank">促進民間參與公共建設公告</a> 																</li> 																<li> 																	<a href="https://gcis.nat.gov.tw/mainNew/" title="前往經濟部商工行政服務入口網(另開頁面)" rel="noopener noreferrer" target="_blank">經濟部商工行政服務入口網</a> 																</li> 																<li> 																	<a href="https://pcic.pcc.gov.tw/pwc-web/" title="前往技師與工程技術顧問公司管理資訊系統(另開頁面)" rel="noopener noreferrer" target="_blank">技師與工程技術顧問公司管理資訊系統</a> 																</li> 																<li> 																	<a href="https://cloudbm.nlma.gov.tw/CPTL/index.jsp" title="前往內政部國土管理署全國建築管理資訊入口網(另開頁面)" rel="noopener noreferrer" target="_blank">內政部國土管理署全國建築管理資訊入口網</a> 																</li> 																<li> 																	<a href="https://www.etax.nat.gov.tw" title="前往財政部稅務入口網(另開頁面)" rel="noopener noreferrer" target="_blank">財政部稅務入口網</a> 																</li> 																<li> 																	<a href="https://www.energylabel.org.tw/purchasing/govgreen/list.aspx" title="前往經濟部能源署節能標章綠色採購網(另開頁面)" rel="noopener noreferrer" target="_blank">經濟部能源署節能標章綠色採購網</a> 																</li> 																<li> 																	<a href="https://dpws.sfaa.gov.tw/index.jsp" title="前往衛生福利部社會及家庭署身心障礙服務入口網(另開頁面)" rel="noopener noreferrer" target="_blank">衛生福利部社會及家庭署身心障礙服務入口網</a> 																</li> 																<li> 																	<a href="https://socialcom.mohw.gov.tw/swalPublic/front/taIndex" title="前往衛生福利部社福人員勞動申訴及溝通平臺-違規停止補助名單(另開頁面)" rel="noopener noreferrer" target="_blank">衛生福利部社福人員勞動申訴及溝通平臺-違規停止補助名單</a> 																</li> 																<li> 																	<a href="https://cloudbm.nlma.gov.tw/bccs/login" title="前往內政部國土管理署工程重機械編管及運用管理系統(另開頁面)" rel="noopener noreferrer" target="_blank">內政部國土管理署工程重機械編管及運用管理系統</a> 																</li> 																<li> 																	<a href="/pis/srml/linkGroupClient/readLinkGroupClient?id=50003006" title="前往經濟部(投資審議司)公告陸資資訊">經濟部(投資審議司)公告陸資資訊</a> 																</li> 																<li> 																	<a href="https://www.mine.gov.tw/" title="前往經濟部地質調查及礦業管理中心(另開頁面)" rel="noopener noreferrer" target="_blank">經濟部地質調查及礦業管理中心</a> 																</li> 																<li> 																	<a href="https://pcic.pcc.gov.tw/pwc-web/service/bidCae001" title="前往廠商承攬公共工程履歷查詢(另開頁面)" rel="noopener noreferrer" target="_blank">廠商承攬公共工程履歷查詢</a> 																</li> 																<li> 																	<a href="https://pcic.pcc.gov.tw/pwc-web/service/bidCab001" title="前往公共工程人員履歷查詢(另開頁面)" rel="noopener noreferrer" target="_blank">公共工程人員履歷查詢</a> 																</li> 																<li> 																	<a href="https://pcic.pcc.gov.tw/pwc-web/service/eng0814Query" title="前往技師懲戒資料(另開頁面)" rel="noopener noreferrer" target="_blank">技師懲戒資料</a> 																</li> 																<li> 																	<a href="https://cloudbm.nlma.gov.tw/CPTL/cpt0503m.do" title="前往內政部國土管理署營造業評鑑結果查詢(另開頁面)" rel="noopener noreferrer" target="_blank">內政部國土管理署營造業評鑑結果查詢</a> 																</li> 																<li> 																	<a href="/pis/srml/linkGroupClient/readLinkGroupClient?id=50003007" title="前往行政法人相關採購資訊">行政法人相關採購資訊</a> 																</li> 																<li> 																	<a href="https://ebuying.hinet.net" title="前往採購網加值服務訂閱(另開頁面)" rel="noopener noreferrer" target="_blank">採購網加值服務訂閱</a> 																</li> 																<li> 																	<a href="https://ccs.hinet.net/line_pwd_cht_big5.htm" title="前往更改HiNet連線密碼(另開頁面)" rel="noopener noreferrer" target="_blank">更改HiNet連線密碼</a> 																</li> 																<li> 																	<a href="https://e-pay.hinet.net/" title="前往HiNet點數卡購買(另開頁面)" rel="noopener noreferrer" target="_blank">HiNet點數卡購買</a> 																</li> 																<li> 																	<a href="https://announcement.mol.gov.tw/" title="前往勞動部「違反勞動法令事業單位(雇主)查詢系統」(另開頁面)" rel="noopener noreferrer" target="_blank">勞動部「違反勞動法令事業單位(雇主)查詢系統」</a> 																</li> 													 												<!--相關連結 > 其他 > 第三階選項結束1 --> 				    			</ul> 				    	</li> 				    	<!--第一階選項 --> 						<li class="drop-down"> 							<a href="#">請求協助</a> 							<ul> 											 												<!--第三階選項 --> 																<li> 																	<a href="/csci/pf/readCsciFaqStatList" title="前往熱門問答">熱門問答</a> 																</li> 												<!--第三階選項結束1 -->	 											</li> 											 												<!--第三階選項 --> 																<li> 																	<a href="/csci/pf/readCsciFaqDetlList" title="前往問題檢索">問題檢索</a> 																</li> 												<!--第三階選項結束1 -->	 											</li> 											 												<!--第三階選項 --> 																<li> 																	<a href="/csci/pf/feedback" title="前往我要發問">我要發問</a> 																</li> 												<!--第三階選項結束1 -->	 											</li> 				    			</ul> 				    	</li> 				    	<!--第一階選項 --> 						<li class="drop-down"> 							<a href="#">最新功能</a> 							<ul> 											 												<!--第三階選項 --> 																<li> 																	<a href="/osm/public/newSysOn" title="前往最新功能">最新功能</a> 																</li> 												<!--第三階選項結束1 -->	 											</li> 				    			</ul> 				    	</li> 				</ul> 				<div class="d-lg-none"> 					<br> 					<hr> 					<br> 					<ul> 						<li> 							<a href="/csci/pf/readCsciFaqStatList">常見問題</a> 						</li> 						<li> 							 <a href="/pis/prac/sitemapClient/readG3Sitemap">網站導覽</a> 						</li> 					</ul> 				</div> 			</nav> 		</div> 	</div> </div>
	</header>
	<!------------------ 桌面版導覽列 1-------------------->

	<!---------content------------>
	<div class="container" style="margin-bottom: 20px">
			<div class="mobile m_h"></div>
			<!--首頁統計表 -->
			



<!-- <a href="javascript:void(0);" id="AC" name="C" title="中間功能區塊" class="guide" accesskey="C">:::</a> -->

<style>
.top_3 {
    background: #fff;
    border-radius: 10px;
    padding: 10px;
    margin-bottom: 20px;
}
.top_3 img {
    width: 50px;
    margin-right: 10px;
}
</style>


<!--統計表-->
<!--20220217修改-->
<div class="pc">
	<div class="row">
		<div class="col-md-4">
	        <div class="row">
				<div class="col-md-6">
					<div class="top_3">
						<a href="/tps/tom/obtainment/common/printTokenPublic/printVerifyToken" style="text-decoration:none;">
						<img src="/pis/images/v2/top_icon4.png" alt="" class="d-inline-block align-middle">
						<div class="d-inline-block align-middle text-center">
							<div class="title_01" style="font-size:1.0rem">列印領<br>標憑據</div>
						</div>
						</a>
					</div>
				</div>
				<div class="col-md-6">
					<div class="top_3">
						<a href="/tps/queryCounter/queryCounterRecords" style="text-decoration:none;">
						<img src="/pis/images/v2/top_icon5.png" alt="" class="d-inline-block align-middle">
						<div class="d-inline-block align-middle text-center">
							<div class="title_01" style="font-size:1.0rem">熱門標案</div>
						</div>
						</a>
					</div>
				</div>		
	        </div>    
	    </div>
		<div class="col-md-8">
	        <div class="row">
	            <div class="col-md-4">
		            <div class="top_3">
		            <a href="##" onclick="basicTenderSearchCase('isSpdt');">
		            <img src="/pis/images/v2/top_icon1.png" alt="等標期內招標件數">
						<div class="d-inline-block align-middle text-center">
							<div class="title_01">等標期內招標件數</div>
							<span class="title_02">6427</span>
							<span class="title_03">件</span>
						</div>	
		            </a>
		            </div>			
				</div>
				<div class="col-md-4">
		            <div class="top_3">
		            <a href="##" onclick="basicTenderSearchCase('isNow');">
		            <img src="/pis/images/v2/top_icon2.png" alt="今日公告招標件數">
						<div class="d-inline-block align-middle text-center">
							<div class="title_01">今日公告招標件數</div>
							<span class="title_02">1119</span>
							<span class="title_03">件</span>
						</div>
		            </a>
		            </div>
				</div>
				<div class="col-md-4">
		            <div class="top_3">
		            <a href="/prkms/tender/common/agent/indexTenderAgentQuery">
		            <img src="/pis/images/v2/top_icon3.png"alt="今日公告決標件數">
						<div class="d-inline-block align-middle text-center">
							<div class="title_01">今日公告決標件數</div>
							<span class="title_02">685</span>
							<span class="title_03">件</span>
						</div>
		            </a>
		            </div>	
				</div>
	        </div>    
	    </div>	
	</div>
</div>
<div class="mobile">
	<div class="row">
		<div class="col-md-3">
			<div class="top_3">
			<span class="title_01">
			<a href="/tps/tom/obtainment/common/printTokenPublic/printVerifyToken" style="text-decoration:none;color:black;">列印領標憑據</a>
			</span></div>
		</div>
		<div class="col-md-3">
			<div class="top_3">
				<span class="title_01">
					<a href="/tps/queryCounter/queryCounterRecords" style="text-decoration:none;color:black;">熱門標案</a>
				</span>
			</div>
		</div>
		<div class="col-md-3">
            <div class="top_3">
            <a href="##" onclick="basicTenderSearchCase('isSpdt');">
            <span class="title_02">6427</span><span class="title_03">件</span><span class="title_01">等標期內招標件數</span>
            </a>
            </div>
		</div>
		<div class="col-md-3">
            <div class="top_3">
            <a href="##" onclick="basicTenderSearchCase('isNow');">
            <span class="title_02">1119</span><span class="title_03">件</span><span class="title_01">今日公告招標件數</span>
            </a>
            </div>
		</div>
		<div class="col-md-3">
            <div class="top_3">
            <a href="/prkms/tender/common/agent/indexTenderAgentQuery">
            <span class="title_02">685</span><span class="title_03">件</span><span class="title_01">今日公告決標件數</span>
            </a>
            </div>
		</div>
	</div>
</div>

<!--黃底通知區塊-->

<div style="background-color:#FFFFC9;margin-bottom:5px;">
	<h2 style="font-size:1em;">
		<a href="https://ebuying.hinet.net/amp/redirect?id=26&verify=$2a$10$gQZa/g2AJPznDlnmK64KPeYPvIonKacTGrvx9D56nnM6NfWkm5gwC" title="前往系統公告" target="_blank" rel="noopener noreferrer">
			<span style="color: black" title="【政府採購首次優惠】線上即時預覽招標文件，立即體驗!">
			【政府採購首次優惠】線上即時預覽招標文件，立即體驗!
			</span>
		</a>
	</h2>
</div>


<!--黃底通知區塊-->

<!--20220217修改-->
<!--統計表結束-->

			<div class="row" id="content">
			<!--首頁統計表結束 -->
			







<script type="text/javascript" src="/ccs/dist/geps3-commons.bundle.js"></script>
<link href="/pis/css/g.css" rel="stylesheet" type="text/css" />


<head>
</head>
<!-- 共用元件 -->
<script>

	//XFS 修正
	function htmlEncode(value) {
		// 建立一個暫存的div元素，並使用text()將內容存成html編碼文字後再用html()取出
		return $('<div/>').text(value).html();
	}
	if (top != self) {
		top.location = htmlEncode(self.location);
	}
</script>

<div class="col-xl-9" id="content">
	<div class="ru_10-h">
		<nav>
			<div class="nav nav-tabs" id="nav-tab" role="tablist">
				<a class="nav-item nav-link" id="index-tab1" data-toggle="tab" href="#index-tab1a" role="tab" aria-controls="index-tab1a" aria-selected="false">
					<span style="margin-top: 0;line-height: 1.2;">系統公告</span>
				</a>
				<a class="nav-item nav-link active" id="index-tab2" data-toggle="tab" href="#index-tab2a" role="tab" aria-controls="index-tab2a" aria-selected="true">
					<span style="margin-top: 0;line-height: 1.2;">找標案</span>
				</a>
				<a class="nav-item nav-link" id="index-tab5" href="/prkms/tender/common/agent/indexTenderAgent" role="tab" aria-controls="index-tab4a" aria-selected="false">
					<span style="margin-top: 0;line-height: 1.2;">找決標</span>
				</a>
				<a class="nav-item nav-link" id="index-tab3" href="/tps/tom/obtainment/common/printTokenPublic/printVerifyToken?isEng=false" role="tab" aria-controls="index-tab3a" aria-selected="false">
					<span style="margin-top: 0;line-height: 1.2;">列印領標憑據</span>
				</a>
				<a class="nav-item nav-link" id="index-tab6" href="/esdms/exdm/personalLogin/indexPersonalLogin" role="tab" aria-controls="index-tab5a" aria-selected="false">
					<span style="margin-top: 0;line-height: 1.2;">專家學者</span>
				</a>
				<a class="nav-item nav-link" id="index-tab4" href="/csci/pf/readCsciFaqStatList" role="tab" aria-controls="index-tab6a" aria-selected="false">
					<span style="margin-top: 0;line-height: 1.2;">熱門問答</span>
				</a>
			</div>
		</nav>
		<script>
// 			//顯示首頁上方tab對應之內容，與tab css更換
// 			function showMainContent(periodDivId, tab) {
// 				var showViewDivIdArray = [ '#bulletinDivId', '#tenderDivId', '#bidPeriodTenderDivId' ];
// 				var tabIdArray = [ '#bulletinTabId', '#tenderTabId', '#bidPeriodTenderTabId' ];
// 				if ($(tab).hasClass('tab_b_act')) {
// 					return;
// 				}
				
// 				$.each(showViewDivIdArray, function(index, value) {
// 					$(value).hide();
// 				});
				
// 				var periodDivIdStr = "#" + periodDivId;
// 				$(periodDivIdStr).show();

// 				$.each(tabIdArray, function(index, value) {
// 					if ($(value).hasClass('tab_b_act')) {
// 						$(value).removeClass('tab_b_act');
// 						$(value).addClass('tab_b');
// 					}
// 				});
				
// 				$(tab).removeClass('tab_b');
// 				$(tab).addClass('tab_b_act');

// 				if ('tenderDivId' == periodDivId) {
// 					//重新將日期元件rePosition
// 					rePositionTenderDatePicker('#tenderBasicDivId');
// 					rePositionTenderDatePicker('#tenderAdvancedDivId');
// 					rePositionTenderDatePicker('#tenderUpdatedDivId');
// 				}
// 			}


			//基本、進階、更正公告
			function changeTenderSearchWay(showTenderDivPeriod) {
				var showTenderViewDivIdArray = [ '#tenderBasicDivId', '#tenderAdvancedDivId', '#tenderUpdatedDivId' ];
				
				$.each(showTenderViewDivIdArray, function(index, value) {
					$(value).hide();
				});
				
				showTenderDivPeriod = "#" + showTenderDivPeriod;
				$(showTenderDivPeriod).show();
				//重新將日期元件rePosition
				rePositionTenderDatePicker(showTenderDivPeriod);
			}

			function rePositionTenderDatePicker(specificPeriodId) {
				switch (specificPeriodId) {
				case '#tenderBasicDivId':
					basicTenderComponent1.rePosition();
					basicTenderComponent2.rePosition();
					basicTenderCommonInit();
					break;
				case '#tenderAdvancedDivId':
					advancedTenderComponent1.rePosition();
					advancedTenderComponent2.rePosition();
					advancedTenderComponent3.rePosition();
					advancedTenderComponent4.rePosition();
					advancedTenderComponent5.rePosition();
					advancedTenderComponent6.rePosition();
					advancedTenderCommonInit();
					break;
				case '#tenderUpdatedDivId':
					updatedTenderComponent1.rePosition();
					updatedTenderComponent2.rePosition();
					updatedTenderCommonInit();
					break;
				default:
					break;
				}
			}
		</script>

		<div class="tab-content" id="nav-tabContent">
			<div class="tab-pane fade" id="index-tab1a" role="tabpanel" aria-labelledby="index-tab1">
				<ul class="tab_cen">
					
						
							
								
								<a href="/pis/pia/client/readBulletinDetail?pkPaiBulletin=70001730" title="前往 中華電信小額支付系統預計於114/6/28(六)上午06點00分至12點00分配合網路設備施工作業，施工期間服務將有中斷現象，敬請避開於此段時間進行電子領標、電子報價、歷史文件瀏覽、數位考場及線上訂閱採購加值服務等作業，不便之處，敬請見諒。">
									<li>[系統公告]中華電信小額支付系統預計於114/6/28(六)上午06點00分&nbsp;........... 
										<span class="date">114/06/18</span> 
										
									</li>
								</a>
							
								
								<a href="/pis/pia/client/readBulletinDetail?pkPaiBulletin=70001695" title="前往 &lt;台灣票據交換所公告&gt;114年06月份系統維護如下，維護期間無法使用該行帳戶進行繳費，不便之處，敬請見諒，謝謝!&lt;p&gt;彰化銀行114年06月04日(三) 17:00 ~ 18:00&lt;/p&gt;&lt;p&gt;兆豐銀行114年06月15日(日) 00:00 ~ 06:00&lt;/p&gt;&lt;p&gt;將來銀行114年06月07日(六) 01:00 ~ 08:00&lt;/p&gt;&lt;p&gt;臺灣企銀114年06月07日(六) 03:00 ~ 07:00&lt;/p&gt;&lt;p&gt;上海銀行114年06月05日(四) 11:30 ~ 12:00&lt;/p&gt;&lt;p&gt;樂天銀行114年06月21日(六) 01:00 ~ 08:00&lt;/p&gt;&lt;p&gt;臺灣銀行114年06月11日(三) 18:00 ~ 20:00&lt;/p&gt;&lt;p&gt;華南銀行114年06月15日(日) 00:00 ~ 08:00&lt;/p&gt;&lt;p&gt;京城銀行114年06月15日(日) 00:00 ~ 06:00&lt;/p&gt;&lt;p&gt;臺灣企銀114年06月14日(六) 03:00 ~ 07:00&lt;/p&gt;&lt;p&gt;彰化銀行114年06月19日(四) 17:00 ~ 19:00&lt;/p&gt;&lt;p&gt;華南銀行114年06月22日(日) 00:00 ~ 08:00　&lt;/p&gt;&lt;p&gt;彰化銀行114年06月23日(一) 17:00 ~ 18:30&lt;/p&gt;&lt;p&gt;樂天銀行114年06月26日(四) 18:30 ~ 19:30&lt;/p&gt;&lt;p&gt;合作金庫114年06月22日(日) 00:00 ~ 09:00&lt;/p&gt;&lt;p&gt;上海銀行114年06月22日(日) 01:00 ~ 10:00&lt;/p&gt;&lt;p&gt;彰化銀行114年06月21日(六) 10:00 ~ 13:00&lt;/p&gt;&lt;p&gt;將來銀行114年06月28日(六) 03:00 ~ 08:00&lt;/p&gt;">
									<li>[系統公告]<台灣票據交換所公告>114年06月份系統維護如下，維護期間無法&nbsp;........... 
										<span class="date">114/05/28</span> 
										
									</li>
								</a>
							
								
								<a href="/pis/pia/client/readBulletinDetail?pkPaiBulletin=70001680" title="前往 &lt;廠商端公告&gt;114年度「政府電子採購網操作說明及實機訓練」已開放報名，歡迎踴躍參加！

場次資訊如下：
1.高雄場，114/06/17(二) 09:00-12:00
2.高雄場，114/06/17(二) 13:30-16:30
3.桃園場，114/06/24(二) 13:30-16:30
4.台中場，114/06/25(三) 09:00-12:00
5.台中場，114/06/25(三) 13:30-16:30
6.台北場，114/06/26(四) 09:00-12:00
7.台北場，114/06/26(四) 13:30-16:30
8.花蓮場，114/06/27(五) 13:30-16:30
9.台北場，114/06/30(一) 09:00-12:00
10.台北場，114/06/30(一) 13:30-16:30

課程內容：
1. 採購網前置準備（廠商安裝程式環境檢測）
2. 採購網會員帳號免費申請、綁定憑證
3. 電子領標作業
4. 電子投標作業01-以「公開取得電子報價單公告」線上報價
5. 廠商投標資格雲端服務--線上申請「無退票記錄證明」及「納稅證明」
----------------------------------
6. 加值服務1 - 招標文件閱覽
7. 加值服務2 - 繳費方案介紹">
									<li>[活動消息]<廠商端公告>114年度「政府電子採購網操作說明及實機訓練」已開&nbsp;........... 
										<span class="date">114/05/19</span> 
										
									</li>
								</a>
							
								
								<a href="/pis/pia/client/readBulletinDetail?pkPaiBulletin=70001640" title="前往 &lt;機關端公告&gt;114年度教育訓練課程於114年5月9日下午2時起開放報名，名額有限，敬請把握！報名方式：請登入機關帳號、密碼，至「教育訓練/實體課程」查詢場次並報名。">
									<li>[最新消息]<機關端公告>114年度教育訓練課程於114年5月9日下午2時起&nbsp;........... 
										<span class="date">114/04/28</span> 
										
									</li>
								</a>
							
								
								<a href="/pis/pia/client/readBulletinDetail?pkPaiBulletin=70001572" title="前往 「電子採購作業辦法」部分條文，業經行政院工程會於中華民國114年3月27日以工程資字第1141500074號令修正發布，相關公文、修正條文、修正總說明及條文對照表請參考附件。">
									<li>[法規修訂]「電子採購作業辦法」部分條文，業經行政院工程會於中華民國114年&nbsp;........... 
										<span class="date">114/03/28</span> 
										
									</li>
								</a>
							
								
								<a href="/pis/pia/client/readBulletinDetail?pkPaiBulletin=70001435" title="前往 為避免發生爭議並保障廠商權益，招標機關辦理變更或補充招標文件，並於政府採購公報刊登更正公告者，其更正公告作業至遲應於截標日前一上班日17時30分前完成傳輸作業，俾利更正公告於截標日前公開於政府電子採購網使廠商知悉。">
									<li>[系統公告]為避免發生爭議並保障廠商權益，招標機關辦理變更或補充招標文件，並&nbsp;........... 
										<span class="date">114/01/03</span> 
										
									</li>
								</a>
							
								
								<a href="/pis/pia/client/readBulletinDetail?pkPaiBulletin=70000940" title="前往 &lt;系統公告&gt;政府電子採購網基於安全性考量，預計於114年1月1日起停止支援Windows 7和8作業系統，建議升級作業系統至Windows 10以上。">
									<li>[最新消息]<系統公告>政府電子採購網基於安全性考量，預計於114年1月1日&nbsp;........... 
										<span class="date">113/03/13</span> 
										
									</li>
								</a>
							
								
								<a href="/pis/pia/client/readBulletinDetail?pkPaiBulletin=70000690" title="前往 有關招標機關審查投標廠商納稅證明文件之相關疑義，請參閱下列「相關連結」。">
									<li>[系統公告]有關招標機關審查投標廠商納稅證明文件之相關疑義，請參閱下列「相關&nbsp;........... 
										<span class="date">112/05/23</span> 
										
									</li>
								</a>
							
								
								<a href="/pis/pia/client/readBulletinDetail?pkPaiBulletin=70000705" title="前往 有關機關審查廠商資格文件（無退票紀錄證明）疑義，請參閱下列「相關連結」。">
									<li>[系統公告]有關機關審查廠商資格文件（無退票紀錄證明）疑義，請參閱下列「相關&nbsp;........... 
										<span class="date">111/11/14</span> 
										
									</li>
								</a>
							
						
						
					
				</ul>
				<div class="col-xl-12 col-sm-12">
					<div class="main-btn text-center">
						<div class="btn w170" tabindex="0" onclick="location.href='/pis/pia/client/readBulletinClientList';">更多資訊</div>
					</div>
				</div>
			</div>
			<div id="checkSearchFailure" style="display: none; cursor: default; text-align: center;">
				<textarea id="failureMessage" title="failureMessage" readonly></textarea>
				<br>
				<div class="bt_cen2">
					<a href="##" onclick="$.unblockUI();" title="關閉">關閉</a>
				</div>
			</div>
			<div class="tab-pane fade active show" id="index-tab2a" role="tabpanel" aria-labelledby="index-tab2">
				<div style="display: inline-block; margin-left: 21px; margin-right: 21px; float: left">查詢方式</div>
				<div style="display: inline-block; width: 70%" class="form-check">
					<input type="radio" name="tenderLevel1" id="tenderLevel11" title="tenderLevel11"  checked="checked" onclick="changeTenderSearchWay('tenderBasicDivId')">
					<label for="tenderLevel11">基本</label>
					<input type="radio" name="tenderLevel1" id="tenderLevel12" title="tenderLevel12"  onclick="changeTenderSearchWay('tenderAdvancedDivId')">
					<label for="tenderLevel12">進階</label>
					<input type="radio" name="tenderLevel1" id="tenderLevel13" title="tenderLevel13"  onclick="changeTenderSearchWay('tenderUpdatedDivId')">
					<label for="tenderLevel13">更正公告</label>
				</div>
				<div id="tenderBasicDivId">
					







<style>
/* .dateArea { */
/* 	width: 17%; */
/* 	margin-right: 45px; */
/* 	display: inline-block; */
/* } */

/* .dateLine { */
/* 	width: 25px; */
/* 	margin-right: 0%; */
/* 	left: 0%; */
/* } */

/* .dateLabel { */
/* 	margin-right: 16px !important; */
/* } */

/* .fddd { */
/* 	width: 39%; */
/* 	position: relative; */
/* 	margin-right: 0%; */
/* } */

/* .g_form-date { */
/* 	width: 175px !important; */
/* } */
</style>


<form action="/prkms/tender/common/basic/readTenderBasic" id="basicTenderSearchForm" method="post" autocomplete="off">
	<input type="hidden" id="firstSearch" title="firstSearch" name="firstSearch" value="false" />
	<input type="hidden" id="searchType" title="searchType" name="searchType" value="basic" />
	<input type="hidden" id="isBinding" name="isBinding" title="isBinding" value="N" />
	<input type="hidden" id="isLogIn" name="isLogIn" title="isLogIn" value="N" />
	<div class="row pt10">
		<div class="col-xl-6 col-sm-12">
			<div class="flo" id='orgNameThId'>
				<label for="orgName">
					<span class="text-red">@</span>機關名稱：
				</label>
			</div>
			<div class="flo">
				<input type="text" class="form-control" id="orgName" title="機關名稱" name="orgName" value="" style="width:95%">
			</div>
			<div class="flo">
				<button type="submit" class="btn l-btn btn-block">
					<a href="/prkms/tender/common/orgName/indexTenderOrgName" tabindex="-1" title="查詢">查詢</a>
				</button>
			</div>
		</div>

		<div class="col-xl-6 col-sm-12 fix-l">
			<div class="flo" id="orgIdThId">
				<label for="orgId">
					<span class="text-reds"></span>機關代碼：
				</label>
			</div>
			<div class="flo">
				<input type="text" class="form-control" id="orgId" title="機關代碼" name="orgId" value="" style="width: 95%">
			</div>
			<div class="flo">
				<button type="submit" class="btn l-btn btn-block">
					<a href="/prkms/tender/common/orgName/indexTenderOrgName" tabindex="-1" title="查詢">查詢</a>
				</button>
			</div>
		</div>
		<div style="width: 100%; height: 10px; float: left"></div>
		<div class="col-xl-6 col-sm-12">
			<div class="flo" id="tenderNameThId">
				<label for="tenderName">
					<span class="text-red">@</span>標案名稱：
				</label>
			</div>
			<div class="flo">
				<input type="text" class="form-control" id="tenderName" title="標案名稱" name="tenderName" style="width: 95%">
			</div>
		</div>
		<div class="col-xl-6 col-sm-12">
			<div class="flo" id="tenderCaseNoThId">
				<label for="tenderId">
					<span class="text-reds"></span>標案案號：
				</label>
			</div>
			<div class="flo">
				<input type="text" class="form-control" id="tenderId" title="標案案號" name="tenderId" style="width: 95%">
			</div>
		</div>
		<div style="width: 100%; height: 10px; float: left"></div>
		<div class="col-xl-6 col-sm-12">
			<div class="flo" id="tenderTypeThId">
				<label for="name">
					<span class="text-red">＊</span>招標類型：
				</label>
			</div>
			<div class="flo">
				<select class="select-s01" id="tenderTypeSelect" title="招標類型" name="tenderType" onchange="changePisTenderType(event,'/prkms/tender/common/basic/readTenderBasic',true)">
					<option value="TENDER_DECLARATION">招標公告</option>

					<option value="SEARCH_APPEAL">公開徵求</option>

					<option value="PUBLIC_READ">公開閱覽</option>

					<option value="PREDICT">政府採購預告</option>
				</select>
			</div>
		</div>
		<div class="col-xl-6 col-sm-12">
			<div class="flo" id="tenderWayTitle">
				<label for="name">
					<span class="text-red">＊</span>招標方式：
				</label>
			</div>
			<div class="flo">
				<select class="select-s01" id="declarationSelect" title="招標方式" name="tenderWay">
					<option value="TENDER_WAY_ALL_DECLARATION">各式招標公告</option>
					<option value="TENDER_WAY_1">公開招標</option>
					<option value="TENDER_WAY_12">公開取得電子報價單</option>
					<option value="TENDER_WAY_2">公開取得報價單或企劃書</option>
					<option value="TENDER_WAY_4">經公開評選或公開徵求之限制性招標</option>
					<option value="TENDER_WAY_5">選擇性招標(建立合格廠商名單)</option>
					<option value="TENDER_WAY_7">選擇性招標(建立合格廠商名單後續邀標)</option>
					<option value="TENDER_WAY_3">選擇性招標(個案)</option>
					<option value="TENDER_WAY_10">電子競價</option>
					<option value="TENDER_WAY_6">限制性招標(未經公開評選或公開徵求)</option>
				</select>
			</div>
		</div>
		<div style="width: 100%; height: 10px; float: left"></div>
		<div class="col-xl-12 col-sm-12">
			<div class="flo" id="dateTypeTitle">
				<label for="name">
					<span class="text-red">＊</span>公告日期：
				</label>
			</div>
			<div class="flo">
				<div class="form-check form-check-inline">
					<input class="form-check-input" type="radio" name="basicDateType" id="basicIsNowDateTypeId" title="當日" checked="checked" value="isNow">
					<label class="form-check-label" for="basicIsNowDateTypeId">當日</label>
				</div>
				<div class="form-check form-check-inline">
					<input class="form-check-input" type="radio" name="basicDateType" id="basicIsSpdtDateTypeId" title="等標期內" value="isSpdt">
					<label class="form-check-label" for="basicIsSpdtDateTypeId">等標期內</label>
				</div>
				<div class="form-check form-check-inline">
					<input class="form-check-input" type="radio" name="basicDateType" id="basicIsDateDateTypeId" title="日期區間" value="isDate">
					<div id="tenderStartDateArea" class="dateArea"></div>
					<label for="endTime4">
						<span>—</span>
					</label>
					<div id="tenderEndDateArea" class="dateArea"></div>
				</div>
			</div>
		</div>
		<div style="width: 100%; height: 10px; float: left"></div>
		<div class="col-xl-12 col-sm-12">
			<div class="flo" id="radProctrgCateThId">
				<label for="name">
					<span class="text-red"></span>採購性質
				</label>
			</div>
			<div class="flo">
				<div class="form-check form-check-inline">
					<input class="form-check-input" type="radio" id="basicRadProctrgCate1" title="工程" name="basicRadProctrgCate" value="RAD_PROCTRG_CATE_1">
					<label class="form-check-label" for="basicRadProctrgCate1">工程</label>
				</div>
				<div class="form-check form-check-inline">
					<input class="form-check-input" type="radio" id="basicRadProctrgCate2" title="財物" name="basicRadProctrgCate" value="RAD_PROCTRG_CATE_2">
					<label class="form-check-label" for="basicRadProctrgCate2">財物</label>
				</div>
				<div class="form-check form-check-inline">
					<input class="form-check-input" type="radio" id="basicRadProctrgCate3" title="勞務" name="basicRadProctrgCate" value="RAD_PROCTRG_CATE_3">
					<label class="form-check-label" for="basicRadProctrgCate3">勞務</label>
				</div>
				<div class="form-check form-check-inline">
					<input class="form-check-input" type="radio" id="basicRadProctrgCate4" title="不限" name="basicRadProctrgCate" value="" checked>
					<label class="form-check-label" for="basicRadProctrgCate4">不限</label>
				</div>
			</div>
		</div>
		<div style="width: 100%; height: 10px; float: left"></div>
		<div class="col-xl-12 col-sm-12">
			<div class="flo" id="gpaOrAnztecOrAstepThId">
				<label for="name">
					<span class="text-red"></span>適用條約或協定：
				</label>
			</div>
			<div class="flo">
				<div class="Lb-1">
					<input type="checkbox" id="gpa" title="適用WTO政府採購協定(GPA)" name="gpa" value="true">
					<label for="gpa">適用WTO政府採購協定(GPA)</label>
				</div>
				<div class="Lb-1">
					<input type="checkbox" id="anztec" title="適用臺紐經濟合作協定(ANZTEC)" name="anztec" value="true">
					<label for="anztec">適用臺紐經濟合作協定(ANZTEC)</label>
				</div>
				<div class="Lb-1">
					<input type="checkbox" id="astep" title="適用臺星經濟夥伴協定(ASTEP)" name="astep" value="true">
					<label for="astep">適用臺星經濟夥伴協定(ASTEP)</label>
				</div>
			</div>
		</div>
		
		<div class="col-xl-12 col-sm-12" id="policyAdvocacyThId">
			<div class="flo">
				<label for="name">
					<span class="text-red"></span>是否為政策及業務宣導業務：
				</label>
			</div>
			<div class="flo">
				<div class="form-check form-check-inline">
					<input class="form-check-input" type="radio" name="policyAdvocacy" id="level_11" title="是否為政策及業務宣導業務" value="Y" >
					<label class="form-check-label" for="level_11">是</label>
				</div>
				<div class="form-check form-check-inline">
					<input class="form-check-input" type="radio" name="policyAdvocacy" id="level_12" title="是否為政策及業務宣導業務" value="N" >
					<label class="form-check-label" for="level_12">否</label>
				</div>
				<div class="form-check form-check-inline">
					<input class="form-check-input" type="radio" name="policyAdvocacy" id="level_14" title="是否為政策及業務宣導業務" value="" checked>
					<label class="form-check-label" for="level_14">不限</label>
				</div>
			</div>
		</div>
		
		<div class="col-xl-12 col-sm-12">
			<div class="main-btn text-center">
				<div class="btn w170" id="basicTenderSearchId" tabindex="0" onclick="basicTenderSearch();">查詢</div>
			</div>
		</div>
		<div style="width: 100%; height: 40px; float: left"></div>
		<div class="col-xl-12 col-sm-12">
			<div class="flo">
				<span>註：</span>
			</div>
			<div class="flo">
				<span class="text-red"><span class="red">◎</span></span>
				<span>符號<span style="color: red;">*</span>代表必填，<span style="color: red;">＠</span>代表可填入關鍵字。</span>
				<br>
				<span class="text-red"><span class="red">◎</span></span>
				<span>若查不到已公告的資料，表示此案正在進行更正公告中。</span>
				<br>
				<span class="text-red"><span class="red">◎</span></span>
				<span>若欲以細項標的分類(如84電腦及相關服務) 查詢者，請使用『標的分類查詢』功能。</span>
				<br>
				<span class="text-red"><span class="red">◎</span></span>
				<span>國防部軍備局新增1個機關，生產製造中心機關代碼為**********。</span>
				<br>
				<span class="text-red"><span class="red">◎</span></span>
				<span>功能使用請參考：首頁&gt;學習資源&gt;線上教學。</span>
			</div>
		</div>
	</div>
</form>
<!--表格結束-->

<script type="text/javascript">
	var basicTenderComponent1;
	var basicTenderComponent2;

	$(document).ready(function () {
		basicTenderCommonInit();
		basicTenderComponent1.rePosition();
		basicTenderComponent2.rePosition();
	});
	
    function basicTenderSearchCase(caseVal) {
        $("input[name=basicDateType][value='"+caseVal+"']").prop("checked",true);
        basicTenderSearch();
    }

	function basicTenderSearch() {
		if ($("#basicTenderSearchForm input[name='basicDateType']:checked").val() == 'isDate' && $('#tenderId').val() == '') {
			if ($("#basicTenderSearchForm input[name='tenderStartDate']").val() == "" || $("#basicTenderSearchForm input[name='tenderEndDate']").val() == "") {
				showCheckFailure("請選擇公告日期");
				return;
			}

			var startDate;
			var endDate;
			switch ($("#basicTenderSearchForm select[name='tenderType']").val()) {
				case 'TENDER_DECLARATION':
					startDate = parseInt($("#basicTenderSearchForm input[name='tenderStartDate']").val().replace(/\//g, ""), 10);
					endDate = parseInt($("#basicTenderSearchForm input[name='tenderEndDate']").val().replace(/\//g, ""), 10);
					break;
				case 'SEARCH_APPEAL':
					startDate = parseInt($("#basicTenderSearchForm input[name='startDate']").val().replace(/\//g, ""), 10);
					endDate = parseInt($("#basicTenderSearchForm input[name='endDate']").val().replace(/\//g, ""), 10);
					break;
				case 'PUBLIC_READ':
					startDate = parseInt($("#basicTenderSearchForm input[name='queryStartDate']").val().replace(/\//g, ""), 10);
					endDate = parseInt($("#basicTenderSearchForm input[name='queryEndDate']").val().replace(/\//g, ""), 10);
					break;
				case 'PREDICT':
					startDate = parseInt($("#basicTenderSearchForm input[name='predictNoticeDateStart']").val().replace(/\//g, ""), 10);
					endDate = parseInt($("#basicTenderSearchForm input[name='predictNoticeDateEnd']").val().replace(/\//g, ""), 10);
					break;
			}

			if (startDate > endDate) {
				showCheckFailure("公告日期 起始日期大於終止日期");
				return;
			}

			var y1 = Math.floor(startDate / 10000) + 1911;
			var m1 = Math.floor(startDate % 10000 / 100);
			var d1 = startDate % 100;
			var date1 = new Date(y1 + "-" + m1 + "-" + d1);
			var y2 = Math.floor(endDate / 10000) + 1911;
			var m2 = Math.floor(endDate % 10000 / 100);
			var d2 = endDate % 100;
			var date2 = new Date(y2 + "-" + m2 + "-" + d2);
			var iDays = parseInt(Math.abs(date1 - date2) / 1000 / 60 / 60 / 24);

			var isBinding = $('#isBinding').val();
			var isLogIn = $('#isLogIn').val();
			if(null == isBinding || '' == isBinding){
				isBinding = 'N';
				$('#isBinding').val('N');
			}
			if(null == isLogIn || '' == isLogIn){
				isLogIn = 'N';
				$('#isLogIn').val('N');
			}
			
			//未登入 未綁定
			if(iDays > 186 && "Y" != isBinding && "Y" != isLogIn) {
				window.location="/prkms/tender/common/bulletion/indexBulletion?showMessage=Y";
				return;
			}
			//登入 未綁定
			else if (iDays > 365 && "Y" != isBinding && "Y" == isLogIn){
				window.location="/prkms/tender/common/bulletion/indexBulletion?showMessage=Y";
				return;
			}
			//other 登入 綁定
			else if(iDays > 730 && "Y" == isBinding && "Y" == isLogIn){
				window.location="/prkms/tender/common/bulletion/indexBulletion?showMessage=Y";
				return;
			}
		}

		$("#basicTenderSearchForm input[name='basicRadProctrgCate']").attr("name", "radProctrgCate");
		$("#basicTenderSearchForm input[name='basicDateType']").attr("name", "dateType");
		$('#basicTenderSearchForm input[name="firstSearch"]').val("true");
//		$('#basicTenderSearchForm').submit();
		//改用get 送出
        window.location = $('#basicTenderSearchForm').attr("action") + "?" + $('#basicTenderSearchForm').serialize();	
	}

	function basicTenderCommonInit() {
		//初始化時間開始
		var strOfTenderBasicStartDateInit = getDateCommonUtilInitDate(new Date(), -6);
		var strOfTenderBasicEndDateInit = getDateCommonUtilInitDate(new Date(), 0);
		//初始化時間結束

		var tenderQueryConfigBasicA = {
			element: $('#basicTenderSearchForm #tenderStartDateArea')[0],
			type: 'yyy/MM/dd',
			defaultValue: strOfTenderBasicStartDateInit,
			attr: { id: 'tenderStartDate', name: 'tenderStartDate', class: 'form-date g_form-date' }
		}
		basicTenderComponent1 = new Geps3.DatePicker(tenderQueryConfigBasicA);

		var tenderQueryConfigBasicAa = {
			element: $('#basicTenderSearchForm #tenderEndDateArea')[0],
			type: 'yyy/MM/dd',
			defaultValue: strOfTenderBasicEndDateInit,
			attr: { id: 'tenderEndDate', name: 'tenderEndDate', class: 'form-date g_form-date' }
		}
		basicTenderComponent2 = new Geps3.DatePicker(tenderQueryConfigBasicAa);

// 		$('.f_20').css({
// 			"width": "100%"
// 		});

	}

	$("#basicTenderSearchId").keypress(function (e) {
	    if (e.which == 13) {
	    	basicTenderSearch();
	    } 
	});

</script>
				</div>
				<div id="tenderAdvancedDivId" style="display: none">
					







<form action="/prkms/tender/common/advanced/readTenderAdvanced" id="advancedTenderSearchForm" method="post" autocomplete="off">
	<input type="hidden" id="firstSearch" title="firstSearch" name="firstSearch" value="false" />
	<input type="hidden" id="searchType" title="searchType" name="searchType" value="advanced" />
	<input type="hidden" id="isBinding" name="isBinding" title="isBinding" value="N" />
	<input type="hidden" id="isLogIn" name="isLogIn" title="isLogIn" value="N" />
	
	<div class="row pt10">
		<div class="col-xl-6 col-sm-12">
			<div class="flo" id='orgNameThId'>
				<label for="orgName">
					<span class="text-red">@</span>機關名稱：
				</label>
			</div>
			<div class="flo">
				<input type="text" class="form-control" id="orgName" title="機關名稱" name="orgName" value="" style="width:95%" >
			</div>
			<div class="flo">
				<button type="submit" class="btn l-btn btn-block">
					<a href="/prkms/tender/common/orgName/indexTenderOrgName?searchType=advanced" tabindex="-1" title="查詢">查詢</a>
				</button>
			</div>
		</div>

		<div class="col-xl-6 col-sm-12 fix-l">
			<div class="flo" id="orgIdThId">
				<label for="depnb">
					<span class="text-reds"></span>機關代碼：
				</label>
			</div>
			<div class="flo">
				<input type="text" class="form-control" id="orgId" title="機關代碼" name="orgId" value="" style="width:95%" >
			</div>
			<div class="flo">
				<button type="submit" class="btn l-btn btn-block">
					<a href="/prkms/tender/common/orgName/indexTenderOrgName?searchType=advanced" tabindex="-1" title="查詢">查詢</a>
				</button>
			</div>
		</div>
		<div style="width: 100%; height: 10px; float: left"></div>
		<div class="col-xl-6 col-sm-12">
			<div class="flo" id="tenderNameThId">
				<label for="tenderName">
					<span class="text-red">@</span>標案名稱：
				</label>
			</div>
			<div class="flo">
				<input type="text" class="form-control" id="tenderName" title="標案名稱" name="tenderName" style="width:95%" >
			</div>
		</div>
		<div class="col-xl-6 col-sm-12">
			<div class="flo" id="tenderCaseNoThId">
				<label for="tenderId">
					<span class="text-reds"></span>標案案號：
				</label>
			</div>
			<div class="flo">
				<input type="text" class="form-control" id="tenderId" title="標案案號" name="tenderId" style="width:95%" >
			</div>
		</div>
		<div style="width: 100%; height: 10px; float: left"></div>
		<div class="col-xl-6 col-sm-12">
			<div class="flo">
				<label for="name">
					<span class="text-red">＊</span>招標類型：
				</label>
			</div>
			<div class="flo">
				<select class="select-s01" id="tenderTypeSelect" title="招標類型" name="tenderType" onchange="changePisTenderType(event,'/prkms/tender/common/advanced/readTenderAdvanced',true)">
					<option value="TENDER_DECLARATION">招標公告</option>

					<option value="SEARCH_APPEAL">公開徵求</option>

					<option value="PUBLIC_READ">公開閱覽</option>

					<option value="PREDICT">政府採購預告</option>
				</select>
			</div>
		</div>
		<div class="col-xl-6 col-sm-12">
			<div class="flo" id="tenderWayTitle">
				<label for="name">
					<span class="text-red">＊</span>招標方式：
				</label>
			</div>
			<div class="flo">
				<select class="select-s01" id="declarationSelect" title="招標方式" name="tenderWay">
					<option value="TENDER_WAY_ALL_DECLARATION">各式招標公告</option>
					<option value="TENDER_WAY_1">公開招標</option>
					<option value="TENDER_WAY_12">公開取得電子報價單</option>
					<option value="TENDER_WAY_2">公開取得報價單或企劃書</option>
					<option value="TENDER_WAY_4">經公開評選或公開徵求之限制性招標</option>
					<option value="TENDER_WAY_5">選擇性招標(建立合格廠商名單)</option>
					<option value="TENDER_WAY_7">選擇性招標(建立合格廠商名單後續邀標)</option>
					<option value="TENDER_WAY_3">選擇性招標(個案)</option>
					<option value="TENDER_WAY_10">電子競價</option>
					<option value="TENDER_WAY_6">限制性招標(未經公開評選或公開徵求)</option>
				</select>
			</div>
		</div>
		<div style="width: 100%; height: 10px; float: left"></div>
		<div class="col-xl-12 col-sm-12">
			<div class="flo" id="dateTypeTitle">
				<label for="name">
					<span class="text-red">＊</span>公告日期：
				</label>
			</div>
			<div class="flo">
				<div class="form-check form-check-inline">
					<input class="form-check-input" type="radio" name="advancedDateType" id="advancedIsNowDateTypeId" title="advancedIsNowDateTypeId" checked="checked" value="isNow" >
					<label class="form-check-label" for="advancedIsNowDateTypeId">當日</label>
				</div>
				<div class="form-check form-check-inline">
					<input class="form-check-input" type="radio" name="advancedDateType" id="advancedIsSpdtDateTypeId" title="advancedIsSpdtDateTypeId" value="isSpdt" >
					<label class="form-check-label" for="advancedIsSpdtDateTypeId">等標期內</label>
				</div>
				<div class="form-check form-check-inline">
					<input class="form-check-input" type="radio" name="advancedDateType" id="advancedIsDateDateTypeId" title="advancedIsDateDateTypeId" value="isDate" >
					<div id="tenderStartDateArea" class="dateArea"></div>
					<label for="endTime4">
						<span>—</span>
					</label>
					<div id="tenderEndDateArea" class="dateArea"></div>
				</div>
			</div>
		</div>
		<div style="width: 100%; height: 10px; float: left"></div>
		<div class="col-xl-12 col-sm-12" id="spdtArea">
			<div class="flo">
				<label for="name">
					<span class="text-red"></span>截止投標：
				</label>
			</div>
			<div class="flo">
				<div class="form-check form-check-inline">
					<div id="spdtStartDateArea" class="dateArea"></div>
					<div style=" position: relative; display: inline-block">—</div>
					<div id="spdtEndDateArea" class="dateArea"></div>
				</div>
			</div>
		</div>
		<div style="width: 100%; height: 10px; float: left"></div>
		<div class="col-xl-12 col-sm-12" id="spdtArea">
			<div class="flo">
				<label for="name">
					<span class="text-red"></span>開標時間：
				</label>
			</div>
			<div class="flo">
				<div class="form-check form-check-inline">
					<div id="opdtStartDateArea" class="dateArea"></div>
					<div style="position: relative; display: inline-block">—</div>
					<div id="opdtEndDateArea" class="dateArea"></div>
				</div>
			</div>
		</div>
		
		
		<div style="width: 100%; height: 10px; float: left"></div>
		<div class="col-xl-6 col-sm-12" id="predictDateArea" style="display: none;">
			<div class="flo">
				<label for="name">
					<span class="text-red"></span>預定招標<br>年月
				</label>
			</div>
			<div class="flo">
				請輸入年/月 <select name="tenderYmStartY" title="請輸入年月" style="width: 12%;">
					<option></option>
					
						<option value="089">089
					
						<option value="090">090
					
						<option value="091">091
					
						<option value="092">092
					
						<option value="093">093
					
						<option value="094">094
					
						<option value="095">095
					
						<option value="096">096
					
						<option value="097">097
					
						<option value="098">098
					
						<option value=" 099"> 099
					
						<option value="100">100
					
						<option value="101">101
					
						<option value="102">102
					
						<option value="103">103
					
						<option value="104">104
					
						<option value="105">105
					
						<option value="106">106
					
						<option value="107">107
					
						<option value="108">108
					
						<option value="
				109">
				109
					
						<option value="110">110
					
						<option value="111">111
					
						<option value="112">112
					
						<option value="113">113
					
						<option value="114">114
					
						<option value="115">115
					
						<option value="116">116
					
						<option value="117">117
					
						<option value="118">118
					
						<option value=" 119"> 119
					
						<option value="120">120
					
						<option value="121">121
					
						<option value="122">122
					
						<option value="123">123
					
						<option value="124">124
					
						<option value="125">125
					
						<option value="126">126
					
						<option value="127">127
					
						<option value="128">128
					
						<option value="
				129">
				129
					
						<option value="130">130
					
				</select> <select name="tenderYmStartM" title="請輸入年月" style="width: 12%;">
					<option></option>
					
						<option value="01">01
					
						<option value="02">02
					
						<option value="03">03
					
						<option value="04">04
					
						<option value="05">05
					
						<option value="06">06
					
						<option value="07">07
					
						<option value="08">08
					
						<option value="09">09
					
						<option value="10">10
					
						<option value="11">11
					
						<option value="12">12
					
				</select> －請輸入年/月 <select name="tenderYmEndY" title="請輸入年月" style="width: 12%;">
					<option></option>
					
						<option value="089">089
					
						<option value="090">090
					
						<option value="091">091
					
						<option value="092">092
					
						<option value="093">093
					
						<option value="094">094
					
						<option value="095">095
					
						<option value="096">096
					
						<option value="097">097
					
						<option value="098">098
					
						<option value=" 099"> 099
					
						<option value="100">100
					
						<option value="101">101
					
						<option value="102">102
					
						<option value="103">103
					
						<option value="104">104
					
						<option value="105">105
					
						<option value="106">106
					
						<option value="107">107
					
						<option value="108">108
					
						<option value="
				109">
				109
					
						<option value="110">110
					
						<option value="111">111
					
						<option value="112">112
					
						<option value="113">113
					
						<option value="114">114
					
						<option value="115">115
					
						<option value="116">116
					
						<option value="117">117
					
						<option value="118">118
					
						<option value=" 119"> 119
					
						<option value="120">120
					
						<option value="121">121
					
						<option value="122">122
					
						<option value="123">123
					
						<option value="124">124
					
						<option value="125">125
					
						<option value="126">126
					
						<option value="127">127
					
						<option value="128">128
					
						<option value="
				129">
				129
					
						<option value="130">130
					
				</select> <select name="tenderYmEndM" title="請輸入年月" style="width: 12%;">
					<option></option>
					
						<option value="01">01
					
						<option value="02">02
					
						<option value="03">03
					
						<option value="04">04
					
						<option value="05">05
					
						<option value="06">06
					
						<option value="07">07
					
						<option value="08">08
					
						<option value="09">09
					
						<option value="10">10
					
						<option value="11">11
					
						<option value="12">12
					
				</select>
			</div>
		</div>
		<div style="width: 100%; height: 10px; float: left"></div>
		<div class="col-xl-12 col-sm-12" id="radProctrgCateThId">
			<div class="flo">
				<label for="name">
					<span class="text-red"></span>採購性質：
				</label>
			</div>
			<div class="flo">
				<div class="form-check form-check-inline">
					<input class="form-check-input" type="radio" id="advancedRadProctrgCate1" title="advancedRadProctrgCate1" name="advancedRadProctrgCate" value="RAD_PROCTRG_CATE_1" >
					<label class="form-check-label" for="advancedRadProctrgCate1">工程</label>
				</div>
				<div class="form-check form-check-inline">
					<input class="form-check-input" type="radio" id="advancedRadProctrgCate2" title="advancedRadProctrgCate2" name="advancedRadProctrgCate" value="RAD_PROCTRG_CATE_2" >
					<label class="form-check-label" for="advancedRadProctrgCate2">財物</label>
				</div>
				<div class="form-check form-check-inline">
					<input class="form-check-input" type="radio" id="advancedRadProctrgCate3" title="advancedRadProctrgCate3" name="advancedRadProctrgCate" value="RAD_PROCTRG_CATE_3" >
					<label class="form-check-label" for="advancedRadProctrgCate3">勞務</label>
				</div>
				<div class="form-check form-check-inline">
					<input class="form-check-input" type="radio" id="advancedRadProctrgCate4" title="advancedRadProctrgCate4" name="advancedRadProctrgCate" value="" checked>
					<label class="form-check-label" for="advancedRadProctrgCate4">不限</label>
				</div>
			</div>
		</div>
		<div style="width: 100%; height: 10px; float: left"></div>
		<div class="col-xl-12 col-sm-12" id="tenderRangeThId">
			<div class="flo">
				<label for="name">
					<span class="text-red"></span>採購級距：
				</label>
			</div>
			<div class="flo">
				<select class="select-s01" name="tenderRange" id="" title="採購級距選擇">
					<option value="TENDER_RANGE_ALL">不限</option>
					<option value="TENDER_RANGE_1">未達公告金額</option>
					<option value="TENDER_RANGE_2">公告金額以上未達查核金額</option>
					<option value="TENDER_RANGE_3">查核金額以上未達巨額</option>
					<option value="TENDER_RANGE_4">巨額</option>
				</select>
			</div>
		</div>
		<div style="width: 100%; height: 10px; float: left"></div>
		<div class="col-xl-12 col-sm-12" id="budgetThId">
			<div class="flo">
				<label for="name">
					<span class="text-red"></span>預算金額：
				</label>
			</div>
			<div class="flo">
				<input type="hidden" name="minBudget" id="minBudget" title="minBudget" >
				<div id="minBudgetArea" class="dateArea" style="width: 34%;display: inline-block"></div>
				<div style="display: inline-block">—</div>
				<input type="hidden" name="maxBudget" id="maxBudget" title="maxBudget" >
				<div id="maxBudgetArea" class="dateArea" style="width: 34%;display: inline-block"></div>
			</div>
		</div>
		<div style="width: 100%; height: 10px; float: left"></div>
		<div class="col-xl-12 col-sm-12" id="execLocationThId">
			<div class="flo">
				<label for="name">
					<span class="text-red"></span>履約地點：
				</label>
			</div>
			<div class="flo">
				<select class="select-s01" id="execLocation" title="execLocation" name="execLocation">
					<option value="">不限</option>
					<option value="EXECUTE_LOCATION_1">基隆市(非原住民地區)</option>
					<option value="EXECUTE_LOCATION_2">臺北市(非原住民地區)</option>
					<option value="EXECUTE_LOCATION_20000200">新北市(非原住民地區)</option>
					<option value="EXECUTE_LOCATION_20000201">新北市烏來區(原住民地區)</option>
					<option value="EXECUTE_LOCATION_50003000">桃園市(非原住民地區)</option>
					<option value="EXECUTE_LOCATION_50003001">桃園市復興區(原住民地區)</option>
					<option value="EXECUTE_LOCATION_7">新竹市(非原住民地區)</option>
					<option value="EXECUTE_LOCATION_8">新竹縣(非原住民地區)</option>
					<option value="EXECUTE_LOCATION_9">新竹縣關西鎮(原住民地區)</option>
					<option value="EXECUTE_LOCATION_10">新竹縣五峰鄉(原住民地區)</option>
					<option value="EXECUTE_LOCATION_11">新竹縣尖石鄉(原住民地區)</option>
					<option value="EXECUTE_LOCATION_12">苗栗縣(非原住民地區)</option>
					<option value="EXECUTE_LOCATION_13">苗栗縣南庄鄉(原住民地區)</option>
					<option value="EXECUTE_LOCATION_14">苗栗縣泰安鄉(原住民地區)</option>
					<option value="EXECUTE_LOCATION_15">苗栗縣獅潭鄉(原住民地區)</option>
					<option value="EXECUTE_LOCATION_16">臺中市(非原住民地區)</option>
					<option value="EXECUTE_LOCATION_20000202">臺中市和平區(原住民地區)</option>
					<option value="EXECUTE_LOCATION_19">南投縣(非原住民地區)</option>
					<option value="EXECUTE_LOCATION_20">南投縣信義鄉(原住民地區)</option>
					<option value="EXECUTE_LOCATION_21">南投縣仁愛鄉(原住民地區)</option>
					<option value="EXECUTE_LOCATION_22">南投縣魚池鄉(原住民地區)</option>
					<option value="EXECUTE_LOCATION_23">彰化縣(非原住民地區)</option>
					<option value="EXECUTE_LOCATION_24">雲林縣(非原住民地區)</option>
					<option value="EXECUTE_LOCATION_25">嘉義市(非原住民地區)</option>
					<option value="EXECUTE_LOCATION_26">嘉義縣(非原住民地區)</option>
					<option value="EXECUTE_LOCATION_27">嘉義縣阿里山鄉(原住民地區)</option>
					<option value="EXECUTE_LOCATION_28">臺南市(非原住民地區)</option>
					<option value="EXECUTE_LOCATION_30">高雄市(非原住民地區)</option>
					<option value="EXECUTE_LOCATION_20000203">高雄市那瑪夏區(原住民地區)</option>
					<option value="EXECUTE_LOCATION_20000204">高雄市茂林區(原住民地區)</option>
					<option value="EXECUTE_LOCATION_20000205">高雄市桃源區(原住民地區)</option>
					<option value="EXECUTE_LOCATION_35">屏東縣(非原住民地區)</option>
					<option value="EXECUTE_LOCATION_36">屏東縣三地門鄉(原住民地區)</option>
					<option value="EXECUTE_LOCATION_37">屏東縣牡丹鄉(原住民地區)</option>
					<option value="EXECUTE_LOCATION_38">屏東縣來義鄉(原住民地區)</option>
					<option value="EXECUTE_LOCATION_39">屏東縣春日鄉(原住民地區)</option>
					<option value="EXECUTE_LOCATION_40">屏東縣泰武鄉(原住民地區)</option>
					<option value="EXECUTE_LOCATION_41">屏東縣獅子鄉(原住民地區)</option>
					<option value="EXECUTE_LOCATION_20000206">屏東縣滿州鄉(原住民地區)</option>
					<option value="EXECUTE_LOCATION_43">屏東縣瑪家鄉(原住民地區)</option>
					<option value="EXECUTE_LOCATION_44">屏東縣霧台鄉(原住民地區)</option>
					<option value="EXECUTE_LOCATION_45">宜蘭縣(非原住民地區)</option>
					<option value="EXECUTE_LOCATION_46">宜蘭縣大同鄉(原住民地區)</option>
					<option value="EXECUTE_LOCATION_47">宜蘭縣南澳鄉(原住民地區)</option>
					<option value="EXECUTE_LOCATION_48">花蓮縣(原住民地區)</option>
					<option value="EXECUTE_LOCATION_20000008">臺東縣綠島鄉(非原住民地區)</option>
					<option value="EXECUTE_LOCATION_50">臺東縣大武鄉(原住民地區)</option>
					<option value="EXECUTE_LOCATION_51">臺東縣太麻里鄉(原住民地區)</option>
					<option value="EXECUTE_LOCATION_52">臺東縣台東市(原住民地區)</option>
					<option value="EXECUTE_LOCATION_53">臺東縣成功鎮(原住民地區)</option>
					<option value="EXECUTE_LOCATION_54">臺東縣池上鄉(原住民地區)</option>
					<option value="EXECUTE_LOCATION_55">臺東縣卑南鄉(原住民地區)</option>
					<option value="EXECUTE_LOCATION_56">臺東縣延平鄉(原住民地區)</option>
					<option value="EXECUTE_LOCATION_57">臺東縣東河鄉(原住民地區)</option>
					<option value="EXECUTE_LOCATION_58">臺東縣金峰鄉(原住民地區)</option>
					<option value="EXECUTE_LOCATION_59">臺東縣長濱鄉(原住民地區)</option>
					<option value="EXECUTE_LOCATION_60">臺東縣海端鄉(原住民地區)</option>
					<option value="EXECUTE_LOCATION_61">臺東縣鹿野鄉(原住民地區)</option>
					<option value="EXECUTE_LOCATION_62">臺東縣達仁鄉(原住民地區)</option>
					<option value="EXECUTE_LOCATION_63">臺東縣關山鎮(原住民地區)</option>
					<option value="EXECUTE_LOCATION_64">臺東縣蘭嶼鄉(原住民地區)</option>
					<option value="EXECUTE_LOCATION_20000006">金門縣(非原住民地區)</option>
					<option value="EXECUTE_LOCATION_65">澎湖縣(非原住民地區)</option>
					<option value="EXECUTE_LOCATION_66">連江縣(非原住民地區)</option>
					<option value="EXECUTE_LOCATION_20000007">其他</option>
					<option value="EXECUTE_LOCATION_42">屏東縣滿洲鄉(原住民地區)</option>
					<option value="EXECUTE_LOCATION_3">臺北縣(非原住民地區)</option>
					<option value="EXECUTE_LOCATION_4">臺北縣烏來鄉(原住民地區)</option>
					<option value="EXECUTE_LOCATION_17">臺中縣(非原住民地區)</option>
					<option value="EXECUTE_LOCATION_18">臺中縣和平鄉(原住民地區)</option>
					<option value="EXECUTE_LOCATION_29">臺南縣(非原住民地區)</option>
					<option value="EXECUTE_LOCATION_31">高雄縣(非原住民地區)</option>
					<option value="EXECUTE_LOCATION_33">高雄縣茂林鄉(原住民地區)</option>
					<option value="EXECUTE_LOCATION_34">高雄縣桃源鄉(原住民地區)</option>
					<option value="EXECUTE_LOCATION_20000004">高雄縣那瑪夏鄉(原住民區)</option>
					<option value="EXECUTE_LOCATION_5">桃園縣(非原住民地區)</option>
					<option value="EXECUTE_LOCATION_6">桃園縣復興鄉(原住民地區)</option>
				</select>
			</div>
		</div>
		<div style="width: 100%; height: 10px; float: left"></div>
		<div class="col-xl-12 col-sm-12" id="priorityCateThId">
			<div class="flo">
				<label for="name">
					<span class="text-red"></span>優先採購分類：
				</label>
			</div>
			<div class="flo">
				<select class="select-s01" id="priorityCate" title="priorityCate" name="priorityCate">
					<option value="">不限</option>
					<option value="PRIORITY_CATE_1">食品</option>
					<option value="PRIORITY_CATE_13">手工藝品</option>
					<option value="PRIORITY_CATE_25">清潔用品</option>
					<option value="PRIORITY_CATE_27">園藝產品</option>
					<option value="PRIORITY_CATE_32">輔助器具</option>
					<option value="PRIORITY_CATE_34">家庭用品</option>
					<option value="PRIORITY_CATE_39">印刷</option>
					<option value="PRIORITY_CATE_44">清潔服務</option>
					<option value="PRIORITY_CATE_48">飲食服務</option>
					<option value="PRIORITY_CATE_52">洗車服務</option>
					<option value="PRIORITY_CATE_54">洗衣服務</option>
					<option value="PRIORITY_CATE_58">客服服務</option>
					<option value="PRIORITY_CATE_63">代工服務</option>
					<option value="PRIORITY_CATE_67">演藝服務</option>
					<option value="PRIORITY_CATE_70">交通服務</option>
					<option value="PRIORITY_CATE_72">其他</option>
				</select>
			</div>
		</div>
		<div style="width: 100%; height: 10px; float: left"></div>
		<div class="col-xl-12 col-sm-12" id="radReConstructThId">
			<div class="flo">
				<label for="name">
					<span class="text-red"></span>災區重建工程：
				</label>
			</div>
			<div class="flo">
				<div class="form-check form-check-inline">
					<input class="form-check-input" type="radio" name="radReConstruct" id="level_9" title="level_9" value="Y" >
					<label class="form-check-label" for="level_9">是</label>
				</div>
				<div class="form-check form-check-inline">
					<input class="form-check-input" type="radio" name="radReConstruct" id="level_10" title="level_10" value="N" >
					<label class="form-check-label" for="level_10">否</label>
				</div>
				<div class="form-check form-check-inline">
					<input class="form-check-input" type="radio" name="radReConstruct" id="level_10_1" title="level_10_1" value="" checked>
					<label class="form-check-label" for="level_10_1">不限</label>
				</div>
			</div>
		</div>
		<div class="col-xl-12 col-sm-12" id="policyAdvocacyThId">
			<div class="flo">
				<label for="name">
					<span class="text-red"></span>是否為政策及業務宣導業務：
				</label>
			</div>
			<div class="flo">
				<div class="form-check form-check-inline">
					<input class="form-check-input" type="radio" name="policyAdvocacy" id="level_11" title="是否為政策及業務宣導業務" value="Y" >
					<label class="form-check-label" for="level_11">是</label>
				</div>
				<div class="form-check form-check-inline">
					<input class="form-check-input" type="radio" name="policyAdvocacy" id="level_12" title="是否為政策及業務宣導業務" value="N" >
					<label class="form-check-label" for="level_12">否</label>
				</div>
				<div class="form-check form-check-inline">
					<input class="form-check-input" type="radio" name="policyAdvocacy" id="level_14" title="是否為政策及業務宣導業務" value="" checked>
					<label class="form-check-label" for="level_14">不限</label>
				</div>
			</div>
		</div>
		
		<div class="col-xl-12 col-sm-12" id="isCppArea">
			<div class="flo">
				<label for="name">
					<span class="text-red"></span>是否屬共同供應契約採購：
				</label>
			</div>
			<div class="flo">
				<div class="form-check form-check-inline">
					<input class="form-check-input" type="radio" name="isCpp" id="level_15" title="是否屬共同供應契約採購" value="Y" >
					<label class="form-check-label" for="level_15">是</label>
				</div>
				<div class="form-check form-check-inline">
					<input class="form-check-input" type="radio" name="isCpp" id="level_16" title="是否屬共同供應契約採購" value="N" >
					<label class="form-check-label" for="level_16">否</label>
				</div>
				<div class="form-check form-check-inline">
					<input class="form-check-input" type="radio" name="isCpp" id="level_17"  title="是否屬共同供應契約採購" value="" checked>
					<label class="form-check-label" for="level_17">不限</label>
				</div>
			</div>
		</div>		
		
		<div style="width: 100%; height: 10px; float: left"></div>
		<div class="col-xl-12 col-sm-12">
			<div class="flo">
				<label for="name">
					<span class="text-red"></span>適用條約或協定：
				</label>
			</div>
			<div class="flo">
				<div class="Lb-1">
					<input type="checkbox" name="GPA" id="GPA" title="適用WTO政府採購協定(GPA)" >
					<label for="GPA">適用WTO政府採購協定(GPA)</label>
				</div>
				<div class="Lb-1">
					<input type="checkbox" name="ANZTEC" id="ANZTEC" title="適用臺紐經濟合作協定(ANZTEC)" >
					<label for="ANZTEC">適用臺紐經濟合作協定(ANZTEC)</label>
				</div>
				<div class="Lb-1">
					<input type="checkbox" name="ASTEP" id="ASTEP" title="適用臺星經濟夥伴協定(ASTEP)" >
					<label for="ASTEP">適用臺星經濟夥伴協定(ASTEP)</label>
				</div>
			</div>
		</div>
		<div class="col-xl-12 col-sm-12">
			<div class="main-btn text-center">
				<div class="btn w170" onclick="advancedTenderSearch();">查詢</div>
			</div>
		</div>
		<div style="width: 100%; height: 40px; float: left"></div>
			<div class="col-xl-12 col-sm-12">		
			<div class="flo">
				<span class="text-w">註：</span><span class="text-red"> <span class="red">◎</span></span>
				<span>符號<span style="color: red;">*</span>代表必填，<span style="color: red;">＠</span>代表可填入關鍵字。</span>
				<br>
				<span class="text-w"></span><span class="text-red"> <span class="red">◎</span></span>
				<span>若查不到已公告的資料，表示此案正在進行更正公告中。</span>
				<br>
				<span class="text-w"></span><span class="text-red"> <span class="red">◎</span></span>
				<span>若欲以細項標的分類(如84電腦及相關服務) 查詢者，請使用『標的分類查詢』功能。</span>
				<br>
				<span class="text-w"></span><span class="text-red"> <span class="red">◎</span></span>
				<span>標案查詢不含公開徵求廠商提供參考資料及公開閱覽之案件，如欲查詢此類案件請於左方功能選單</span>
				<br>
				<span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;【查詢服務】中進行查詢。</span>
				<br>
				<span class="text-w"></span><span class="text-red"> <span class="red">◎</span></span>
				<span>非刊登於政府採購公報之公開取得公告資訊，於機關上傳當日公告於網路，因索引檔產生需時較久</span>
				<br>
				<span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;，資料無法同步查得。索引檔更新時間為：03：30、12：20、18：00(更新時間會依系統效能因素</span>
				<br>
				<span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;而變動)。</span>
				<br>
				<span class="text-w"></span><span class="text-red"> <span class="red">◎</span></span>
				<span>國防部軍備局新增1個機關，生產製造中心機關代碼為**********。</span>
				<br>
				<span class="text-w"></span><span class="text-red"> <span class="red">◎</span></span>
				<span>功能使用請參考：首頁&gt;學習資源&gt;線上教學。</span>
				<br>
				<span class="text-w"></span><span class="text-red"> <span class="red">◎</span></span>
				<span>「是否屬共同供應契約採購」查詢條件，僅提供「公告日期」為112年7月1日之後使用。</span>
			</div>
			
		</div>
	</div>

</form>
<!--表格結束-->


<!--左邊內容結束-->
<script type="text/javascript">
	var advancedTenderComponent1;
	var advancedTenderComponent2;
	var advancedTenderComponent3;
	var advancedTenderComponent4;
	var advancedTenderComponent5;
	var advancedTenderComponent6;
	var advancedTenderComponent7;
	var advancedTenderComponent8;

	$(document).ready(function () {
		advancedTenderCommonInit();
		advancedTenderComponent1.rePosition();
		advancedTenderComponent2.rePosition();
		advancedTenderComponent3.rePosition();
		advancedTenderComponent4.rePosition();
		advancedTenderComponent5.rePosition();
		advancedTenderComponent6.rePosition();
		$('#advancedTenderSearchForm').validate({
			rules: {
				minBudget: {
					digits: true,
				},
				maxBudget: {
					digits: true,
				},
				tenderYmStart: {
					date: true
				},
				tenderYmEnd: {
					date: true
				}
			},
			messages: {
				minBudget: {
					digits: "請輸入正確格式"
				},
				maxBudget: {
					digits: "請輸入正確格式"
				},
			}
		});
	});


	function advancedTenderSearch() {

		if ($("#advancedTenderSearchForm input[name='advancedDateType']:checked").val() == 'isDate' && $("#advancedTenderSearchForm input[id='tenderId']").val() == '') {
			if ($("#advancedTenderSearchForm input[name='tenderStartDate']").val() == "" || $("#advancedTenderSearchForm input[name='tenderEndDate']").val() == "") {
				showCheckFailure("請選擇公告日期");
				return;
			}

			var startDate;
			var endDate;
			switch ($("#advancedTenderSearchForm select[name='tenderType']").val()) {
				case 'TENDER_DECLARATION':
					startDate = parseInt($("#advancedTenderSearchForm input[name='tenderStartDate']").val().replace(/\//g, ""), 10);
					endDate = parseInt($("#advancedTenderSearchForm input[name='tenderEndDate']").val().replace(/\//g, ""), 10);
					break;
				case 'SEARCH_APPEAL':
					startDate = parseInt($("#advancedTenderSearchForm input[name='startDate']").val().replace(/\//g, ""), 10);
					endDate = parseInt($("#advancedTenderSearchForm input[name='endDate']").val().replace(/\//g, ""), 10);
					break;
				case 'PUBLIC_READ':
					startDate = parseInt($("#advancedTenderSearchForm input[name='queryStartDate']").val().replace(/\//g, ""), 10);
					endDate = parseInt($("#advancedTenderSearchForm input[name='queryEndDate']").val().replace(/\//g, ""), 10);
					break;
				case 'PREDICT':
					startDate = parseInt($("#advancedTenderSearchForm input[name='predictNoticeDateStart']").val().replace(/\//g, ""), 10);
					endDate = parseInt($("#advancedTenderSearchForm input[name='predictNoticeDateEnd']").val().replace(/\//g, ""), 10);
					break;
			}


			if (startDate > endDate) {
				showCheckFailure("公告日期 起始日期大於終止日期");
				return;
			}

			var y1 = Math.floor(startDate / 10000) + 1911;
			var m1 = Math.floor(startDate % 10000 / 100);
			var d1 = startDate % 100;
			var date1 = new Date(y1 + "-" + m1 + "-" + d1);
			var y2 = Math.floor(endDate / 10000) + 1911;
			var m2 = Math.floor(endDate % 10000 / 100);
			var d2 = endDate % 100;
			var date2 = new Date(y2 + "-" + m2 + "-" + d2);
			var iDays = parseInt(Math.abs(date1 - date2) / 1000 / 60 / 60 / 24);

			var isBinding = $('#isBinding').val();
			var isLogIn = $('#isLogIn').val();
			if(null == isBinding || '' == isBinding){
				isBinding = 'N';
				$('#isBinding').val('N');
			}
			if(null == isLogIn || '' == isLogIn){
				isLogIn = 'N';
				$('#isLogIn').val('N');
			}
			
			//未登入 未綁定
			if(iDays > 186 && "Y" != isBinding && "Y" != isLogIn) {
				window.location="/prkms/tender/common/bulletion/indexBulletion?showMessage=Y";
				return;
			}
			//登入 未綁定
			else if (iDays > 365 && "Y" != isBinding && "Y" == isLogIn){
				window.location="/prkms/tender/common/bulletion/indexBulletion?showMessage=Y";
				return;
			}
			//other 登入 綁定
			else if(iDays > 730 && "Y" == isBinding && "Y" == isLogIn){
				window.location="/prkms/tender/common/bulletion/indexBulletion?showMessage=Y";
				return;
			}
		}

		//		驗年月 
		if ($("#advancedTenderSearchForm select[name='tenderType']").val() == 'PREDICT') {
			var i = 0;
			if ($("#advancedTenderSearchForm select[name='tenderYmStartY']").val() != "")
				i++;
			if ($("#advancedTenderSearchForm select[name='tenderYmStartM']").val() != "")
				i++;
			if (i % 2 != 0) {
				showCheckFailure("預定招標年月均須填寫");
				return;
			}

			var i = 0;
			if ($("#advancedTenderSearchForm select[name='tenderYmEndY']").val() != "")
				i++;
			if ($("#advancedTenderSearchForm select[name='tenderYmEndM']").val() != "")
				i++;
			if (i % 2 != 0) {
				showCheckFailure("預定招標年月均須填寫");
				return;
			}

			if ($("#advancedTenderSearchForm select[name='tenderYmStartY']").val() != "" && $("#advancedTenderSearchForm select[name='tenderYmStartM']").val() != "" && $("select[name='tenderYmEndY']").val() != "" && $("select[name='tenderYmEndM']").val() != "") {
				var start = parseInt($("#advancedTenderSearchForm select[name='tenderYmStartY']").val() + $("#advancedTenderSearchForm select[name='tenderYmStartM']").val(), 10);
				var end = parseInt($("#advancedTenderSearchForm select[name='tenderYmEndY']").val() + $("#advancedTenderSearchForm select[name='tenderYmEndM']").val(), 10);
				if (start > end) {
					showCheckFailure("預定招標年月起日應小於迄日");
					return;
				}
			}
			if ($("#advancedTenderSearchForm select[name='tenderWay']").val() == "TENDER_WAY_ALL_DECLARATION") {
				showCheckFailure("請選擇預定招標方式");
				return;
			}
		}

		$("#advancedTenderSearchForm input[name='advancedRadProctrgCate']").attr("name", "radProctrgCate");
		$("#advancedTenderSearchForm input[name='advancedDateType']").attr("name", "dateType");
		$('#advancedTenderSearchForm input[name="firstSearch"]').val("true");
//		$('#advancedTenderSearchForm').submit();
		//改用get 送出
        window.location = $('#advancedTenderSearchForm').attr("action") + "?" + $('#advancedTenderSearchForm').serialize();	
	}

	function advancedTenderCommonInit() {


		//初始化時間開始
		var strOfTenderAdvancedStartDateInit = getDateCommonUtilInitDate(new Date(), -6);
		var strOfTenderAdvancedEndDateInit = getDateCommonUtilInitDate(new Date(), 0);
		//初始化時間結束


		var tenderQueryConfiga = {
			element: $('#advancedTenderSearchForm #tenderStartDateArea')[0],
			type: 'yyy/MM/dd',
			defaultValue: strOfTenderAdvancedStartDateInit,
			attr: { id: 'tenderStartDate', name: 'tenderStartDate', class: 'form-date g_form-date' },
			callback: function(data)　{
				chkIsCppArea(data.inputValue);
				}
		}
		advancedTenderComponent1 = new Geps3.DatePicker(tenderQueryConfiga);

		var tenderQueryConfigaa = {
			element: $('#advancedTenderSearchForm #tenderEndDateArea')[0],
			type: 'yyy/MM/dd',
			defaultValue: strOfTenderAdvancedEndDateInit,
			attr: { id: 'tenderEndDate', name: 'tenderEndDate', class: 'form-date g_form-date' }
		}
		advancedTenderComponent2 = new Geps3.DatePicker(tenderQueryConfigaa);

		var tenderQueryConfigb = {
			element: $('#advancedTenderSearchForm #spdtStartDateArea')[0],
			type: 'yyy/MM/dd',
			defaultValue: '',
			attr: { id: 'spdtStartDate', name: 'spdtStartDate', class: 'form-date g_form-date' }
		}
		advancedTenderComponent3 = new Geps3.DatePicker(tenderQueryConfigb);

		var tenderQueryConfigbb = {
			element: $('#advancedTenderSearchForm #spdtEndDateArea')[0],
			type: 'yyy/MM/dd',
			defaultValue: '',
			attr: { id: 'spdtEndDate', name: 'spdtEndDate', class: 'form-date g_form-date'}
		}
		advancedTenderComponent4 = new Geps3.DatePicker(tenderQueryConfigbb);

		var tenderQueryConfigc = {
			element: $('#advancedTenderSearchForm #opdtStartDateArea')[0],
			type: 'yyy/MM/dd',
			defaultValue: '',
			attr: { id: 'opdtStartDate', name: 'opdtStartDate', class: 'form-date g_form-date' }
		}
		advancedTenderComponent5 = new Geps3.DatePicker(tenderQueryConfigc);

		var tenderQueryConfigcc = {
			element: $('#advancedTenderSearchForm #opdtEndDateArea')[0],
			type: 'yyy/MM/dd',
			defaultValue: '',
			attr: { id: 'opdtEndDate', name: 'opdtEndDate', class: 'form-date g_form-date' }
		}
		advancedTenderComponent6 = new Geps3.DatePicker(tenderQueryConfigcc);

		var minBudgetConfig = {
			element: $('#advancedTenderSearchForm #minBudgetArea')[0],
			attr: { id: 'minBudgetInputId', alt: '最小預算金額', title: '最小預算金額' },
			defaultValue: '',
			showMessage: false,
			callback: function (data) {
				$("#advancedTenderSearchForm #minBudget").val(data.value);
			}
		}
		var advancedTenderComponent7 = new Geps3.Currency(minBudgetConfig);
		$("#minBudgetInputId").attr("name", "");

		var maxBudgetConfig = {
			element: $('#advancedTenderSearchForm #maxBudgetArea')[0],
			attr: { id: 'maxBudgetInputId', alt: '最大預算金額', title:'最大預算金額' },
			defaultValue: '',
			showMessage: false,
			callback: function (data) {
				$("#advancedTenderSearchForm #maxBudget").val(data.value);
			}
		}
		var advancedTenderComponent8 = new Geps3.Currency(maxBudgetConfig);
		$("#maxBudgetInputId").attr("name", "");


// 		$('.f_20').css({
// 			"width": "100%"
// 		});

	}
	
	function chkIsCppArea(inputval){

		if(inputval != null && inputval != ''){
			
			var dateArr = inputval.split('/');
			var thYear = Number(1911) + Number(dateArr[0]);
			var thdate = thYear + "/" + dateArr[1] + "/" + dateArr[2];
			
			var caseChkDate = new Date('2023/07/01');
			var casestartDate =  new Date(thdate);
			
			if(caseChkDate > casestartDate){
				$('input:radio[name=isCpp][value=""]').click();
				$('#isCppArea').hide();
			}else{
				$('#isCppArea').show();
			}
		}

	}
</script>
				</div>
				<div id="tenderUpdatedDivId" style="display: none">
					<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">








<form action="/prkms/tender/common/updated/readTenderUpdated" id="updatedTenderSearchForm" method="post" autocomplete="off">
	<input type="hidden" id="firstSearch" title="firstSearch" name="firstSearch" value="false" />
	<input type="hidden" id="searchType" title="searchType" name="searchType" value="updated" />
	<input type="hidden" id="hadUpdated" title="hadUpdated" name="hadUpdated" value="true" />
	<input type="hidden" id="isBinding" name="isBinding" title="isBinding" value="N" />
	<input type="hidden" id="isLogIn" name="isLogIn" title="isLogIn" value="N" />
	
	<div class="row pt10">
		<div class="col-xl-6 col-sm-12">
			<div class="flo" id='orgNameThId'>
				<label for="orgName">
					<span class="text-red">@</span>機關名稱：
				</label>
			</div>
			<div class="flo">
				<input type="text" class="form-control" id="orgName" title="機關名稱" name="orgName" value="" style="width:95%">
			</div>
			<div class="flo">
				<button type="submit" class="btn l-btn btn-block">
					<a href="/prkms/tender/common/orgName/indexTenderOrgName?searchType=updated" tabindex="-1" title="查詢">查詢</a>
				</button>
			</div>
		</div>

		<div class="col-xl-6 col-sm-12 fix-l">
			<div class="flo" id="orgIdThId">
				<label for="orgId">
					<span class="text-reds"></span>機關代碼：
				</label>
			</div>
			<div class="flo">
				<input type="text" class="form-control" id="orgId" title="機關代碼" name="orgId" value="" style="width: 95%">
			</div>
			<div class="flo">
				<button type="submit" class="btn l-btn btn-block">
					<a href="/prkms/tender/common/orgName/indexTenderOrgName?searchType=updated" tabindex="-1" title="查詢">查詢</a>
				</button>
			</div>
		</div>
		<div style="width: 100%; height: 10px; float: left"></div>
		<div class="col-xl-6 col-sm-12">
			<div class="flo" id="tenderNameThId">
				<label for="tenderName">
					<span class="text-red">@</span>標案名稱：
				</label>
			</div>
			<div class="flo">
				<input type="text" class="form-control" id="tenderName" title="標案名稱" name="tenderName" style="width: 95%">
			</div>
		</div>
		<div class="col-xl-6 col-sm-12">
			<div class="flo" id="tenderCaseNoThId">
				<label for="tenderId">
					<span class="text-reds"></span>標案案號：
				</label>
			</div>
			<div class="flo">
				<input type="text" class="form-control" id="tenderId" title="標案案號" name="tenderId" style="width: 95%">
			</div>
		</div>
		<div style="width: 100%; height: 10px; float: left"></div>
		<div class="col-xl-6 col-sm-12">
			<div class="flo" id="tenderTypeThId">
				<label for="name">
					<span class="text-red">＊</span>招標類型：
				</label>
			</div>
			<div class="flo">
				<select class="select-s01" id="tenderTypeSelect" title="招標類型" name="tenderType" onchange="changePisTenderType(event,'/prkms/tender/common/updated/readTenderUpdated',true)">
					<option value="TENDER_DECLARATION">招標公告</option>
					<option value="SEARCH_APPEAL">公開徵求</option>
					<option value="PUBLIC_READ">公開閱覽</option>
					<option value="PREDICT">政府採購預告</option>
				</select>
			</div>
		</div>
		<div class="col-xl-6 col-sm-12">
			<div class="flo" id="tenderWayTitle">
				<label for="name">
					<span class="text-red">＊</span>招標方式：
				</label>
			</div>
			<div class="flo">
				<select class="select-s01" id="declarationSelect" title="招標方式" name="tenderWay">
					<option value="TENDER_WAY_ALL_DECLARATION">各式招標公告</option>
					<option value="TENDER_WAY_ALL_DECLARATION">各式招標公告</option>
					<option value="TENDER_WAY_1">公開招標</option>
					<option value="TENDER_WAY_12">公開取得電子報價單</option>
					<option value="TENDER_WAY_2">公開取得報價單或企劃書</option>
					<option value="TENDER_WAY_4">經公開評選或公開徵求之限制性招標</option>
					<option value="TENDER_WAY_5">選擇性招標(建立合格廠商名單)</option>
					<option value="TENDER_WAY_7">選擇性招標(建立合格廠商名單後續邀標)</option>
					<option value="TENDER_WAY_3">選擇性招標(個案)</option>
					<option value="TENDER_WAY_10">電子競價</option>
					<option value="TENDER_WAY_6">限制性招標(未經公開評選或公開徵求)</option>
				</select>
			</div>
		</div>
		<div style="width: 100%; height: 10px; float: left"></div>
		<div class="col-xl-12 col-sm-12">
			<div class="flo" id="dateTypeTitle">
				<label for="name">
					<span class="text-red">＊</span>公告日期：
				</label>
			</div>
			<div class="flo">
				<div class="form-check form-check-inline">
					<input class="form-check-input" type="radio" name="updatedDateType" id="level_21" title="level_21" checked="checked" value="isNow">
					<label class="form-check-label" for="basicIsNowDateTypeId">當日</label>
				</div>
				<div class="form-check form-check-inline">
					<input class="form-check-input" type="radio" name="updatedDateType" id="level_22" title="level_22" value="isSpdt">
					<label class="form-check-label" for="level_22">等標期內</label>
				</div>
				<div class="form-check form-check-inline">
					<input class="form-check-input" type="radio" name="updatedDateType" id="level_23" title="level_23" value="isDate">
					<div id="tenderStartDateArea" class="dateArea"></div>
					<label for="endTime4">
						<span>—</span>
					</label>
					<div id="tenderEndDateArea" class="dateArea"></div>
				</div>
			</div>
		</div>
		<div style="width: 100%; height: 10px; float: left"></div>
		<div class="col-xl-12 col-sm-12">
			<div class="flo" id="radProctrgCateThId">
				<label for="name">
					<span class="text-red"></span>採購性質
				</label>
			</div>
			<div class="flo">
				<div class="form-check form-check-inline">
					<input class="form-check-input" type="radio" id="updatedRadProctrgCate1" title="updatedRadProctrgCate1" name="updatedRadProctrgCate" value="RAD_PROCTRG_CATE_1">
					<label class="form-check-label" for="updatedRadProctrgCate1">工程</label>
				</div>
				<div class="form-check form-check-inline">
					<input class="form-check-input" type="radio" id="updatedRadProctrgCate2" title="updatedRadProctrgCate2" name="updatedRadProctrgCate" value="RAD_PROCTRG_CATE_2">
					<label class="form-check-label" for="updatedRadProctrgCate2">財物</label>
				</div>
				<div class="form-check form-check-inline">
					<input class="form-check-input" type="radio" id="updatedRadProctrgCate2" title="updatedRadProctrgCate2" name="updatedRadProctrgCate" value="RAD_PROCTRG_CATE_2">
					<label class="form-check-label" for="updatedRadProctrgCate3">勞務</label>
				</div>
				<div class="form-check form-check-inline">
					<input class="form-check-input" type="radio" id="updatedRadProctrgCate4" title="updatedRadProctrgCate4" name="updatedRadProctrgCate" value="">
					<label class="form-check-label" for="updatedRadProctrgCate4">不限</label>
				</div>
			</div>
		</div>
		<div style="width: 100%; height: 10px; float: left"></div>
		<div class="col-xl-12 col-sm-12">
			<div class="flo" id="gpaOrAnztecOrAstepThId">
				<label for="name">
					<span class="text-red"></span>適用條約或協定：
				</label>
			</div>
			<div class="flo">
				<div class="Lb-1">
					<input type="checkbox" id="gpa" title="適用WTO政府採購協定(GPA)" name="gpa" value="true">
					<label for="gpa">適用WTO政府採購協定(GPA)</label>
				</div>
				<div class="Lb-1">
					<input type="checkbox" id="anztec" title="適用臺紐經濟合作協定(ANZTEC)" name="anztec" value="true">
					<label for="anztec">適用臺紐經濟合作協定(ANZTEC)</label>
				</div>
				<div class="Lb-1">
					<input type="checkbox" id="astep" title="適用臺星經濟夥伴協定(ASTEP)" name="astep" value="true">
					<label for="astep">適用臺星經濟夥伴協定(ASTEP)</label>
				</div>
			</div>
		</div>
		<div class="col-xl-12 col-sm-12">
			<div class="main-btn text-center">
				<div class="btn w170" onclick="updatedTenderSearch();">查詢</div>
			</div>
		</div>
		<div style="width: 100%; height: 40px; float: left"></div>
		<div class="col-xl-12 col-sm-12">
			<span class="text-w">註：</span><span class="text-red"> <span class="red">◎</span></span>
			<span>符號<span style="color: red;">*</span>代表必填，<span style="color: red;">＠</span>代表可填入關鍵字。</span>
			<br>
			<span class="text-w"></span><span class="text-red"> <span class="red">◎</span></span>
			<span>若查不到已公告的資料，表示此案正在進行更正公告中。</span>
			<br>
			<span class="text-w"></span><span class="text-red"> <span class="red">◎</span></span>
			<span>若欲以細項標的分類(如84電腦及相關服務) 查詢者，請使用『標的分類查詢』功能。</span>
			<br>
			<span class="text-w"></span><span class="text-red"> <span class="red">◎</span></span>
			<span>國防部軍備局新增1個機關，生產製造中心機關代碼為**********。</span>
			<br>
			<span class="text-w"></span><span class="text-red"> <span class="red">◎</span></span>
			<span>功能使用請參考：首頁&gt;學習資源&gt;線上教學。</span>
		</div>
	</div>

</form>

<!--表格結束-->

<!--左邊內容結束-->

<script type="text/javascript">
	var updatedTenderComponent1;
	var updatedTenderComponent2;

	$(document).ready(function () {
		updatedTenderCommonInit();
		updatedTenderComponent1.rePosition();
		updatedTenderComponent2.rePosition();
		var wdth = $(window).width();
		$("#container").width( wdth - $("#container").parent().offset().left - $("#container").parent().parent().offset().left);
		myChart.resize();
	});


	function updatedTenderSearch() {
		var $form = $(event.target).parents('form:first');
		var formId = "#" + $form.attr('id') + "";

		if ($("#updatedTenderSearchForm input[name='updatedDateType']:checked").val() == 'isDate' && $("#updatedTenderSearchForm input[id='tenderId']").val() == '') {
			if ($("#updatedTenderSearchForm input[name='tenderStartDate']").val() == "" || $("#updatedTenderSearchForm input[name='tenderEndDate']").val() == "") {
				showCheckFailure("請選擇公告日期");
				return;
			}

			var startDate;
			var endDate;
			switch ($(formId + "#tenderTypeSelect").val()) {
				case 'TENDER_DECLARATION':
					startDate = parseInt($("#updatedTenderSearchForm input[name='tenderStartDate']").val().replace(/\//g, ""), 10);
					endDate = parseInt($("#updatedTenderSearchForm input[name='tenderEndDate']").val().replace(/\//g, ""), 10);
					break;
				case 'SEARCH_APPEAL':
					startDate = parseInt($("#updatedTenderSearchForm input[name='startDate']").val().replace(/\//g, ""), 10);
					endDate = parseInt($("#updatedTenderSearchForm input[name='endDate']").val().replace(/\//g, ""), 10);
					break;
				case 'PUBLIC_READ':
					startDate = parseInt($("#updatedTenderSearchForm input[name='queryStartDate']").val().replace(/\//g, ""), 10);
					endDate = parseInt($("#updatedTenderSearchForm input[name='queryEndDate']").val().replace(/\//g, ""), 10);
					break;
				case 'PREDICT':
					startDate = parseInt($("#updatedTenderSearchForm input[name='predictNoticeDateStart']").val().replace(/\//g, ""), 10);
					endDate = parseInt($("#updatedTenderSearchForm  input[name='predictNoticeDateEnd']").val().replace(/\//g, ""), 10);
					break;
			}

			if (startDate > endDate) {
				showCheckFailure("公告日期 起始日期大於終止日期");
				return;
			}

			var y1 = Math.floor(startDate / 10000) + 1911;
			var m1 = Math.floor(startDate % 10000 / 100);
			var d1 = startDate % 100;
			var date1 = new Date(y1 + "-" + m1 + "-" + d1);
			var y2 = Math.floor(endDate / 10000) + 1911;
			var m2 = Math.floor(endDate % 10000 / 100);
			var d2 = endDate % 100;
			var date2 = new Date(y2 + "-" + m2 + "-" + d2);
			var iDays = parseInt(Math.abs(date1 - date2) / 1000 / 60 / 60 / 24);

			var isBinding = $('#isBinding').val();
			var isLogIn = $('#isLogIn').val();
			if(null == isBinding || '' == isBinding){
				isBinding = 'N';
				$('#isBinding').val('N');
			}
			if(null == isLogIn || '' == isLogIn){
				isLogIn = 'N';
				$('#isLogIn').val('N');
			}
			
			//未登入 未綁定
			if(iDays > 186 && "Y" != isBinding && "Y" != isLogIn) {
				window.location="/prkms/tender/common/bulletion/indexBulletion?showMessage=Y";
				return;
			}
			//登入 未綁定
			else if (iDays > 365 && "Y" != isBinding && "Y" == isLogIn){
				window.location="/prkms/tender/common/bulletion/indexBulletion?showMessage=Y";
				return;
			}
			//other 登入 綁定
			else if(iDays > 730 && "Y" == isBinding && "Y" == isLogIn){
				window.location="/prkms/tender/common/bulletion/indexBulletion?showMessage=Y";
				return;
			}

		}

		$("#updatedTenderSearchForm input[name='updatedRadProctrgCate']").attr("name", "radProctrgCate");
		$("#updatedTenderSearchForm input[name='updatedDateType']").attr("name", "dateType");
		$('#updatedTenderSearchForm input[name="firstSearch"]').val("true");
//		$('#updatedTenderSearchForm').submit();
		//改用get 送出
        window.location = $('#updatedTenderSearchForm').attr("action") + "?" + $('#updatedTenderSearchForm').serialize();	
	}

	function updatedTenderCommonInit() {

		//初始化時間開始
		var strOfTenderUpdatedStartDateInit = getDateCommonUtilInitDate(new Date(), -6);
		var strOfTenderUpdateddEndDateInit = getDateCommonUtilInitDate(new Date(), 0);
		//初始化時間結束


		var tenderQueryConfiga = {
			element: $('#updatedTenderSearchForm #tenderStartDateArea')[0],
			type: 'yyy/MM/dd',
			defaultValue: strOfTenderUpdatedStartDateInit,
			attr: { id: 'tenderStartDate', name: 'tenderStartDate', class: 'form-date g_form-date' }
		}
		updatedTenderComponent1 = new Geps3.DatePicker(tenderQueryConfiga);

		var tenderQueryConfigaa = {
			element: $('#updatedTenderSearchForm #tenderEndDateArea')[0],
			type: 'yyy/MM/dd',
			defaultValue: strOfTenderUpdateddEndDateInit,
			attr: { id: 'tenderEndDate', name: 'tenderEndDate', class: 'form-date g_form-date' }
		}
		updatedTenderComponent2 = new Geps3.DatePicker(tenderQueryConfigaa);

// 		$('.f_20').css({
// 			"width": "100%"
// 		});

	}
</script>
				</div>
				<script type="text/javascript">
					//若使用tenderId則可不使用日期搜尋
					$("input[name='tenderId']").change(
							function() {
								var tabVal = $( "input[name='tenderLevel1']:checked").attr("id").trim();
								if (this.value != '') {
									switch (tabVal) {
									case "tenderLevel11":
										$('#basicIsDateDateTypeId').attr("checked", true);
										break;
									case "tenderLevel12":
										$('#advancedIsDateDateTypeId').attr("checked", true);
										break;
									case "tenderLevel13":
										$('#level_23').attr("checked", true);
										break;
									default:
										break;
									}
									$('#tenderStartDateArea input').val('');
									$('#tenderEndDateArea input').val('');
								}
							});
					
					$(document).ready(function() {
						clickTenderDate();
					});
					
					function clickTenderDate(){
						var startDate = $('div#tenderStartDateArea');
						var endDate = $('div#tenderEndDateArea');
						var dateArray = [];

						dateArray.push(startDate);
						dateArray.push(endDate);
						$.each(dateArray, function(index, value){
							$(this).on('click', function(){
								//判斷父層是哪個div來勾選radio
								if($(this).parents("#tenderBasicDivId").length==1) {
									$('input#basicIsDateDateTypeId').prop('checked', true);
								}
								else if ($(this).parents("#tenderAdvancedDivId").length==1) {
									$('input#advancedIsDateDateTypeId').prop('checked', true);
								}else if ($(this).parents("#tenderUpdatedDivId").length==1) {
									$('input#level_23').prop('checked', true);
								}
							});
						});
					}
				</script>
			</div>
		</div>
	</div>
</div>

			<!--登入區塊 -->
			






<script type="text/javascript" src='/ccs/dist/geps3-commons.bundle.js'></script>
<script type="text/javascript" charset="UTF-8">
var orgComponent;
var suppComponent;
var alertMessage = {"HICOS_NOT_WORK_MSG": "尚未安裝元件"};

function captchaOrg() {
	  return new Promise((resolve, reject) => {
		  	var imageVerificationConfig = {
	        		 element : document.getElementById('captchaOrg'),
	        		 lang : 'ch',
	        		 attr : { 
		        		img : { id : 'orgImageVerification' , name: 'orgImageVerification' , alt:'驗證碼文字圖片',style:'max-width:85px' },
	        		 	input : { id : 'orgImageVerifyCode' , name : 'orgImageVerifyCode' , size:4 , maxlength:4 , style:'width:58px; display: inline-block; float: left; margin-left: 10px'},
	       			 	playVoiceButImg: { style:'width:30px'},
	     			 	reloadButImg:{style:'width:30px'}
	        		 },
					 hideImgBtn : false
	     		}
		  	orgComponent = new Geps3.ImageVerification(imageVerificationConfig);
		  	resolve("Success1");
	  });
	}
	
function captchaSupp() {
	  return new Promise((resolve, reject) => {
		  	var imageVerificationConfig = {
	        		 element : document.getElementById('captchaSupp'),
	        		 lang : 'ch',
	        		 attr : { 
	        		  	img : { id : 'suppImageVerification' , name: 'suppImageVerification' , alt:'驗證碼文字圖片',style:'max-width:85px' },
	        		  	input : { id : 'suppImageVerifyCode' , name : 'suppImageVerifyCode' , size:4 , maxlength:4 , style:'width:58px; display: inline-block; float: left; margin-left: 10px'},
	       			 	playVoiceButImg: { style:'width:30px'},
	     			 	reloadButImg:{style:'width:30px'}
	        		 },
					 hideImgBtn : false
	     		}
		  	suppComponent = new Geps3.ImageVerification(imageVerificationConfig);
		  	resolve("Success2");
	  });
	}
	
async function captchaOrgExe() {
	var result = await captchaOrg();
	$("#orgImageVerification").parent().attr("style","width: 85px; height: 45px; color: red; display: inline-block; float: left");
	$("#captchaOrg #imgVerifyHint").attr("style","display: none");
	$("#captchaOrg #reloadBut").parent().parent().attr("style", "display: inline-block");
	$("#captchaOrg #reloadBut").parent().parent().attr("align", "center");
	$("#captchaOrg #reloadBut").parent().attr("style", "display: inline-block; margin-top: 5px; margin-left: 5px;");
	$('#captchaOrg #reloadBut').hover(
		function () {
		   $(this).css({"color":"white","text-decoration":"none"});
		}, 
		
		function () {
		   $(this).css({"color":"white","text-decoration":"none"});
		}
	);
	$('#captchaOrg #playVoiceBut').hover(
		function () {
		   $(this).css({"color":"white","text-decoration":"none"});
		}, 
		
		function () {
		   $(this).css({"color":"white","text-decoration":"none"});
		}
	);
}
	
async function captchaSuppExe() {	  
	var result = await captchaSupp();
	$("#suppImageVerification").parent().attr("style","width: 85px; height: 45px; color: red; display: inline-block; float: left");
 	$("#captchaSupp #imgVerifyHint").attr("style","display: none");
	$("#captchaSupp #reloadBut").parent().parent().attr("style", "display: inline-block");
	$("#captchaSupp #reloadBut").parent().parent().attr("align", "center");
  	$("#captchaSupp #reloadBut").parent().attr("style", "display: inline-block; margin-top: 5px; margin-left: 5px;");
	$("#captchaSupp #reloadBut").hover(
		function () {
		   $(this).css({"color":"black","text-decoration":"none"});
		}, 
		
		function () {
		   $(this).css({"color":"black","text-decoration":"none"});
		}
        );	

	$("#captchaSupp #playVoiceBut").hover(
		function () {
		   $(this).css({"color":"black","text-decoration":"none"});
		}, 
		
		function () {
		   $(this).css({"color":"black","text-decoration":"none"});
		}
    );	
}

$(document).ready(function() {
	captchaOrg();
	captchaSupp();
	getAlertMessage();
	cleanPath();
	
	
		
		
		
			orgUseAccount();
		
	
	
		
		
		
			suppUseAccount();
		
	
	
	
	
	
	
	// 增加ENTER event
	$("#orgUserId, #orgUserIdExt, #orgImageVerifyCode").keypress(function(e){
		code = (e.keyCode ? e.keyCode : e.which);
		if (code == 13){
			orgLogin('pwd');
		}
	});
	
	// 增加ENTER event
	$("#orgPin").keypress(function(e){
		code = (e.keyCode ? e.keyCode : e.which);
		if (code == 13){
			orgLogin('cert');
		}
	});
	
	// 增加ENTER event
	$("#suppUserId, #suppUserIdExt, #suppImageVerifyCode").keypress(function(e){
		code = (e.keyCode ? e.keyCode : e.which);
		if (code == 13){
			suppLogin('pwd');
		}
	});
	
	// 增加ENTER event
	$("#suppPin").keypress(function(e){
		code = (e.keyCode ? e.keyCode : e.which);
		if (code == 13){
			suppLogin('cert');
		}
	});
	
	// 切換顯示密碼
	$(".toggle-eye").click(function(e){
		e.stopPropagation();
	    $(this).toggleClass("fa-eye fa-eye-slash");
	    
	    var input = $("#" + e.target.dataset.eyeword);
	    if (input.attr("type") == "password") {
			input.attr("type", "text");
		} else {
			input.attr("type", "password");
		}
	});
	
	// 登入名稱難字顯示
	

	// 登入名稱難字顯示
	
});

function showCns(elm, value) {
	var allStrAry = [];
	while(checkIsCNS(value)) {
		let hwTagIdxBegin = value.indexOf('<page>');
		let hwTagIdxEnd = value.indexOf('</code>');
		
		let normalStr = value.substring(0, hwTagIdxBegin);
		allStrAry.push(normalStr);
		
		let hwTagStr = value.substring(hwTagIdxBegin, hwTagIdxEnd+7);			
		allStrAry.push(hwTagStr);
		
		value = value.substring(hwTagIdxEnd+7);
	}
	if (value.length > 0) {
		allStrAry.push(value);
	}
	
	$(elm).empty();
	allStrAry.forEach(function(item){
		if (checkIsCNS(item)) {
			var rawVal = $("<textarea></textarea>").html(item).text(); // escapeXml還原
			var showStr = Geps3.CNS.pageCode2Img(rawVal);
			$(elm).append($("<span></span>").html(showStr));
		}
		else {
			$(elm).append($("<span></span>").text(item));
		} 
	});

}

function checkIsCNS(defVal) {
	var expCNS = new RegExp(
			'(?:&lt;|<)\\s*page\\s*(?:&gt;|>)\\s*([0-9a-f]{1,2})\\s*(?:&lt;|<)\\s*\/\\s*page\\s*(?:&gt;|>)\\s*(?:&lt;|<)\\s*code\\s*(?:&gt;|>)\\s*([0-9a-f]{1,4})\\s*(?:&lt;|<)\\s*\/\\s*code\\s*(?:&gt;|>)');

	if (expCNS.test(defVal)) {
		return true;
	}

	return false;
}

//帳密登入-機關
function orgUseAccount() {
	//切換模式
	$("#orgCertificateMode").hide();
	$("#orgAccountMode").show();
	$("#orgMobileCertMode").hide();
	// 清除資料
	$("#orgPin").val("");
	$("#orgPinError").html("");
	$("#orgMobileError").html("");
	
	$("#orgAccountModeLoginType1").prop('checked', true);
	$("#orgAccountModeLoginType2").prop('checked', false);
	$("#orgAccountModeLoginType5").prop('checked', false);
}

// 憑證登入-機關
function orgUseCertificate() {
	//切換模式
	$("#orgCertificateMode").show();
	$("#orgAccountMode").hide();
	$("#orgMobileCertMode").hide();
	// 清除資料
	$("#orgUserId").val("");
	$("#orgUserIdExt").val("");
	$("#orgPassword").val("");
	$("#orgUserIdValid").html("");
	$("#orgUserIdExtValid").html("");
	$("#orgPasswordValid").html("");
	$("#orgImageVerifyCodeValid").html("");
	$("#orgMobileError").html("");
	
	$("#orgAccountModeLoginType3").prop('checked', false);
	$("#orgAccountModeLoginType4").prop('checked', true);
	$("#orgAccountModeLoginType6").prop('checked', false);
}

//行動自然人憑證登入-機關
function orgUseMobileCert() {
	//切換模式
	$("#orgCertificateMode").hide();
	$("#orgAccountMode").hide();
	$("#orgMobileCertMode").show();
	// 清除資料
	$("#orgUserId").val("");
	$("#orgUserIdExt").val("");
	$("#orgPassword").val("");
	$("#orgUserIdValid").html("");
	$("#orgUserIdExtValid").html("");
	$("#orgPasswordValid").html("");
	$("#orgImageVerifyCodeValid").html("");
	$("#orgPin").val("");
	$("#orgPinError").html("");
	
	$("#orgAccountModeLoginType7").prop('checked', false);
	$("#orgAccountModeLoginType8").prop('checked', false);
	$("#orgAccountModeLoginType9").prop('checked', true);
}

//帳密登入-廠商
function suppUseAccount() {
	//切換模式
	$("#suppCertificateMode").hide();
	$("#suppAccountMode").show();
	$("#suppMobileCertMode").hide();
	// 清除資料
	$("#suppPin").val("");
	$("#suppPinError").html("");
	$("#suppMobileError").html("");
	
	$("#suppAccountModeLoginType1").prop('checked', true);
	$("#suppAccountModeLoginType2").prop('checked', false);
	$("#suppAccountModeLoginType5").prop('checked', false);
}

// 憑證登入-廠商
function suppUseCertificate() {
	//切換模式
	$("#suppCertificateMode").show();
	$("#suppAccountMode").hide();
	$("#suppMobileCertMode").hide();
	// 清除資料
	$("#suppUserId").val("");
	$("#suppUserIdExt").val("0");
	$("#suppPassword").val("");
	$("#suppUserIdValid").html("");
	$("#suppUserIdExtValid").html("");
	$("#suppPasswordValid").html("");
	$("#suppImageVerifyCodeValid").html("");
	$("#suppMobileError").html("");
	
	$("#suppAccountModeLoginType3").prop('checked', false);
	$("#suppAccountModeLoginType4").prop('checked', true);
	$("#suppAccountModeLoginType6").prop('checked', false);
}

//行動自然人憑證登入-廠商
function suppUseMobileCert() {
	//切換模式
	$("#suppCertificateMode").hide();
	$("#suppAccountMode").hide();
	$("#suppMobileCertMode").show();
	// 清除資料
	$("#suppUserId").val("");
	$("#suppUserIdExt").val("0");
	$("#suppPassword").val("");
	$("#suppUserIdValid").html("");
	$("#suppUserIdExtValid").html("");
	$("#suppPasswordValid").html("");
	$("#suppImageVerifyCodeValid").html("");	
	$("#suppPin").val("");
	$("#suppPinError").html("");
	
	$("#suppAccountModeLoginType7").prop('checked', false);
	$("#suppAccountModeLoginType8").prop('checked', false);
	$("#suppAccountModeLoginType9").prop('checked', true);
}

// pin碼檢查
function vaildPin(pin){
	if (!pin) {
		return "請輸入憑證PIN碼";
	}else  if (pin.length < 6 || pin.length > 8) {
		return "憑證密碼只允許6-8碼，請確認輸入的憑證密碼是否正確，\n若您的密碼非6-8碼，請至各憑證管理中心修改憑證密碼";
	}
	
	return;
}

// 機關登入
function orgLogin(type) {
	$("#signedForm").trigger("reset");
	$("#signedForm").attr("action", "/pis/sso/loginForOrg");
	$("#signedForm").attr("method", "post");
	
	// 密碼登入
	if("pwd" == type) {
		// 設定登入資料
		$("#loginType").val("pwd");
		$("#userId").val($("#orgUserId").val());
		$("#userIdExt").val($("#orgUserIdExt").val());
		
		//進行編碼防止特殊字元亂碼
		var encodePwd = encodeURIComponent($("#orgPassword").val());
		
		$("#password").val(encodePwd);
		$("#imageVerifyCode").val($("#orgImageVerifyCode").val());
		$("#imageVerifyToken").val(orgComponent.token);
		$("#signedForm").submit(); // 登入
	}
	
	if("cert" != type && "mobile" != type) {
		$("#signedForm").trigger("reset");
		return;
	}
	
	// 憑證登入
	if("cert" == type) {
		// pin碼檢查
		var result=vaildPin($("#orgPin").val());
		$("#orgPinError").html("");
		if(result){
			$("#orgPinError").html(result);
			return;
		}
		
		$("#loginType").val("cert");
		$("#pin").val($("#orgPin").val());
	} else {
		$("#orgMobileError").html("");
	}
	setTimeout(callAjax(type, "org"),"500");
}

//廠商登入
function suppLogin(type) {
	$("#signedForm").trigger("reset");
	$("#signedForm").attr("action", "/pis/sso/loginForSupp");
	$("#signedForm").attr("method", "post");
	
	// 密碼登入
	if("pwd" == type) {
		// 設定登入資料
		$("#loginType").val("pwd");
		$("#userId").val($("#suppUserId").val());
		$("#userIdExt").val($("#suppUserIdExt").val());
		
		//進行編碼防止特殊字元亂碼
		var encodePwd = encodeURIComponent($("#suppPassword").val());
		
		$("#password").val(encodePwd);
		$("#imageVerifyCode").val($("#suppImageVerifyCode").val());
		$("#imageVerifyToken").val(suppComponent.token);
		$("#signedForm").submit(); // 登入
	}
	
	if("cert" != type && "mobile" != type) {
		$("#signedForm").trigger("reset");
		return;
	}
	
	// 憑證登入
	if("cert" == type) {
	// pin碼檢查
		var result=vaildPin($("#suppPin").val());
		$("#suppPinError").html("");
		if(result){
			$("#suppPinError").html(result);
			return;
		}
		
		$("#loginType").val("cert");
		$("#pin").val($("#suppPin").val());
	} else {
		$("#suppMobileError").html("");
	}
	setTimeout(callAjax(type, "supp"),"500");
}

function callAjax(type = "cert", loginType) {
	try {
		$.ajax({
			url : '/pis/sso/readRandom',
			type : "POST",
		}).done(function(content) {
			$("#content").val(content);
			if("cert" == type){
				makeSignature(); //簽章
			}
			if("mobile" == type){
				mobileCert(content, loginType); // 行動自然人憑證
			}
		}).fail(function(jqXHR, textStatus) {
			alert(textStatus);
		});
	} catch (e) {
		//
	} finally {
		//
	}
}

//取得提示訊息
function getAlertMessage() {
	try {
		$.ajax({
			url : '/pis/sso/readHicosMessage',
			type : "GET",
		}).done(function(content) {
			alertMessage = JSON.parse(content);
		}).fail(function(jqXHR, textStatus) {
			console.error(textStatus);
		});
	} catch (e) {
		console.log(e);
	} finally {
		//
	}
}

// 至raam的功能
function gotoRaam(uri) {
	try {
		$.ajax({
			url : '/pis/sso/readServerUrl',
			type : "GET",
		}).done(function(content) {
			location.href = content + uri;
		}).fail(function(jqXHR, textStatus) {
			console.error(textStatus);
		});
	} catch (e) {
		//
	} finally {
		//
	}
}

//清除獨立登入的path
function cleanPath() {
	$.ajax({
		url : '/pis/sso/cleanPath',
		type : "POST",
	}).done(function(content) {
		
	}).fail(function(jqXHR, textStatus) {
		alert(textStatus);
	});
}

function mobileCert(signData, loginType) {
	let data = btoa(signData);
	data = data.replace(/\n/g, "");
	let serviceCode = "LOGIN_BIND_CERT_ORG"; //機關登入
	if(loginType == "supp") {
		serviceCode = "LOGIN_BIND_CERT_SUPP"; //廠商登入
	}
	let paramOpt = {
			"opCode": "SIGN",
			"hint": "綁定憑證",
			"signData": data,
			"serviceCode":  serviceCode
	};
	const params = "&twFidoInitData=" + encodeURIComponent(JSON.stringify(paramOpt));
	
	const funcPath='/qdcs/soft-token/twFidoInit';
	try {
		$.ajax(
		{
			url : funcPath,
			type : "POST",
			dataType: 'json',
			data : params,
		})
		.done(function(content) {
			let target = "_blank";
			let twFidoForm = createTempForm("twFidoForm", target, content.apiUrl);

			createTempInput(twFidoForm, "transaction_id", content.transactionId);
			createTempInput(twFidoForm, "op_code", content.opCode);
			createTempInput(twFidoForm, "sp_service_id", content.spServiceId);
			createTempInput(twFidoForm, "sp_checksum", content.spChecksum);
			createTempInput(twFidoForm, "hint", content.hint);
			createTempInput(twFidoForm, "sign_type", content.signType);
			createTempInput(twFidoForm, "sign_data", content.signData);
			createTempInput(twFidoForm, "tbs_encoding", content.tbsEncoding);
			createTempInput(twFidoForm, "hash_algorithm", content.hashAlgorithm);

			twFidoForm.submit();
			twFidoForm.remove();
		}).fail(function(jqXHR, textStatus) {
			makeAlert(textStatus);
		});

	} catch (e) {
		console.log(e);
		makeAlert('初始化行動自然人失敗');
	}
}

function createTempForm(id, target, url) {
	let form = document.createElement("form");
	form.setAttribute("id", id);
	form.setAttribute("method", "post");
	form.setAttribute("action", url);
	form.setAttribute("target", target);
	document.getElementsByTagName("body")[0].appendChild(form);
	return form;
}

function createTempInput(form, name, value) {
	let input = document.createElement("input");
	input.setAttribute("name", name);
	input.value = value;
	form.appendChild(input);
}
</script>

<SCRIPT type="text/javascript" charset="UTF-8">
	

	// Element Id
	const EID_SIGNEDFORM = "signedForm";
	const EID_PIN = "pin";
	const EID_CONTENT = "content"; // 明文
	const EID_SIGNEDDATA = "signedData"; // 簽章結果
	const EID_CARDNO = "cardNo";
	
	var postTarget;
	var timeoutId;
	function postData(target, data) {
		if (!http.sendRequest) {
			return null;
		}
		http.url = target;
		http.actionMethod = "POST";
		var code = http.sendRequest(data);
		if (code != 0)
			return null;
		return http.responseText;
	}
	function checkFinish() {
		if (postTarget) {
			postTarget.close();
			$("#alertMessage").html(alertMessage.HICOS_NOT_WORK_MSG);
			makeDlg("alertMessageDetail");
		}
	}
	function closeNowDlg() {
		$.unblockUI();
		$('body').css('overflow', 'auto'); // 讓本體的捲軸恢復。
	}
	function makeDlg(dlgName) {

		var dlgName = "#" + dlgName;
		var __defaultW = $(window).height() / 3;
		var __dlgW = $(dlgName).width() + 50;
		// 若指定dlg的長度超過預設則直接使用指定長度
		if (__dlgW > __defaultW)
			__defaultW = __dlgW;
		var __centerDlgH = $(dlgName).height() / 2;
		var __centerH = $(window).height() / 2;
		var __top = __centerH - __centerDlgH - 50;
		var __centerDlgW = __dlgW / 2;
		var __centerW = $(window).width() / 2;
		var __left = __centerW - __centerDlgW;
		var __dlgH = $(dlgName).height() + 40; // 避免出現卷軸

		$('body').css('overflow', 'hidden'); // 讓本體的捲軸消失避免讓blockUI滾動滑鼠還收到訊號。
		$.blockUI({
			onOverlayClick : closeNowDlg,
			message : $(dlgName),
			css : {
				// centerX : true,
				width : __defaultW,
				height : __dlgH,
				top : __top,
				left : __left,
				overflow : 'auto', // 捲軸的顯示 【註1】
				margin : 'auto',
			// color : '#2f0000'
			},
			overlayCSS : {
				cursor : ''
			}
		});
	}
	// 提示訊息
	function makeAlert(msg) {
		var showMsg = '<div style="padding: 10px;"><br><h2>'+msg+'</h2><br><div class="main-btn text-center"><div class="btn" onclick="closeNowDlg()">關閉</div></div></div><br>';
		$('body').css('overflow', 'hidden'); // 讓本體的捲軸消失避免讓blockUI滾動滑鼠還收到訊號。
		
		createAutoSizeDlg(showMsg);
	}
	/** 產生一個自動大小的視窗 */
	function createAutoSizeDlg(fmtDiv) {

		var __defaultW = $(window).width() / 3;
		var __defaultH = $(window).height() / 3;
		var __centerDlgW = __defaultW / 2;
		var __centerDlgH = __defaultH / 2;
		var __centerW = $(window).width() / 2;
		var __centerH = $(window).height() / 2;
		var __top = __centerH - __centerDlgH - 20;
		var __left = __centerW - __centerDlgW;

		$.blockUI({
			onOverlayClick : closeNowDlg,
			message : fmtDiv,
			applyPlatformOpacityRules : false,
			css : {
				width : __defaultW,
				top : __top,
				left : __left,
				overflow : 'auto', // 捲軸的顯示 【註1】
				margin : 'auto',
			},
			overlayCSS : {
				cursor : ''
			}
		});

	}
	function makeSignature() {
		var ua = window.navigator.userAgent;
		if (ua.indexOf("MSIE") != -1 || ua.indexOf("Trident") != -1) //is IE, use ActiveX
		{
			postTarget = window.open("http://localhost:61161/waiting.gif",
					"Signing", "height=200, width=200, left=100, top=20");
			var tbsPackage = getTbsPackage();
			document.getElementById("httpObject").innerHTML = '<OBJECT id="http" width=1 height=1 style="LEFT: 1px; TOP: 1px" type="application/x-httpcomponent" VIEWASTEXT></OBJECT>';
			var data = postData("http://localhost:61161/sign", "tbsPackage="
					+ tbsPackage);
			postTarget.close();
			postTarget = null;
			if (!data) {
				$("#alertMessage").html(alertMessage.HICOS_NOT_WORK_MSG);
				makeDlg("alertMessageDetail");	
			}
			else
				setSignature(data);
		} else {
			postTarget = window.open("http://localhost:61161/popupForm", "簽章中",
					"height=280, width=230, left=100, top=20");
			timeoutId = setTimeout(checkFinish, 3500);
		}
	}

	function getTbsPackage() {
		var tbsData = {};
		tbsData["tbs"] = encodeURIComponent(document.getElementById(EID_CONTENT).value);
		tbsData["tbsEncoding"] = "NONE";
		tbsData["hashAlgorithm"] = "SHA256";
		tbsData["withCardSN"] = "false";
		tbsData["pin"] = encodeURIComponent(document.getElementById(EID_PIN).value);
		tbsData["nonce"] = "";
		tbsData["func"] = "MakeSignature";
		tbsData["signatureType"] = "PKCS7";
		var json = JSON.stringify(tbsData);
		return json;
	}

	function setSignature(signature) {
		var ret = JSON.parse(signature);

		if (ret.ret_code != 0) {
			var msg = "簽章檢測失敗, 代碼=" + ret.ret_code.toString(16) + ", 原因:"
					+ ret.message;
			if (ret.last_error)
				msg = msg + ", 次代碼=" + ret.last_error + ", 次原因:" + ret.message2;
			alert(msg);
		} else {
			//簽章成功後執行
			document.getElementById(EID_SIGNEDDATA).value = ret.signature;
			document.getElementById(EID_CARDNO).value = ret.cardSN;
			document.getElementById(EID_SIGNEDFORM).submit();
		}
	}

	function receiveMessage(event) {
		if (console)
			console.debug(event);

		//安全起見，這邊應填入網站位址檢查
		if (event.origin != "http://localhost:61161")
			return;
		try {
			var ret = JSON.parse(event.data);
			if (ret.func) {
				if (ret.func == "getTbs") {
					clearTimeout(timeoutId);

					//簽章
					var json = getTbsPackage();
					postTarget.postMessage(json, "*");
				} else if (ret.func == "sign") {
					setSignature(event.data);
				}
			} else {
				if (console)
					console.error("no func");
			}
		} catch (e) {
			//errorhandle
			if (console)
				console.error(e);
		}
	}

	function addDomain() {
		window.open("http://localhost:61161/addDomain?domain="
				+ window.location.hostname, "AddDomain",
				"height=400, width=400,left=100, top=20");
	}

	if (window.addEventListener) {
		window.addEventListener("message", receiveMessage, false);
	} else {
		//for IE8
		window.attachEvent("onmessage", receiveMessage);
	}

	//for IE8
	var console = console || {
		"log" : function() {
		},
		"debug" : function() {
		},
		"error" : function() {
		}
	};
	
	function gotoRegisterGov(){
		location.href='/raam/govApplyType/init'
	}
	
    function gotoRegisterCorp(){
    	location.href='/raam/corpRegistrationCase/init'
	}
    
    function gotoRegisterSupp(){
    	location.href='/raam/generalBusiRegistration/init'
	}
    
    function gotoGetPwdSupp(){
    	location.href='/raam/getPassword/init/'
	}
    
    function gotoGetPwdGov(){
    	location.href='/raam/getPassword/init/gov'
	}
    
    function gotoLogout(){
    	location.href='/pis/sso/logout'
	}
    
    function gotoPersonal(){
    	location.href='/pis/personal'
	}
</SCRIPT>
<span id="httpObject"></span>

<form method="post" name="signedForm" id="signedForm">
	<input type="hidden" name="loginType" title="loginType" id="loginType">
	
	<input type="hidden" name="userId" title="userId" id="userId">
	<input type="hidden" name="userIdExt" title="userIdExt" id="userIdExt">
	<input type="password" name="password" title="password" id="password" autocomplete="off" style="display: none">
	<input type="hidden" name="imageVerifyToken" id="imageVerifyToken">
	<input type="hidden" name="imageVerifyCode" id="imageVerifyCode">
	
	<input type="hidden" name="signedData" title="signedData" id="signedData">
	<input type="hidden" name="cardNo" title="cardNo" id="cardNo">
	<input type="hidden" id="pin">
	<input type="hidden" id="content">
</form>

<div class="col-xl-3">
	<div class="ru_10" style="margin-bottom: 20px;">
		<div class="bg-bg p10 m0" id="orgLogin" style="font-size:1.25rem">機關代碼</div>
		
			
			

				<div class="p10 plr15">
					<div id="orgAccountMode" style="display: none;">
						<div class="form-check form-check-inline">
							<input class="form-check-input" type="radio" name="orgAccountModeLoginType" title="orgAccountModeLoginType" id="orgAccountModeLoginType1" onclick="orgUseAccount();" value="pwd" checked>
							<label class="form-check-label" for="orgAccountModeLoginType1">帳號</label>
						</div>
						<div class="form-check form-check-inline">
							<input class="form-check-input" type="radio" name="orgAccountModeLoginType" title="orgAccountModeLoginType" id="orgAccountModeLoginType2" onclick="orgUseCertificate();" value="cert">
							<label class="form-check-label" for="orgAccountModeLoginType2">憑證</label>
						</div>
						<div class="form-check">
							<input class="form-check-input" type="radio" name="orgAccountModeLoginType" title="orgAccountModeLoginType" id="orgAccountModeLoginType5" onclick="orgUseMobileCert();" value="mobile">
							<label class="form-check-label" for="orgAccountModeLoginType5">行動自然人憑證</label>
						</div>
							<div class="row pt10">
								<div class="col-7">
									<div class="form-group">
										<label for="orgUserId">帳號：</label>
										<input type="text" class="form-control" id="orgUserId" value='' autocomplete="off">
										<div class="text-red" id="orgUserIdValid">
											
										</div>
									</div>
								</div>
								<div class="col-5">
									<div class="form-group">
										<label for="orgUserIdExt">延伸碼：</label>
										<input type="text" class="form-control" id="orgUserIdExt" value='' autocomplete="off">
										<div class="text-red" id="orgUserIdExtValid">
											
										</div>
									</div>
								</div>
							</div>
							<div class="form-group">
								<label for="orgPassword">密碼：</label>
								<div  style="position: relative;">
									<input type="password" class="form-control" id="orgPassword" autocomplete="off">
									<i data-eyeword="orgPassword" class="fa fa-eye toggle-eye" style=" position: absolute; top:25%; left:90%; color:#00000080;"></i>
								</div>
								<div class="text-red" id="orgPasswordValid">
									
								</div>
							</div>
							<div class="form-group">
								<label>驗證碼(不分大小寫)：</label>
								<div id="captchaOrg"></div>
								<div class="text-red" id="orgImageVerifyCodeValid" style="clear: left;">
					            	
					            </div>
							</div>
							<div class="row text-center">
								<div class="bt_log1">
									<button type="submit" class="btn l-btn btn-block" onclick="orgLogin('pwd');">
										<span style="color: white;" title="登入">登入</span>
									</button>
								</div>
								<div class="bt_log2">
									<button type="submit" class="btn l-btn-a btn-block" onclick="gotoGetPwdGov();">
										<span title="忘記密碼">忘記密碼</span>
									</button>
								</div>
								<div class="col-md-12 pt5">
									<button type="submit" class="btn l-btn btn-block" onclick="gotoRegisterGov()">
										<span style="color: white;" title="機關帳號申請">機關帳號申請</span>
									</button>
								</div>
								<div class="col-md-12 pt5">
									<button type="submit" class="btn l-btn btn-block" onclick="gotoRegisterCorp()">
										<span style="color: white;" title="法人團體帳號申請">法人團體帳號申請</span>
									</button>
								</div>
							</div>
					</div>
					
					<div id="orgCertificateMode" style="display: none;">
						<div class="form-check form-check-inline">
							<input class="form-check-input" type="radio" name="orgAccountModeLoginType" title="orgAccountModeLoginType" id="orgAccountModeLoginType3" onclick="orgUseAccount();" value="pwd">
							<label class="form-check-label" for="orgAccountModeLoginType3">帳號</label>
						</div>
						<div class="form-check form-check-inline">
							<input class="form-check-input" type="radio" name="orgAccountModeLoginType" title="orgAccountModeLoginType" id="orgAccountModeLoginType4" onclick="orgUseCertificate();" value="cert" checked>
							<label class="form-check-label" for="orgAccountModeLoginType4">憑證</label>
						</div>
						<div class="form-check">
							<input class="form-check-input" type="radio" name="orgAccountModeLoginType" title="orgAccountModeLoginType" id="orgAccountModeLoginType6" onclick="orgUseMobileCert();" value="mobile">
							<label class="form-check-label" for="orgAccountModeLoginType6">行動自然人憑證</label>
						</div>
						<div class="form-group">
							<label for="orgPin">請插入憑證：</label>
							<input type="password" class="form-control" id="orgPin" placeholder="請輸入pin碼" autocomplete="off">
							<span id="orgPinError" class="text-red"> 
							</span>
						</div>
						<div class="row text-center">
							<div class="col-md-12 pt5">
								<button type="submit" class="btn l-btn btn-block" onclick="orgLogin('cert');">
									<span style="color: white;" title="登入">登入</span>
								</button>
							</div>
							<div class="col-md-12 pt5">
								<button type="submit" class="btn l-btn btn-block" onclick="gotoRegisterGov()">
									<span style="color: white;" title="機關帳號申請">機關帳號申請</span>
								</button>
							</div>
							<div class="col-md-12 pt5">
								<button type="submit" class="btn l-btn btn-block" onclick="gotoRegisterCorp()">
									<span style="color: white;" title="法人團體帳號申請">法人團體帳號申請</span>
								</button>
							</div>
						</div>
					</div>
					
					<div id="orgMobileCertMode" style="display: none;">
						<div class="form-check form-check-inline">
							<input class="form-check-input" type="radio" name="orgAccountModeLoginType" title="orgAccountModeLoginType" id="orgAccountModeLoginType7" onclick="orgUseAccount();" value="pwd">
							<label class="form-check-label" for="orgAccountModeLoginType7">帳號</label>
						</div>
						<div class="form-check form-check-inline">
							<input class="form-check-input" type="radio" name="orgAccountModeLoginType" title="orgAccountModeLoginType" id="orgAccountModeLoginType8" onclick="orgUseCertificate();" value="cert">
							<label class="form-check-label" for="orgAccountModeLoginType8">憑證</label>
						</div>
						<div class="form-check">
							<input class="form-check-input" type="radio" name="orgAccountModeLoginType" title="orgAccountModeLoginType" id="orgAccountModeLoginType9" onclick="orgUseMobileCert();" value="mobile" checked>
							<label class="form-check-label" for="orgAccountModeLoginType9">行動自然人憑證</label>
						</div>
						<div class="my-3">
							請點選【下一步】，前往行動自然人憑證網站，驗證完成後將導回採購網並進入登入後個人化首頁。<br/>
							<span id="orgMobileError" class="text-red">
								
								
							</span>
						</div>
						<div class="row text-center">
							<div class="col-md-12 pt5">
								<button type="submit" class="btn l-btn btn-block" onclick="orgLogin('mobile');">
									<span style="color: white;" title="下一步">下一步</span>
								</button>
							</div>
							<div class="col-md-12 pt5">
								<button type="submit" class="btn l-btn btn-block" onclick="gotoRegisterGov()">
									<span style="color: white;" title="機關帳號申請">機關帳號申請</span>
								</button>
							</div>
							<div class="col-md-12 pt5">
								<button type="submit" class="btn l-btn btn-block" onclick="gotoRegisterCorp()">
									<span style="color: white;" title="法人團體帳號申請">法人團體帳號申請</span>
								</button>
							</div>
						</div>
					</div>
				</div>
			
		
	</div>

	<div class="ru_10" style="margin-bottom: 20px;">
		<div class="bg-bg-o p10 m0" id="suppLogin" style="font-size:1.25rem">廠商代碼</div>
		
			
			
				
				
				<div class="p10 plr15">
					<div id="suppAccountMode" style="display: none;">
						<div class="form-check form-check-inline">
							<input class="form-check-input" type="radio" name="suppAccountModeLoginType" title="suppAccountModeLoginType" id="suppAccountModeLoginType1" onclick="suppUseAccount();" value="pwd" checked>
							<label class="form-check-label" for="suppAccountModeLoginType1">帳號</label>
						</div>
						<div class="form-check form-check-inline">
							<input class="form-check-input" type="radio" name="suppAccountModeLoginType" title="suppAccountModeLoginType" id="suppAccountModeLoginType2" onclick="suppUseCertificate();" value="cert">
							<label class="form-check-label" for="suppAccountModeLoginType2">憑證</label>
						</div>
						<div class="form-check">
							<input class="form-check-input" type="radio" name="suppAccountModeLoginType" title="suppAccountModeLoginType" id="suppAccountModeLoginType5" onclick="suppUseMobileCert();" value="mobile">
							<label class="form-check-label" for="suppAccountModeLoginType5">行動自然人憑證</label>
						</div>
							<div class="row pt10">
								<div class="col-7">
									<div class="form-group">
										<label for="suppUserId">帳號：</label>
										<input type="text" class="form-control" id="suppUserId" value='' autocomplete="off">
										<div class="text-red" id="suppUserIdValid">
											
										</div>
									</div>
								</div>
								<div class="col-5">
									<div class="form-group">
										<label for="suppUserIdExt">延伸碼：</label>
										
											
											
												<input type="text" id="suppUserIdExt" class="form-control" value='0'>
											
										
										<div class="text-red" id="suppUserIdExtValid">
											
										</div>
									</div>
								</div>
								<div class="col-12 font-weight-bold mb-3" style="color:red;">延伸碼預設為0，毋須更改</div>
							</div>
							<div class="form-group">
								<label for="suppPassword">密碼：</label>
								<div  style="position: relative;">
									<input type="password" class="form-control" id="suppPassword" autocomplete="off">
									<i data-eyeword="suppPassword" class="fa fa-eye toggle-eye"  style=" position: absolute; top:25%; left:90%; color:#00000080;"></i>
								</div>
								<div class="text-red" id="suppPasswordValid">
									
								</div>
							</div>
							<div class="form-group">
								<label>驗證碼(不分大小寫)：</label>
								<div id="captchaSupp"></div>
								<div class="text-red" id="suppImageVerifyCodeValid" style="clear: left;">
					            	
					            </div>
							</div>
							<div class="row text-center">
								<div class="bt_log1">
									<button type="submit" class="btn l-btn-o btn-block" onclick="suppLogin('pwd');">
										<span title="登入">登入</span>
									</button>
								</div>
								<div class="bt_log2">
									<button type="submit" class="btn l-btn-a btn-block" onclick="gotoGetPwdSupp();">
										<span title="忘記帳號或密碼">忘記帳號或密碼</span>
									</button>
								</div>
								<div class="col-md-12 pt5">
									<button type="submit" class="btn l-btn-o btn-block" onclick="gotoRegisterSupp()">
										<span title="登出">廠商帳號申請</span>
									</button>
								</div>
							</div>
					</div>
					
					<div id="suppCertificateMode" style="display: none;">
						<div class="form-check form-check-inline">
							<input class="form-check-input" type="radio" name="suppAccountModeLoginType" title="suppAccountModeLoginType" id="suppAccountModeLoginType3" onclick="suppUseAccount();" value="pwd">
							<label class="form-check-label" for="suppAccountModeLoginType3">帳號</label>
						</div>
						<div class="form-check form-check-inline">
							<input class="form-check-input" type="radio" name="suppAccountModeLoginType" title="suppAccountModeLoginType" id="suppAccountModeLoginType4" onclick="suppUseCertificate();" value="cert" checked>
							<label class="form-check-label" for="suppAccountModeLoginType4">憑證</label>
						</div>
						<div class="form-check">
							<input class="form-check-input" type="radio" name="suppAccountModeLoginType" title="suppAccountModeLoginType" id="suppAccountModeLoginType6" onclick="suppUseMobileCert();" value="mobile">
							<label class="form-check-label" for="suppAccountModeLoginType6">行動自然人憑證</label>
						</div>
						<div class="form-group">
							<label for="suppPin">請插入憑證：</label>
							<input type="password" class="form-control" id="suppPin" placeholder="請輸入pin碼" autocomplete="off">
							<span id="suppPinError" class="text-red"> 
							</span>
						</div>
						<div class="row text-center">
							<div class="col-md-12 pt5">
								<button type="submit" class="btn l-btn-o btn-block" onclick="suppLogin('cert');">
									<span title="登入">登入</span>
								</button>
							</div>
							<div class="col-md-12 pt5">
								<button type="submit" class="btn l-btn-o btn-block" onclick="gotoRegisterSupp()">
									<span title="廠商帳號申請">廠商帳號申請</span>
								</button>
							</div>

						</div>
					</div>
					
					<div id="suppMobileCertMode" style="display: none;">
						<div class="form-check form-check-inline">
							<input class="form-check-input" type="radio" name="suppAccountModeLoginType" title="suppAccountModeLoginType" id="suppAccountModeLoginType7" onclick="suppUseAccount();" value="pwd">
							<label class="form-check-label" for="suppAccountModeLoginType7">帳號</label>
						</div>
						<div class="form-check form-check-inline">
							<input class="form-check-input" type="radio" name="suppAccountModeLoginType" title="suppAccountModeLoginType" id="suppAccountModeLoginType8" onclick="suppUseCertificate();" value="cert">
							<label class="form-check-label" for="suppAccountModeLoginType8">憑證</label>
						</div>
						<div class="form-check">
							<input class="form-check-input" type="radio" name="suppAccountModeLoginType" title="suppAccountModeLoginType" id="suppAccountModeLoginType9" onclick="suppUseMobileCert();" value="mobile" checked>
							<label class="form-check-label" for="suppAccountModeLoginType9">行動自然人憑證</label>
						</div>
						<div class="my-3">
							請點選【下一步】，前往行動自然人憑證網站，驗證完成後將導回採購網並進入登入後個人化首頁。<br/>
							<span id="suppMobileError" class="text-red">
								
								
							</span>
						</div>
						<div class="row text-center">
							<div class="col-md-12 pt5">
								<button type="submit" class="btn l-btn-o btn-block" onclick="suppLogin('mobile');">
									<span title="下一步">下一步</span>
								</button>
							</div>
							<div class="col-md-12 pt5">
								<button type="submit" class="btn l-btn-o btn-block" onclick="gotoRegisterSupp()">
									<span title="廠商帳號申請">廠商帳號申請</span>
								</button>
							</div>

						</div>
					</div>
				</div>
			
		
	</div>
</div>

<div id="alertMessageDetail" style="margin-top: 25px;margin-left: 25px;display: none; cursor: default; width: 600px;" >
		<div class="text-center" style=" font-size: 1.5em;color: #079ba2;border-bottom: thin solid #079BA2;font-weight: 900;margin-bottom: 10px;width: 100%;float: left;">
			提示訊息
		</div>
		<div id="alertMessage" class="text-left">
		</div>
		<div class="main-btn text-center mb-3">
			<div class="btn" onclick="closeNowDlg()">
			   關閉
		   </div>
		</div>
</div>

			<!--登入區塊結束 -->
			</div>
	</div>

	<!--輪播圖-->
	







<script type="text/javascript" src="/ccs/dist/geps3-commons.bundle.js"></script>
<link href="/pis/css/g.css" rel="stylesheet" type="text/css" />

<script>
$(function(){
    $(document).on('keyup keydown keypress', function(e) {
		var keyCode = e.keyCode || e.which;
		if (keyCode == 9 && e.target.name == "carouselNameA") {
			$("#carouselExampleIndicators").carousel('pause');
		}else{
			$("#carouselExampleIndicators").carousel('cycle');
		}
		
	});
    
    const cPrev = document.querySelector('.carousel-control-prev');
    cPrev.addEventListener("focus", (event) => {
      event.target.style = "border-width:3px;border-style:groove;border-color:#000000;padding:5px;";
    });
    cPrev.addEventListener("blur", (event) => {
      event.target.style = "";
    });
    
    const cNext = document.querySelector('.carousel-control-next');
    cNext.addEventListener("focus", (event) => {
      event.target.style = "border-width:3px;border-style:groove;border-color:#000000;padding:5px;";
    });
    cNext.addEventListener("blur", (event) => {
      event.target.style = "";
    });
    
    const carouselNameA = document.querySelectorAll('[name=\'carouselNameA\']');
    
    for (i = 0; i < carouselNameA.length; i++) {
   		if(i <= 2){
   			carouselNameA[i].addEventListener("focus", (event) => {
   	        	event.target.querySelector('img').style = "border-width:1px;border-style:groove;border-color:#000000;padding:0px;";
   	        });
   		}else{
   			carouselNameA[i].addEventListener("focus", (event) => {
   	        	event.target.querySelector('img').style = "border-width:1px;border-style:groove;border-color:#000000;padding:20px;";
   	        });
   			
   		}
    	carouselNameA[i].addEventListener("blur", (event) => {
        	event.target.querySelector('img').style = "";
        });
    }
    
});
</script>
<div class="index_2">
<div class="col-xl-12">
 	<div class="container">
	<div id="carouselExampleIndicators" class="carousel slide" data-ride="carousel" style="">
            
            <ol class="carousel-indicators" style="display:none;">
                <li data-target="#carouselExampleIndicators" data-slide-to="0" class=""></li>
                <li data-target="#carouselExampleIndicators" data-slide-to="1" class="active"></li>
                <li data-target="#carouselExampleIndicators" data-slide-to="2" class=""></li>
            </ol>
            
            <!-- 常用功能總數 -->
            
            <!-- 一頁常用功能數量PC -->
            
            <!-- 一頁常用功能數量Mobile -->
            
            <!-- 區塊數量PC -->
            
            <!-- 區塊數量Mobile -->
            
            <!-- 區塊數量Max -->
            
            
            <div class="carousel-inner">
           	 	
	           	 	<div class="carousel-item active">
	                   	 <div class="icon_4">   
							<div class="pc"> 
							 	
								
									<a href="/qdcs/html/hicos_env_test.htm" name="carouselNameA">
									    <img class="d-block bg4" src="/pis/srml/functionClient/getFunctionImage?id=60000000" alt="安裝程式環境檢測" />
									</a>
								
									<a href="/prkms/tender/common/basic/indexTenderBasic" name="carouselNameA">
									    <img class="d-block bg4" src="/pis/srml/functionClient/getFunctionImage?id=60000005" alt="標案查詢" />
									</a>
								
									<a href="/prkms/today/common/todayTender" name="carouselNameA">
									    <img class="d-block bg4" src="/pis/srml/functionClient/getFunctionImage?id=60000006" alt="今日招標" />
									</a>
								
									<a href="/tps/queryCounter/queryCounterRecords" name="carouselNameA">
									    <img class="d-block bg4" src="/pis/srml/functionClient/getFunctionImage?id=60000007" alt="熱門標案" />
									</a>
								
									<a href="/tps/tom/obtainment/common/printTokenPublic/printVerifyToken" name="carouselNameA">
									    <img class="d-block bg4" src="/pis/srml/functionClient/getFunctionImage?id=60000008" alt="列印領標憑據" />
									</a>
								
									<a href="/esdms/exdm/personalLogin/indexPersonalLogin" name="carouselNameA">
									    <img class="d-block bg4" src="/pis/srml/functionClient/getFunctionImage?id=60000001" alt="專家學者資料維護" />
									</a>
								
									<a href="/vms/mpa/mpaPublicLogin/indexLoginMpaPublic" name="carouselNameA">
									    <img class="d-block bg4" src="/pis/srml/functionClient/getFunctionImage?id=60000009" alt="個別廠商資訊專區" />
									</a>
								
									<a href="/raam/corpRegistrationCase/init" name="carouselNameA">
									    <img class="d-block bg4" src="/pis/srml/functionClient/getFunctionImage?id=60000010" alt="法人團體帳號申請" />
									</a>
								
							</div>
							<div class="mobile">
							 	
								
									<a href="/qdcs/html/hicos_env_test.htm" rel="noopener noreferrer" target="_blank" name="carouselNameA">
								    	<img class="d-block bg4" src="/pis/srml/functionClient/getFunctionImage?id=60000000" alt="安裝程式環境檢測" />
									</a>
								
									<a href="/prkms/tender/common/basic/indexTenderBasic" rel="noopener noreferrer" target="_blank" name="carouselNameA">
								    	<img class="d-block bg4" src="/pis/srml/functionClient/getFunctionImage?id=60000005" alt="標案查詢" />
									</a>
								
									<a href="/prkms/today/common/todayTender" rel="noopener noreferrer" target="_blank" name="carouselNameA">
								    	<img class="d-block bg4" src="/pis/srml/functionClient/getFunctionImage?id=60000006" alt="今日招標" />
									</a>
								
									<a href="/tps/queryCounter/queryCounterRecords" rel="noopener noreferrer" target="_blank" name="carouselNameA">
								    	<img class="d-block bg4" src="/pis/srml/functionClient/getFunctionImage?id=60000007" alt="熱門標案" />
									</a>
								 
						    </div>
						</div>
	                </div>
                
	           	 	<div class="carousel-item ">
	                   	 <div class="icon_4">   
							<div class="pc"> 
							 	
								
									<a href="/qdcs/html/hicos_env_test.htm" name="carouselNameA">
									    <img class="d-block bg4" src="/pis/srml/functionClient/getFunctionImage?id=60000000" alt="安裝程式環境檢測" />
									</a>
								
									<a href="/prkms/tender/common/basic/indexTenderBasic" name="carouselNameA">
									    <img class="d-block bg4" src="/pis/srml/functionClient/getFunctionImage?id=60000005" alt="標案查詢" />
									</a>
								
									<a href="/prkms/today/common/todayTender" name="carouselNameA">
									    <img class="d-block bg4" src="/pis/srml/functionClient/getFunctionImage?id=60000006" alt="今日招標" />
									</a>
								
									<a href="/tps/queryCounter/queryCounterRecords" name="carouselNameA">
									    <img class="d-block bg4" src="/pis/srml/functionClient/getFunctionImage?id=60000007" alt="熱門標案" />
									</a>
								
									<a href="/tps/tom/obtainment/common/printTokenPublic/printVerifyToken" name="carouselNameA">
									    <img class="d-block bg4" src="/pis/srml/functionClient/getFunctionImage?id=60000008" alt="列印領標憑據" />
									</a>
								
									<a href="/esdms/exdm/personalLogin/indexPersonalLogin" name="carouselNameA">
									    <img class="d-block bg4" src="/pis/srml/functionClient/getFunctionImage?id=60000001" alt="專家學者資料維護" />
									</a>
								
									<a href="/vms/mpa/mpaPublicLogin/indexLoginMpaPublic" name="carouselNameA">
									    <img class="d-block bg4" src="/pis/srml/functionClient/getFunctionImage?id=60000009" alt="個別廠商資訊專區" />
									</a>
								
									<a href="/raam/corpRegistrationCase/init" name="carouselNameA">
									    <img class="d-block bg4" src="/pis/srml/functionClient/getFunctionImage?id=60000010" alt="法人團體帳號申請" />
									</a>
								
							</div>
							<div class="mobile">
							 	
								
									<a href="/tps/tom/obtainment/common/printTokenPublic/printVerifyToken" rel="noopener noreferrer" target="_blank" name="carouselNameA">
								    	<img class="d-block bg4" src="/pis/srml/functionClient/getFunctionImage?id=60000008" alt="列印領標憑據" />
									</a>
								
									<a href="/esdms/exdm/personalLogin/indexPersonalLogin" rel="noopener noreferrer" target="_blank" name="carouselNameA">
								    	<img class="d-block bg4" src="/pis/srml/functionClient/getFunctionImage?id=60000001" alt="專家學者資料維護" />
									</a>
								
									<a href="/vms/mpa/mpaPublicLogin/indexLoginMpaPublic" rel="noopener noreferrer" target="_blank" name="carouselNameA">
								    	<img class="d-block bg4" src="/pis/srml/functionClient/getFunctionImage?id=60000009" alt="個別廠商資訊專區" />
									</a>
								
									<a href="/raam/corpRegistrationCase/init" rel="noopener noreferrer" target="_blank" name="carouselNameA">
								    	<img class="d-block bg4" src="/pis/srml/functionClient/getFunctionImage?id=60000010" alt="法人團體帳號申請" />
									</a>
								 
						    </div>
						</div>
	                </div>
                
            </div>
            
            <a class="carousel-control-prev" href="#carouselExampleIndicators" role="button" data-slide="prev">
               	<span class="carousel-control-prev-icon" aria-hidden="true"></span>
                <span class="sr-only">Previous</span>
            </a>
            <a class="carousel-control-next" href="#carouselExampleIndicators" role="button" data-slide="next">
                <span class="carousel-control-next-icon" aria-hidden="true"></span>
                <span class="sr-only">Next</span>
            </a>
        </div>
	</div>       
</div>
</div>

	
	<!--圖表-->
	



	
	<!--左邊-->
	





<style>
.tab_cen{
	/*原本的z-index為999太高 先設為0*/
	z-index : 1;
}
</style>
<script>
$(function(){
    $(document).on('keyup keydown keypress', function(e) {
   	 var keyCode = e.keyCode || e.which;
   	  if (keyCode == 9 && e.target.name == "carouselName") {
    		$("#carouselExampleIndicators_b").carousel('pause');
   	 	}else{
   	 		$("#carouselExampleIndicators_b").carousel('cycle');
   	 	}
	});
    
    const cPrev = document.querySelector('[id=\'cPrev\']');
    cPrev.addEventListener("focus", (event) => {
      event.target.style = "border-width:3px;border-style:groove;border-color:#000000;padding:5px;";
    });
    cPrev.addEventListener("blur", (event) => {	
      event.target.style = "";
    });
    
    const cNext = document.querySelector('[id=\'cNext\']');
    cNext.addEventListener("focus", (event) => {
		event.target.style = "border-width:3px;border-style:groove;border-color:#000000;padding:5px;";
    });
    cNext.addEventListener("blur", (event) => {
      event.target.style = "";
    });
    
	const carouselName = document.querySelectorAll('[name=\'carouselName\']');
    
    for (i = 0; i < carouselName.length; i++) {
   		carouselName[i].addEventListener("focus", (event) => {
        	event.target.querySelector('img').style = "border-width:1px;border-style:groove;border-color:#000000;";
        });
    	carouselName[i].addEventListener("blur", (event) => {
        	event.target.querySelector('img').style = "";
        });
    }
    
});
</script>
<div class="middle_1_cen">
	<div class="middle_4">
		<div id="tab-demo">
			<div class="index_2" style="padding-top: 20px;">
				<div class="container" style="margin-bottom: 20px">
					<div id="tab-demo">
						<ul class="tab-title">
							<li class="active"><a href="#tab01" title="工程會訊息">工程會訊息</a></li>
							<li><a href="https://www.pcc.gov.tw/" title="前往工程會網站(另開頁面)" rel="noopener noreferrer" target="_blank">工程會網站</a></li>
							<li><a href="/csci/pf/readCsciFaqStatList" title="熱門問答集">熱門問答集</a></li>
							<li><a href="/csci/pf/feedback" title="請求協助">請求協助</a></li>
						</ul>
						<div id="tab01" class="tab-inner" style="">
							<ul class="tab_cen">
								
									
										
											
											<a href="/pis/pia/client/readBulletinDetail?pkPaiBulletin=70000980" title="前往 人口販運防制法於112年6月14日修正通過，並於113年1月1日施行，增訂廠商有違反人口販運防制法第41條第1項情形者，5年內不得參與政府採購。">
												<li>人口販運防制法於112年6月14日修正通過，並於113年1月1日施行，&nbsp;........... <span class="date">113/04/15</span>
												</li>
											</a>
										
									
									
								
							</ul>
						</div>
					</div>
					<div class="main-btn text-center">
						<div class="btn w170" tabindex="0" id="bulletinListid" onclick="location.href='/pis/pia/client/readEngBulletinList'">更多資訊</div>
					</div>
				</div>
			</div>
			<div class="index_2">
				<div class="m_h2"></div>
				<div class="col-xl-12">
					<div class="container">
						<div id="carouselExampleIndicators_b" class="carousel slide" data-ride="carousel">
							<ol class="carousel-indicators" style="display: none">
								<li data-target="#carouselExampleIndicators_b" data-slide-to="0" class="active"></li>
								<li data-target="#carouselExampleIndicators_b" data-slide-to="1"></li>
								<li data-target="#carouselExampleIndicators_b" data-slide-to="2"></li>
							</ol>
							<div class="icon_4all">
								<div class="carousel-inner">
									<div class="carousel-item active">
										<div class="pc">
											<div class="icon_4">
												<a href="https://www.moeaidb.gov.tw/external/ctlr?PRO=bid.rwdBidList&cate=2699" title="產業創新條例第58條公告(另開頁面)" rel="noopener noreferrer" target="_blank" name="carouselName">
													<img src="/pis/images/v2/4icon7.svg" alt="產業創新條例第58條公告" class="d-block bg4_a">
												</a>
												<a href="/pis/carezone/readCarezone" title="新創專區" name="carouselName">
													<img src="/pis/images/v2/4icon2.svg" alt="新創專區" class="d-block bg4_b">
												</a>
												<a href="https://si.taiwan.gov.tw/Home/buyingpower/start" title="前往社會企業登入平台(另開頁面)" rel="noopener noreferrer" target="_blank" name="carouselName">
													<img src="/pis/images/v2/4icon9.svg" alt="社會企業登入平台" class="d-block bg4_a">
												</a>
												<a href="https://sunshine.cy.gov.tw/" title="前往政治獻金停聽看(另開頁面)" rel="noopener noreferrer" target="_blank" name="carouselName">
													<img src="/pis/images/v2/4icon10.svg" alt="政治獻金停聽看" class="d-block bg4_b">
												</a>
											</div>
										</div>
										<div class="mobile">
											<div class="icon_4">
												<a href="https://www.moeaidb.gov.tw/external/ctlr?PRO=bid.rwdBidList&cate=2699" title="產業創新條例第58條公告(另開頁面)" rel="noopener noreferrer" target="_blank" name="carouselName">
													<img src="/pis/images/v2/4icon7.svg" alt="產業創新條例第58條公告" class="d-block bg4_a">
												</a>
												<a href="/pis/carezone/readCarezone" title="新創專區" name="carouselName">
													<img src="/pis/images/v2/4icon2.svg" alt="新創專區" class="d-block bg4_b">
												</a>
												<a href="https://si.taiwan.gov.tw/Home/buyingpower/start" title="前往社會企業登入平台(另開頁面)" rel="noopener noreferrer" target="_blank" name="carouselName">
													<img src="/pis/images/v2/4icon9.svg" alt="社會企業登入平台" class="d-block bg4_a">
												</a>
											</div>
										</div>
									</div>
									<div class="carousel-item">
										<div class="pc">
											<div class="icon_4">
												<a href="/pis/apa/gotoApaPage" title="前往購買原住民身心障礙產品或勞務" name="carouselName">
													<img src="/pis/images/v2/4icon8.svg" alt="購買原住民身心障礙產品或勞務" class="d-block bg4_a" >
												</a>
												<a href="/pis/srml/linkGroupClient/readLinkGroupClient?id=50003007" title="前往行政法人相關採購資訊" rel="noopener noreferrer" target="_blank" name="carouselName">
													<img src="/pis/images/v2/4icon3.svg" alt="行政法人相關採購資訊" class="d-block bg4_b">
												</a>
												<a href="https://ebuying.hinet.net/epvas/" title="前往加值服務(另開頁面)" rel="noopener noreferrer" target="_blank" name="carouselName">
													<img src="/pis/images/v2/4icon11.svg" alt="加值服務" class="d-block bg4_a">
												</a>
												<a href="https://e-pay.hinet.net/" title="前往購買點數(另開頁面)" rel="noopener noreferrer" target="_blank" name="carouselName">
													<img src="/pis/images/v2/4icon5.svg" alt="購買點數" class="d-block bg4_b">
												</a>
											</div>
										</div>
										<div class="mobile">
											<div class="icon_4">
												<a href="https://sunshine.cy.gov.tw/" title="前往政治獻金停聽看(另開頁面)" rel="noopener noreferrer" target="_blank" name="carouselName">
													<img src="/pis/images/v2/4icon10.svg" alt="政治獻金停聽看" class="d-block bg4_a">
												</a>
												
												<a href="/pis/apa/gotoApaPage" title="前往購買原住民身心障礙產品或勞務" name="carouselName">
													<img src="/pis/images/v2/4icon8.svg" alt="購買原住民身心障礙產品或勞務" class="d-block bg4_b">
												</a>
												<a href="/pis/srml/linkGroupClient/readLinkGroupClient?id=50003007" title="前往行政法人相關採購資訊" rel="noopener noreferrer" target="_blank" name="carouselName">
													<img src="/pis/images/v2/4icon3.svg" alt="行政法人相關採購資訊" class="d-block bg4_a">
												</a>
											</div>
										</div>
									</div>
									<div class="carousel-item">
										<div class="pc">
											<div class="icon_4">
												<a href="https://ppp.mof.gov.tw/WWW/inv_ann.aspx" title="前往促進民間參與公共建設公告(另開視窗)" rel="noopener noreferrer" target="_blank" name="carouselName">
													<img src="/pis/images/v2/4icon6.svg" alt="促進民間參與公共建設公告" class="d-block bg4_a">
												</a>
											</div>
										</div>
										<div class="mobile">
											<div class="icon_4">
												<a href="https://ebuying.hinet.net/epvas/" title="前往加值服務(另開頁面)" rel="noopener noreferrer" target="_blank" name="carouselName">
													<img src="/pis/images/v2/4icon11.svg" alt="加值服務" class="d-block bg4_a">
												</a>
												<a href="https://e-pay.hinet.net/" title="前往購買點數(另開頁面)" rel="noopener noreferrer" target="_blank" name="carouselName">
													<img src="/pis/images/v2/4icon5.svg" alt="購買點數" class="d-block bg4_b">
												</a>
												<a href="https://ppp.mof.gov.tw/WWW/inv_ann.aspx" title="前往促進民間參與公共建設公告(另開視窗)" rel="noopener noreferrer" target="_blank" name="carouselName">
													<img src="/pis/images/v2/4icon6.svg" alt="促進民間參與公共建設公告" class="d-block bg4_a">
												</a>
											</div>
										</div>
									</div>
								</div>
							</div>
							<a class="carousel-control-prev" href="#carouselExampleIndicators_b" role="button" data-slide="prev" id="cPrev">
								<span class="carousel-control-prev-icon" aria-hidden="true"></span> <span class="sr-only">Previous</span>
							</a>
							<a class="carousel-control-next" href="#carouselExampleIndicators_b" role="button" data-slide="next" id="cNext">
								<span class="carousel-control-next-icon" aria-hidden="true"></span> <span class="sr-only">Next</span>
							</a>
						</div>
					</div>
				</div>
				<div class="m_h2"></div>
			</div>
		</div>
	</div>
</div>

<script>
window.onload = new function() {
	var IEV = IEVersion();
	if(IEV!=-1&&IEV!='edge') {
		alert("政府電子採購網已不支援IE(Internet Explorer)瀏覽器，建議改用Chrome或Edge瀏覽器，詳如系統公告。");
	}
	
	//var isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
	let isSafari = navigator.vendor.match(/apple/i) &&
    !navigator.userAgent.match(/crios/i) &&
    !navigator.userAgent.match(/fxios/i) &&
    !navigator.userAgent.match(/Opera|OPT\//);
	if (isSafari) {
		alert("您使用之Safari瀏覽器目前無法支援電子領標及投標功能，建議改用Chrome或Edge瀏覽器。");
	}
	
}

$("#bulletinListid").keypress(function (e) {
    if (e.which == 13) {
    	location.href='/pis/pia/client/readEngBulletinList';
    } 
});

function IEVersion() {
    var userAgent = navigator.userAgent; //取得瀏覽器的userAgent字串  
    var isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1; //判斷是否IE<11瀏覽器  
    var isEdge = userAgent.indexOf("Edge") > -1 && !isIE; //判斷是否IE的Edge瀏覽器  
    var isIE11 = userAgent.indexOf('Trident') > -1 && userAgent.indexOf("rv:11.0") > -1;
    if(isIE) {
        var reIE = new RegExp("MSIE (\\d+\\.\\d+);");
        reIE.test(userAgent);
        var fIEVersion = parseFloat(RegExp["$1"]);
        if(fIEVersion == 7) {
            return 7;
        } else if(fIEVersion == 8) {
            return 8;
        } else if(fIEVersion == 9) {
            return 9;
        } else if(fIEVersion == 10) {
            return 10;
        } else {
            return 6;//IE版本<=7
        }   
    } else if(isEdge) {
        return 'edge';//edge
    } else if(isIE11) {
        return 11; //IE11  
    }else{
        return -1;//不是ie瀏覽器
    }
}
</script>
	<!--頁簽結束-->

	<!--footer-->
	 <footer class=" ftbg">
	 <div class="container">
	 <div class="pc">
	 





<div id="listBtn" tabindex="0" onclick="listBtn()"> <div style="margin-bottom: 0.5rem;font-weight: 500;line-height: 1.2;color: #fff;font-size: 1.5em;width: 64px;margin-top: -47px;margin-left: -29px;">展開</div> </div> <div id="textlistn" style="display: none;">    <div class="foot_all">    <!--第一階選項 -->   <div class="foot_L">    <div class="foot_a">網站導覽</div>    <!--第一階選項結束 -->        	 	<div class="foot_a">網站導覽</div>   </div>   <!--第三階選項 -->   <div class="foot_R">    <a href="/pis/prac/sitemapClient/readG3Sitemap">    <div class="foot_b">網站導覽(三代)</div></a>    <a href="/pis/prac/sitemapClient/readG2Sitemap">    <div class="foot_b">網站導覽(二代)</div></a>   </div>   <!--第三階選項結束 -->     </div>  <!--第二階選項結束 -->   <div class="foot_all">    <!--第一階選項 -->   <div class="foot_L">    <div class="foot_a">學習資源</div>    <!--第一階選項結束 -->        	 	<div class="foot_a">教育訓練</div>   </div>   <!--第三階選項 -->   <div class="foot_R">    <a href="/ol/entityCourse/index">    <div class="foot_b">機關端(線上報名)</div></a>    <a href="/ol/trgEnroll/index">    <div class="foot_b">廠商端(線上報名)</div></a>   </div>   <!--第三階選項結束 -->     </div>  <div class="foot_all">    <!--第一階選項 -->   <div class="foot_L">        	 	<div class="foot_a"></div>    <!--第一階選項結束 -->        	 	<div class="foot_a">線上學習</div>   </div>   <!--第三階選項 -->   <div class="foot_R">    <a href="/qdcs/trainingIndex">    <div class="foot_b">線上教學</div></a>   </div>   <!--第三階選項結束 -->     </div>  <div class="foot_all">    <!--第一階選項 -->   <div class="foot_L">        <!--第一階選項結束 -->        	 	<div class="foot_a_2">教學資料(下載專區)</div>   </div>   <!--第三階選項 -->   <div class="foot_R">    <a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=20000003">    <div class="foot_b">系統使用手冊</div></a>    <a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=60000731">    <div class="foot_b">三代採購網教育訓練教材</div></a>    <a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=60000736">    <div class="foot_b">三代教學影片</div></a>    <a href="/pis/prac/downloadGroupClient/getClientDownloadForDownloadFile?id=70000016">    <div class="foot_b">環境檢測安裝步驟及FAQ</div></a>    <a href="/pis/prac/downloadGroupClient/getClientDownloadForDownloadFile?id=70000015">    <div class="foot_b">用戶端程式偵測障礙排除</div></a>    <a href="/pis/prac/downloadGroupClient/readPreparation">    <div class="foot_b">第一次使用三代政府電子採購網之前置作業</div></a>   </div>   <!--第三階選項結束 -->     </div>  <div class="foot_all">    <!--第一階選項 -->   <div class="foot_L">        <!--第一階選項結束 -->        	 	<div class="foot_a_2">採購專業人員訓練</div>   </div>   <!--第三階選項 -->   <div class="foot_R">    <a href="/psms/pptom/pptomPublic/indexRead">    <div class="foot_b">代訓機構名單</div></a>    <a href="/psms/pptcm/pptcmPublic/indexReadPptcm">    <div class="foot_b">代訓機構開課</div></a>    <a href="/psms/plrtqdm/questionPublic/indexReadQuestion">    <div class="foot_b">採購法規題庫</div></a>    <a href="/psms/plrtm/plrtmQuery/indexReadCertificate">    <div class="foot_b">採購專業人員及格證書查詢</div></a>    <a href="/psms/plrtm/plrtmReissue/certificateReissue">    <div class="foot_b">採購專業人員及格證書補發申請</div></a>   </div>   <!--第三階選項結束 -->     </div>  <!--第二階選項結束 -->   <div class="foot_all">    <!--第一階選項 -->   <div class="foot_L">    <div class="foot_a">採購作業</div>    <!--第一階選項結束 -->        	 	<div class="foot_a">標案查詢</div>   </div>   <!--第三階選項 -->   <div class="foot_R">    <a href="#" onclick="goTender()">    <div class="foot_b">標案查詢</div>    </a>   </div>   <!--第三階選項結束 -->     </div>  <div class="foot_all">    <!--第一階選項 -->   <div class="foot_L">        	 	<div class="foot_a"></div>    <!--第一階選項結束 -->        	 	<div class="foot_a">等標期</div>   </div>   <!--第三階選項 -->   <div class="foot_R">    <a href="/pis/prac/declarationClient/bWait">    <div class="foot_b">等標期</div></a>   </div>   <!--第三階選項結束 -->     </div>  <div class="foot_all">    <!--第一階選項 -->   <div class="foot_L">        	 	<div class="foot_a"></div>    <!--第一階選項結束 -->        	 	<div class="foot_a">電子公報</div>   </div>   <!--第三階選項 -->   <div class="foot_R">    <a href="/ebm/public/EbmMain/indexEbmMain">    <div class="foot_b">電子公報</div></a>   </div>   <!--第三階選項結束 -->     </div>  <!--第二階選項結束 -->   <div class="foot_all">    <!--第一階選項 -->   <div class="foot_L">    <div class="foot_a">查詢服務</div>    <!--第一階選項結束 -->        	 	<div class="foot_a">標案相關</div>   </div>   <!--第三階選項 -->   <div class="foot_R">    <a href="#" onclick="goTender()">    <div class="foot_b">標案查詢</div>    </a>    <a href="/prkms/gpaPredict/common/indexGpaPredict">    <div class="foot_b">採購預告查詢</div></a>    <a href="/prkms/tpRead/common/indexTpRead">    <div class="foot_b">公開閱覽查詢</div></a>    <a href="/prkms/tpAppeal/common/indexTpAppeal">    <div class="foot_b">公開徵求查詢</div></a>    <a href="/opas/pspad/pspadClient/readPublicPspadListInit">    <div class="foot_b">公示送達查詢</div></a>    <a href="/cscps/ciom/medicineSearch">    <div class="foot_b">衛材藥品查詢</div></a>    <a href="/prkms/priority/common/indexTenderPriority">    <div class="foot_b">優先採購查詢</div></a>    <a href="/osm/public/proctrg">    <div class="foot_b">採購標的分類</div></a>   </div>   <!--第三階選項結束 -->     </div>  <div class="foot_all">    <!--第一階選項 -->   <div class="foot_L">        	 	<div class="foot_a"></div>    <!--第一階選項結束 -->        	 	<div class="foot_a">財物相關</div>   </div>   <!--第三階選項 -->   <div class="foot_R">    <a href="/opas/arpam/public/indexArpam">    <div class="foot_b">財物出租查詢</div></a>    <a href="/opas/aspam/public/indexAspam">    <div class="foot_b">財物變賣查詢</div></a>    <a href="/tps/pia/PiaPriceWithoutSsoController/query/commonPiaPriceSearch">    <div class="foot_b">物調公告查詢</div></a>   </div>   <!--第三階選項結束 -->     </div>  <div class="foot_all">    <!--第一階選項 -->   <div class="foot_L">        	 	<div class="foot_a"></div>    <!--第一階選項結束 -->        	 	<div class="foot_a">國外採購</div>   </div>   <!--第三階選項 -->   <div class="foot_R"> 	   <a href="https://gpa.taiwantrade.com.tw/zh/home" title="前往全球政府採購商機(另開頁面)" rel="noopener noreferrer" target="_blank"> 	   <div class="foot_b">全球政府採購商機</div></a> 	   <a href="https://www.pcc.gov.tw/pcc/content/index?eid=1980&amp;type=C" title="前往外國政府採購網站(另開頁面)" rel="noopener noreferrer" target="_blank"> 	   <div class="foot_b">外國政府採購網站</div></a> 	   <a href="/prkms/foreignGov/common/indexTenderForeignGov" title="前往外國政府採購商情"> 	   <div class="foot_b">外國政府採購商情</div></a>   </div>     </div>  <div class="foot_all">    <!--第一階選項 -->   <div class="foot_L">        	 	<div class="foot_a"></div>    <!--第一階選項結束 -->        	 	<div class="foot_a">廠商相關</div>   </div>   <!--第三階選項 -->   <div class="foot_R">    <a href="/osm/public/foreSupp">    <div class="foot_b">外國廠商代碼</div></a>    <a href="/vms/stiem/stiemPublic/indexSearchPublic">    <div class="foot_b">科技研究機構</div></a>    <a href="/peems/lapeem/lapeemGeneralQuery">    <div class="foot_b">效益評估查詢</div></a>    <a href="/vms/jgvm/jgvmPublic/indexReadJgvmPublic">    <div class="foot_b">連帶保證廠商</div></a>    <a href="/vms/emlm/emlmPublicSearch/indexSearchEmlmPublic">    <div class="foot_b">優良廠商名單</div></a>    <a href="/vms/rvlm/rvlmPublicSearch/indexSearchRvlmPublic">    <div class="foot_b">拒絕往來廠商</div></a>    <a href="/vms/rvlm/rvlmPublicSearchRevoked/indexSearchRevokedRvlmPublic">    <div class="foot_b">註銷拒絕往來廠商</div></a>    <a href="/vms/gblm/gblmPublicSearch/indexSearchGblmPublic">    <div class="foot_b">全球化廠商名單</div></a>    <a href="/vms/gblm/gblmPublicCompositeSearch/indexCompositeSearch">    <div class="foot_b">廠商綜合查詢</div></a>    <a href="/tps/tom/obtainment/common/printTokenPublic/printVerifyToken">    <div class="foot_b">列印領標憑據</div></a>    <a href="/qdcs/yellowPageQuery">    <div class="foot_b">廠商名錄</div></a>    <a href="/piat/dispute/oneOOne/indexSearch">    <div class="foot_b">政府採購法第101條停權案例</div></a>   </div>   <!--第三階選項結束 -->     </div>  <div class="foot_all">    <!--第一階選項 -->   <div class="foot_L">        	 	<div class="foot_a"></div>    <!--第一階選項結束 -->        	 	<div class="foot_a">其他</div>   </div>   <!--第三階選項 -->   <div class="foot_R">    <a href="/prkms/rebuild/common/indexRebuild">    <div class="foot_b">災區重建工程案件查詢</div></a>    <a href="/peems/lapeem/lapeemGeneralPolit">    <div class="foot_b">政治獻金法第七條查詢</div></a>    <a href="/tps/common/atmAbroad/commonSearchAtmAbroad/search">    <div class="foot_b">得標外國政府採購案件查詢</div></a>    <a href="/tps/tom/thdTender/query/enterToThdTender">    <div class="foot_b">歷史文件瀏覽</div></a>    <a href="/pec/PecQueryPublic/indexPecQueryPublicInit">    <div class="foot_b">採購評選委員名單</div></a>    <a href="/wr-report/wr/homeClient/list">    <div class="foot_b">採購統計</div></a>    <a href="/pis/bsmizone/readBsmizone">    <div class="foot_b">商品檢驗查詢</div></a>   </div>   <!--第三階選項結束 -->     </div>  <!--第二階選項結束 -->   <div class="foot_all">    <!--第一階選項 -->   <div class="foot_L">    <div class="foot_a">下載專區</div>    <!--第一階選項結束 -->    <div class="foot_a">下載專區</div>   </div>   <!--下載專區 > 下載專區 > 第三階選項 -->   <div class="foot_R">    <a href="/pis/prac/declarationClient/bWait" title="前往等標期(另開頁面)" rel="noopener noreferrer" target="_blank">    <div class="foot_b">等標期</div>    </a>     <a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=50003004" title="前往拒絕往來名單">    <div class="foot_b">拒絕往來名單</div>    </a>    <a href="https://www.pcc.gov.tw/content/list?eid=9807&lang=1" title="前往招標文件範本(另開頁面)" rel="noopener noreferrer" target="_blank">    <div class="foot_b">招標文件範本</div>    </a>     <a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=42000000" title="前往共同供應契約">    <div class="foot_b">共同供應契約</div>    </a>    <a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=20000003" title="前往系統使用手冊">    <div class="foot_b">系統使用手冊</div>    </a>    <a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=60000736" title="前往三代教學影片">    <div class="foot_b">三代教學影片</div>    </a>    <a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=20000004" title="前往常用工具下載">    <div class="foot_b">常用工具下載</div>    </a>    <a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=60000731" title="前往教育訓練教材">    <div class="foot_b">教育訓練教材</div>    </a>    <a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=70000000" title="前往安裝程式環境檢測及障礙排除說明">    <div class="foot_b">安裝程式環境檢測及障礙排除說明</div>    </a>    <a href="/tps/tp/OpenData/showList" title="前往資料集下載(另開頁面)" rel="noopener noreferrer" target="_blank">    <div class="foot_b">資料集下載</div>    </a>     <a href="https://web.pcc.gov.tw/tps/tp/OpenData/showGPAList" title="前往近半年GPA資料集下載(另開頁面)" rel="noopener noreferrer" target="_blank">    <div class="foot_b">近半年GPA資料集下載</div>    </a>     <a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=60000716" title="前往帳號處理">    <div class="foot_b">帳號處理</div>    </a>    <a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=50003000" title="前往重要訊息通告">    <div class="foot_b">重要訊息通告</div>    </a>    <a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=70000026" title="前往ODF專區">    <div class="foot_b">ODF專區</div>    </a>    <a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=50003006" title="前往中央組改文件 ">    <div class="foot_b">中央組改文件 </div>    </a>    <a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=50003002" title="前往５都改制文件">    <div class="foot_b">５都改制文件</div>    </a>   </div>   <!--下載專區 > 下載專區 > 第三階選項結束 -->   </div>  <!--第二階選項結束 -->   <div class="foot_all">    <!--第一階選項 -->   <div class="foot_L">    <div class="foot_a">相關連結</div>    <!--第一階選項結束 -->        	 	<div class="foot_a">憑證</div>   </div>   <!--第三階選項 -->   <div class="foot_R">    <a href="https://gcp.nat.gov.tw/">    <div class="foot_b">政府憑證管理中心</div></a>    <a href="https://moeaca.nat.gov.tw/">    <div class="foot_b">工商憑證管理中心</div></a>    <a href="https://moica.nat.gov.tw/main.html">    <div class="foot_b">自然人憑證管理中心</div></a>    <a href="https://xca.nat.gov.tw/">    <div class="foot_b">組織及團體憑證管理中心</div></a>    <a href="https://fido.moi.gov.tw/pt/">    <div class="foot_b">內政部行動自然人憑證系統</div></a>   </div>   <!--第三階選項結束 -->     </div>  <div class="foot_all">    <!--第一階選項 -->   <div class="foot_L">        <!--第一階選項結束 -->        	 	<div class="foot_a_2">行政院工程會全球資訊網</div>   </div>   <!--第三階選項 -->   <div class="foot_R">    <a href="https://www.pcc.gov.tw/">    <div class="foot_b">行政院工程會全球資訊網</div></a>   </div>   <!--第三階選項結束 -->     </div>  <div class="foot_all">    <!--第一階選項 -->   <div class="foot_L"> 	   	 	<div class="foot_a"></div>    <!--第一階選項結束 -->       	 	<div class="foot_a">其他</div>       </div>   <!--相關連結 > 其他 > 第三階選項 -->   <div class="foot_R">    <a href="https://pcic.pcc.gov.tw/pwc-web/" title="前往工程標案管理系統(另開頁面)" rel="noopener noreferrer" target="_blank"> 	<div class="foot_b">工程標案管理系統</div> 	</a>     <a href="https://pcic.pcc.gov.tw/pwc-web/" title="前往工程延遲付款通報(另開頁面)" rel="noopener noreferrer" target="_blank"> 	<div class="foot_b">工程延遲付款通報</div> 	</a>     <a href="https://ppp.mof.gov.tw/WWW/inv_ann.aspx" title="前往促進民間參與公共建設公告(另開頁面)" rel="noopener noreferrer" target="_blank"> 	<div class="foot_b">促進民間參與公共建設公告</div> 	</a>     <a href="https://gcis.nat.gov.tw/mainNew/" title="前往經濟部商工行政服務入口網(另開頁面)" rel="noopener noreferrer" target="_blank"> 	<div class="foot_b">經濟部商工行政服務入口網</div> 	</a>     <a href="https://pcic.pcc.gov.tw/pwc-web/" title="前往技師與工程技術顧問公司管理資訊系統(另開頁面)" rel="noopener noreferrer" target="_blank"> 	<div class="foot_b">技師與工程技術顧問公司管理資訊系統</div> 	</a>     <a href="https://cloudbm.nlma.gov.tw/CPTL/index.jsp" title="前往內政部國土管理署全國建築管理資訊入口網(另開頁面)" rel="noopener noreferrer" target="_blank"> 	<div class="foot_b">內政部國土管理署全國建築管理資訊入口網</div> 	</a>     <a href="https://www.etax.nat.gov.tw" title="前往財政部稅務入口網(另開頁面)" rel="noopener noreferrer" target="_blank"> 	<div class="foot_b">財政部稅務入口網</div> 	</a>     <a href="https://www.energylabel.org.tw/purchasing/govgreen/list.aspx" title="前往經濟部能源署節能標章綠色採購網(另開頁面)" rel="noopener noreferrer" target="_blank"> 	<div class="foot_b">經濟部能源署節能標章綠色採購網</div> 	</a>     <a href="https://dpws.sfaa.gov.tw/index.jsp" title="前往衛生福利部社會及家庭署身心障礙服務入口網(另開頁面)" rel="noopener noreferrer" target="_blank"> 	<div class="foot_b">衛生福利部社會及家庭署身心障礙服務入口網</div> 	</a>     <a href="https://socialcom.mohw.gov.tw/swalPublic/front/taIndex" title="前往衛生福利部社福人員勞動申訴及溝通平臺-違規停止補助名單(另開頁面)" rel="noopener noreferrer" target="_blank"> 	<div class="foot_b">衛生福利部社福人員勞動申訴及溝通平臺-違規停止補助名單</div> 	</a>     <a href="https://cloudbm.nlma.gov.tw/bccs/login" title="前往內政部國土管理署工程重機械編管及運用管理系統(另開頁面)" rel="noopener noreferrer" target="_blank"> 	<div class="foot_b">內政部國土管理署工程重機械編管及運用管理系統</div> 	</a>     		<a href="/pis/srml/linkGroupClient/readLinkGroupClient?id=50003006" title="前往經濟部(投資審議司)公告陸資資訊"> 			<div class="foot_b">經濟部(投資審議司)公告陸資資訊</div> 		</a>    <a href="https://www.mine.gov.tw/" title="前往經濟部地質調查及礦業管理中心(另開頁面)" rel="noopener noreferrer" target="_blank"> 	<div class="foot_b">經濟部地質調查及礦業管理中心</div> 	</a>     <a href="https://pcic.pcc.gov.tw/pwc-web/service/bidCae001" title="前往廠商承攬公共工程履歷查詢(另開頁面)" rel="noopener noreferrer" target="_blank"> 	<div class="foot_b">廠商承攬公共工程履歷查詢</div> 	</a>     <a href="https://pcic.pcc.gov.tw/pwc-web/service/bidCab001" title="前往公共工程人員履歷查詢(另開頁面)" rel="noopener noreferrer" target="_blank"> 	<div class="foot_b">公共工程人員履歷查詢</div> 	</a>     <a href="https://pcic.pcc.gov.tw/pwc-web/service/eng0814Query" title="前往技師懲戒資料(另開頁面)" rel="noopener noreferrer" target="_blank"> 	<div class="foot_b">技師懲戒資料</div> 	</a>     <a href="https://cloudbm.nlma.gov.tw/CPTL/cpt0503m.do" title="前往內政部國土管理署營造業評鑑結果查詢(另開頁面)" rel="noopener noreferrer" target="_blank"> 	<div class="foot_b">內政部國土管理署營造業評鑑結果查詢</div> 	</a>     		<a href="/pis/srml/linkGroupClient/readLinkGroupClient?id=50003007" title="前往行政法人相關採購資訊"> 			<div class="foot_b">行政法人相關採購資訊</div> 		</a>    <a href="https://ebuying.hinet.net" title="前往採購網加值服務訂閱(另開頁面)" rel="noopener noreferrer" target="_blank"> 	<div class="foot_b">採購網加值服務訂閱</div> 	</a>     <a href="https://ccs.hinet.net/line_pwd_cht_big5.htm" title="前往更改HiNet連線密碼(另開頁面)" rel="noopener noreferrer" target="_blank"> 	<div class="foot_b">更改HiNet連線密碼</div> 	</a>     <a href="https://e-pay.hinet.net/" title="前往HiNet點數卡購買(另開頁面)" rel="noopener noreferrer" target="_blank"> 	<div class="foot_b">HiNet點數卡購買</div> 	</a>     <a href="https://announcement.mol.gov.tw/" title="前往勞動部「違反勞動法令事業單位(雇主)查詢系統」(另開頁面)" rel="noopener noreferrer" target="_blank"> 	<div class="foot_b">勞動部「違反勞動法令事業單位(雇主)查詢系統」</div> 	</a>    </div>   <!--相關連結 > 其他 > 第三階選項結束 -->   </div>  <!--第二階選項結束 -->   <div class="foot_all">    <!--第一階選項 -->   <div class="foot_L">    <div class="foot_a">請求協助</div>    <!--第一階選項結束 -->        	 	<div class="foot_a">熱門問答</div>   </div>   <!--第三階選項 -->   <div class="foot_R">    <a href="/csci/pf/readCsciFaqStatList">    <div class="foot_b">熱門問答</div></a>   </div>   <!--第三階選項結束 -->     </div>  <div class="foot_all">    <!--第一階選項 -->   <div class="foot_L">        	 	<div class="foot_a"></div>    <!--第一階選項結束 -->        	 	<div class="foot_a">問題檢索</div>   </div>   <!--第三階選項 -->   <div class="foot_R">    <a href="/csci/pf/readCsciFaqDetlList">    <div class="foot_b">問題檢索</div></a>   </div>   <!--第三階選項結束 -->     </div>  <div class="foot_all">    <!--第一階選項 -->   <div class="foot_L">        	 	<div class="foot_a"></div>    <!--第一階選項結束 -->        	 	<div class="foot_a">我要發問</div>   </div>   <!--第三階選項 -->   <div class="foot_R">    <a href="/csci/pf/feedback">    <div class="foot_b">我要發問</div></a>   </div>   <!--第三階選項結束 -->     </div>  <!--第二階選項結束 -->   <div class="foot_all">    <!--第一階選項 -->   <div class="foot_L">    <div class="foot_a">最新功能</div>    <!--第一階選項結束 -->        	 	<div class="foot_a">最新功能</div>   </div>   <!--第三階選項 -->   <div class="foot_R">    <a href="/osm/public/newSysOn">    <div class="foot_b">最新功能</div></a>   </div>   <!--第三階選項結束 -->     </div>  <!--第二階選項結束 -->  </div>
	
<!--footer-->
<div class="foot_last" style="float: left; width: 100%; margin-bottom: 20px;">
	<div class="col-xl-12">
		<div>
			<img src="/pis/images/v2/logo.png" style="margin-bottom: 20px;float: left; width: 288px; margin-right: 15px" alt="行政院工共工程委員會圖示">
			<a target="_blank" rel="noopener noreferrer" href="https://accessibility.moda.gov.tw/Applications/Detail?category=20230224201311" title="無障礙網站(另開視窗)">
			<img src="/pis/images/ap2.0.png" alt="無障礙網站2.0"></a>
			<img src="/pis/images/qr.jpg" style="position: absolute;right: 5%;bottom: -20px;" alt="行動版QR Code圖示" title="行動版QR Code圖示">
		</div>
	</div>
	<div style="width: 100%;float: left;">
		<p>
			<span>免費系統客服電話：0800-080-512 系統客服傳真：02-33229691 緊急聯絡電話：(07)2280795</span>
			<span>工程會電話：(02)87897500 工程會地址：110207臺北市信義區松仁路3號9樓</span>
			<span>採購申訴審議委員會電話：(02)87897530 中央採購稽核小組電話：(02)87897548 傳真：(02)87897554</span>
			<span>政府電子採購網 版權所有 © 2021 歡迎本區第 <strong class="counterValue"></strong> 位訪客 (至098/12/31累計76003935人次)</span>
		</p>
		<p>
			<span>支援瀏覽器版本為Edge及Firefox及Chrome瀏覽器解析度1280 X 960台灣時間為 <strong id="nowTimeSpan"></strong></span>
		</p>
	</div>
</div>
<div class="col-xl-12 pd0">	
	<div class="foot_last2">
		<a href="/pis/pia/client/mail" title="前往聯絡我們"><span class="foot_last2_a"><img src="images/link.png" alt="">聯絡我們</span></a>
		<a href="/pis/prac/declarationClient/security" title="前往安全保護聲明"><span class="foot_last2_a"><img src="images/link.png" alt="">安全保護聲明</span></a>
		<a href="/pis/prac/declarationClient/private" title="前往個人隱私聲明"><span class="foot_last2_a"><img src="images/link.png" alt="">個人隱私聲明</span></a>
		<a href="/pis/prac/declarationClient/right" title="前往著作權聲明"><span class="foot_last2_a"><img src="images/link.png" alt="">著作權聲明</span></a>
		<a href="/osm/public/sysDownRcd" title="前往停機紀錄"><span class="foot_last2_a"><img src="images/link.png" alt="">停機紀錄</span></a>
		<a href="/pis/prac/declarationClient/accessibility" title="前往無障礙聲明"><span class="foot_last2_a"><img src="images/link.png" alt="">無障礙聲明</span></a>
	</div>
</div>

<!--footerEND-->

<script type="text/javascript">
	$(document).ready(function() {
		generateNowTime();
		setInterval("generateNowTime()", 10000);
		// 20110714 顯示訪客人數
		var isT5Env = '';
		if(isT5Env != 'Y') {
			getCounterValue(); 
		}
	});

	function generateNowTime() {
		var time = new Date();
		var nowstr = time.getHours() + '點' + time.getMinutes() + '分';
		$("#nowTimeSpan").text(nowstr);
	}

	function getCounterValue() {
		jQuery.ajax({
			url : "/pis/pia/client/counter/counterValue?refresh=Y",
			type : "POST",
			dataType : "html",
			timeout : 30000,
			error : function(XMLHttpRequest, textStatus, errorThrown) {
				if (textStatus == 'timeout') {
					// timeout
					jQuery('strong.counterValue').text('');
				} else {
					// error
					jQuery('strong.counterValue').text('error');
				}
				ajaxEvent = false;
			},
			beforeSend : function() {
				// busy
				ajaxEvent = true;
			},
			complete : function() {
				ajaxEvent = false;
			},
			success : function(doc) {
				if (doc.toLowerCase) {
					if (doc.toLowerCase().indexOf("</body>") != -1) {
						window.location.href = "/pis";
					} else {
						jQuery('strong.counterValue').text(doc);
					}
				}
			}
		});
	}

	function refreshNewCount() {

		jQuery.ajax({
			url : "/pis/pia/client/counter/refreshNewCount?refresh=Y",
			type : "POST",
			dataType : "html",
			timeout : 30000,
			error : function(XMLHttpRequest, textStatus, errorThrown) {
				if (textStatus == 'timeout') {
					// timeout
					jQuery('strong.counterValue').text('');
				} else {
					// error
					jQuery('strong.counterValue').text('error');
				}
				ajaxEvent = false;
			},
			beforeSend : function() {
				// busy
				ajaxEvent = true;
			},
			complete : function() {
				ajaxEvent = false;
			},
			success : function(doc) {
				ajaxEvent = false;
			}
		});
	}

	function goTender() {
		refreshNewCount();
		window.location.href = "/prkms/tender/common/basic/indexTenderBasic";
	}
	
	$("#listBtn").keypress(function (e) {
        if (e.which == 13) {
            listBtn();
        } 
    });
	
</script>
	</div>
	


<div class="mobile">
	<div class="foot_last">
		<div class="col-xl-12"><div class="col-xs-12" style="text-align: center; margin-top: 20px; margin-bottom: 10px"><img src="/pis/images/v2/logo.png" width="70%" alt="行政院工共工程委員會圖示"></div></div>
		<div class="col-xl-12 pd0">
			<span>免費系統客服電話：0800-080-512</span><br>
			<span>系統客服傳真：02-33229691</span><br>
			<span>緊急聯絡電話：(07)2280795</span><br>
			<span>工程會電話：(02)87897500</span><br>
			<span>工程會地址：110207臺北市信義區松仁路3號9樓</span><br>
			<span>採購申訴審議委員會電話：(02)87897530</span><br>
			<span>中央採購稽核小組電話：(02)87897548</span><br>
			<span>傳真：(02)87897554</span><br>
			<span>政府電子採購網 版權所有 © 2021<br>歡迎本區第 <strong class="counterValue"></strong> 位訪客   <br>(至098/12/31累計76003935人次)</span><br>
		</div><br><br>
		<div class="col-xl-12 pd0">	
			<div class="foot_last2">
				
					
					
						<a href="/pis/pia/client/mail" title="前往聯絡我們"><div class="foot_last2_a"><img src="/pis/images/v2/link.png" alt="">聯絡我們</div></a>
						<a href="/pis/prac/declarationClient/security" title="前往安全保護聲明"><div class="foot_last2_a"><img src="/pis/images/v2/link.png" alt="">安全保護聲明</div></a>
						<a href="/pis/prac/declarationClient/private" title="前往個人隱私聲明"><div class="foot_last2_a"><img src="/pis/images/v2/link.png" alt="">個人隱私聲明</div></a>
						<a href="/pis/prac/declarationClient/right" title="前往著作權聲明"><div class="foot_last2_a"><img src="/pis/images/v2/link.png" alt="">著作權聲明</div></a>
						<a href="/osm/public/sysDownRcd" title="前往停機紀錄"><div class="foot_last2_a"><img src="/pis/images/v2/link.png" alt="">停機紀錄</div></a>
						<a href="/pis/prac/declarationClient/accessibility" title="前往無障礙聲明"><div class="foot_last2_a"><img src="/pis/images/v2/link.png" alt="">無障礙聲明</div></a>
					
				
			</div>
		</div>
		<div class="col-xl-12 pd0" style="margin-bottom: 30px;width: 100%;float: left;">	
			<span>支援瀏覽器版本為<br>Edge及Firefox及Chrome<br>瀏覽器解析度1280 X 960台灣時間為 <strong id="nowTimeSpan"></strong></span>
		</div>
	</div>
	</div>
	</div>
	</footer>
	<!--footerEND-->
	
<script>

	$(function(){
	setLoginBreadTitle();
		});

	//設定TITLE
	function setLoginBreadTitle() {
		
		//取得 func path
		var funcUrl = "/pis/menu/readFuncPath";
		$.ajax({
			url : funcUrl,
			type : "GET",
		}).done(function(content) {
			if(!Array.isArray(content)) {
				return;
			}
			
			if(content.length>=2){
				console.log(content[content.length-2]+content[content.length-1]);
				
				document.title = '政府電子採購網-'+content[content.length-2]+content[content.length-1];
				
			}

		}).fail(function(jqXHR, textStatus) {
			console.error('readFuncPath error ' + textStatus);
		});
	}
</script>	
	
</body>


	
	
		<script src="/pis/js/v2/mobile-nav.js" type="text/javascript"></script>
	
  

</html>