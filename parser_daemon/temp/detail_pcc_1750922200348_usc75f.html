<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">







<html lang="zh-Hant-TW" xml:lang="zh-tw">
<head>
<meta charset="UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=edge"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
<meta property="og:image" content="/tps/images/icon.gif" />




<title>政府電子採購網</title>
<link rel="icon" href="/tps/images/icon.gif">

<!--練習區滑鼠跟隨游標CSS設定及是否「練習區」-->


<!--練習區滑鼠跟隨游標CSS設定及是否「練習區」-->

<style>
input:focus {
    color: #495057;
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgb(0 123 255 / 25%);
}

textarea:focus {
    color: #495057;
    background-color: #fff;
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgb(0 123 255 / 25%);
}

select:focus-visible {
    color: #495057;
    background-color: #fff;
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgb(0 123 255 / 25%);
}

</style>

<link href="/tps/css/all.css" rel="stylesheet" type="text/css"/>

<link href="/tps/css/bootstrap.css" rel="stylesheet" type="text/css"/>
<link href="/tps/css/v2/font-awesome/css/font-awesome.css" rel="stylesheet"/>
<link href="/tps/css/tab_menu.css" rel="stylesheet" type="text/css"/> 
<link href="/tps/css/tab.css" rel="stylesheet" type="text/css"/>
<link href="/tps/css/index_tab.css" type="text/css" rel="stylesheet"/><!--首頁最上方下拉選單-->



<script src="/tps/js/calendar.js"></script><!--日期--> 
<script src="/tps/js/jquery-3.5.0.min.js"></script><!--函式庫--> 
<script src="/tps/js/tab.js"></script><!--頁簽--> 
<script src="/tps/js/menu.js" type="text/javascript"></script><!--下拉選單-->
<script src="/tps/js/index_tab.js" type="text/javascript"></script><!--首頁最上方下拉選單-->
<script src="/tps/js/jquery.blockUI.js"></script>
<script src="/tps/js/jquery.validate.min.js" type="text/javascript"></script>
<script src="/tps/js/additional-methods.js" type="text/javascript"></script>



<meta name="google-site-verification" content="_RsXySMeLvSPD4xblNnXL5YTiToz0qucp2NflC2YnO8" />
</head>

<body style="background:#ddd5b8">
<!--TOP-->

	
	
		<div class="top">
			

<noscript>
<td style="color:red;">
您的瀏覽器不支援JavaScript功能，若網頁功能無法正常使用時，請開啟瀏覽器JavaScript狀態
</td>
</noscript>
<style>
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only-focusable:active, .sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  overflow: visible;
  clip: auto;
  white-space: normal;
}
</style>
<!--TOP-->
<div class="top">
	<div class="top_a">
	  <div class="top_a_cen">
	    <div class="top_a_l">
	      <ul>
	      	<li><a href="#content" tabindex="5" class="sr-only sr-only-focusable"><span style="color:#FFFFFF;background-color:#81b1c9;margin-right:5px;Opacity:80;">中央內容區塊</span></a></li>
	        <!--上方內容導盲磚--> 
	        <li><a href="#U" id="AU" name="U" title="上方功能區塊" class="guide" accesskey="U" tabindex="0">:::</a></li>
	        <li><a  href="/pis/" title="回首頁">回首頁</a></li>
	        <li><a  href="/pis/prac/sitemapClient/readG3Sitemap" title="網站導覽">網站導覽</a></li>
	        <li><a  href="/qdcs/webmobile/Home" title="行動版">行動版</a></li>
	        <li><a  href="/tps/QueryTender/query/queryEng?mode=" title="ENGLISH">ENGLISH</a></li>
	        <li><a  href="/pis/pia/client/mail" title="意見信箱">意見信箱</a></li>
<!-- 			<li><a  href="/csci/pf/feedback" title="試辦問題回報">試辦問題回報</a></li> -->
	      </ul>
	    </div>
	   <div class="top_a_r">
			<div class="top_a_r_1">

				<a href="https://web.pcc.gov.tw/pis/" title="前往正式區的按鈕"> 
				<img src="/tps/images/r_btn1.png" alt="前往正式區的按鈕">
				</a>
			</div>
			<div class="top_a_r_2">

				<a href="https://webtest.pcc.gov.tw/pis/" title="前往練習區的按鈕"> 	
                 <img src="/tps/images/r_btn2.png" alt="前往練習區的按鈕">
				</a>
			</div>
			</div>
		</div>
	</div>
	<div class="top_b">
		<h1>政府電子採購網</h1>
	</div>
			
			





<!--BANNER--> <div class="top_c"> 	<div class="top_ca"> 		<div class="nav"> 			<ul class="menu"> 			 			    			    	<!--第一階選項 --> 					<li class="li_tp fix_menu"> 						<a href="#" class="bind_menu_a" data-layerid="layer1">網站導覽</a> 					</li> 					<!--第一階選項結束 --> 					 					<div class="sub_menu bind_menu_a" id="layer1" data-layerid="layer1" style="display: none;"> 							<i></i> 							<div class="sub_area clearfix" style="margin-bottom:20px"> 							<!--第二階選項 --> 							 							 							    			 							    			 							    			<ul class="sub_nav list" style="margin-bottom:20px"> 							    			 												<li class="li_tp tm"><a class="tmc">網站導覽</a> 													<!--第三階選項 --> 													<ul class="sub02"> 																	<li class="tpb_a"><a href="/pis/prac/sitemapClient/readG3Sitemap"class="tmd">網站導覽(三代)</a></li> 																	<li class="tpb_a"><a href="/pis/prac/sitemapClient/readG2Sitemap"class="tmd">網站導覽(二代)</a></li> 													</ul> 													<!--第三階選項結束 --> 												</li> 											 							    			</ul> 							<!--第二階選項結束 --> 							 							</div> 						</div> 			    	<!--第一階選項 --> 					<li class="li_tp fix_menu"> 						<a href="#" class="bind_menu_a" data-layerid="layer2">學習資源</a> 					</li> 					<!--第一階選項結束 --> 					 					<div class="sub_menu bind_menu_a" id="layer2" data-layerid="layer2" style="display: none;"> 							<i></i> 							<div class="sub_area clearfix" style="margin-bottom:20px"> 							<!--第二階選項 --> 							 							 							    			 							    			 							    			<ul class="sub_nav list" style="margin-bottom:20px"> 							    			 												<li class="li_tp tm"><a class="tmc">教育訓練</a> 													<!--第三階選項 --> 													<ul class="sub02"> 																	<li class="tpb_a"><a href="/ol/entityCourse/index"class="tmd">機關端(線上報名)</a></li> 																	<li class="tpb_a"><a href="/ol/trgEnroll/index"class="tmd">廠商端(線上報名)</a></li> 													</ul> 													<!--第三階選項結束 --> 												</li> 											 							    			</ul> 							    			 							    			 							    			<ul class="sub_nav list" style="margin-bottom:20px"> 							    			 												<li class="li_tp tm"><a class="tmc">線上學習</a> 													<!--第三階選項 --> 													<ul class="sub02"> 																	<li class="tpb_a"><a href="/qdcs/trainingIndex"class="tmd">線上教學</a></li> 													</ul> 													<!--第三階選項結束 --> 												</li> 											 							    			</ul> 							    			 							    			 							    			<ul class="sub_nav list" style="margin-bottom:20px"> 							    			 												<li class="li_tp tm"><a class="tmc">教學資料(下載專區)</a> 													<!--第三階選項 --> 													<ul class="sub02"> 																	<li class="tpb_a"><a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=20000003"class="tmd">系統使用手冊</a></li> 																	<li class="tpb_a"><a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=60000731"class="tmd">三代採購網教育訓練教材</a></li> 																	<li class="tpb_a"><a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=60000736"class="tmd">三代教學影片</a></li> 																	<li class="tpb_a"><a href="/pis/prac/downloadGroupClient/getClientDownloadForDownloadFile?id=70000016"class="tmd">環境檢測安裝步驟及FAQ</a></li> 																	<li class="tpb_a"><a href="/pis/prac/downloadGroupClient/getClientDownloadForDownloadFile?id=70000015"class="tmd">用戶端程式偵測障礙排除</a></li> 																	<li class="tpb_a"><a href="/pis/prac/downloadGroupClient/readPreparation"class="tmd">第一次使用三代政府電子採購網之前置作業</a></li> 													</ul> 													<!--第三階選項結束 --> 												</li> 											 							    			</ul> 							    			 							    			 							    			<ul class="sub_nav list" style="margin-bottom:20px"> 							    			 												<li class="li_tp tm"><a class="tmc">採購專業人員訓練</a> 													<!--第三階選項 --> 													<ul class="sub02"> 																	<li class="tpb_a"><a href="/psms/pptom/pptomPublic/indexRead"class="tmd">代訓機構名單</a></li> 																	<li class="tpb_a"><a href="/psms/pptcm/pptcmPublic/indexReadPptcm"class="tmd">代訓機構開課</a></li> 																	<li class="tpb_a"><a href="/psms/plrtqdm/questionPublic/indexReadQuestion"class="tmd">採購法規題庫</a></li> 																	<li class="tpb_a"><a href="/psms/plrtm/plrtmQuery/indexReadCertificate"class="tmd">採購專業人員及格證書查詢</a></li> 																	<li class="tpb_a"><a href="/psms/plrtm/plrtmReissue/certificateReissue"class="tmd">採購專業人員及格證書補發申請</a></li> 													</ul> 													<!--第三階選項結束 --> 												</li> 											 							    			</ul> 							<!--第二階選項結束 --> 							 							</div> 						</div> 			    	<!--第一階選項 --> 					<li class="li_tp fix_menu"> 						<a href="#" class="bind_menu_a" data-layerid="layer3">採購作業</a> 					</li> 					<!--第一階選項結束 --> 					 					<div class="sub_menu bind_menu_a" id="layer3" data-layerid="layer3" style="display: none;"> 							<i></i> 							<div class="sub_area clearfix" style="margin-bottom:20px"> 							<!--第二階選項 --> 							 							 							    			 							    			 							    			<ul class="sub_nav list" style="margin-bottom:20px"> 							    			 												<li class="li_tp tm"><a class="tmc">標案查詢</a> 													<!--第三階選項 --> 													<ul class="sub02"> 																	<li class="tpb_a"><a href="#" onclick="goTender()" class="tmd">標案查詢</a></li> 													</ul> 													<!--第三階選項結束 --> 												</li> 											 							    			</ul> 							    			 							    			 							    			<ul class="sub_nav list" style="margin-bottom:20px"> 							    			 												<li class="li_tp tm"><a class="tmc">等標期</a> 													<!--第三階選項 --> 													<ul class="sub02"> 																	<li class="tpb_a"><a href="/pis/prac/declarationClient/bWait"class="tmd">等標期</a></li> 													</ul> 													<!--第三階選項結束 --> 												</li> 											 							    			</ul> 							    			 							    			 							    			<ul class="sub_nav list" style="margin-bottom:20px"> 							    			 												<li class="li_tp tm"><a class="tmc">電子公報</a> 													<!--第三階選項 --> 													<ul class="sub02"> 																	<li class="tpb_a"><a href="/ebm/public/EbmMain/indexEbmMain"class="tmd">電子公報</a></li> 													</ul> 													<!--第三階選項結束 --> 												</li> 											 							    			</ul> 							<!--第二階選項結束 --> 							 							</div> 						</div> 			    	<!--第一階選項 --> 					<li class="li_tp fix_menu"> 						<a href="#" class="bind_menu_a" data-layerid="layer4">查詢服務</a> 					</li> 					<!--第一階選項結束 --> 					 					<div class="sub_menu bind_menu_a" id="layer4" data-layerid="layer4" style="display: none;"> 							<i></i> 							<div class="sub_area clearfix" style="margin-bottom:20px"> 							<!--第二階選項 --> 							 							 							    			 							    			 							    			<ul class="sub_nav list" style="margin-bottom:20px"> 							    			 												<li class="li_tp tm"><a class="tmc">標案相關</a> 													<!--第三階選項 --> 													<ul class="sub02"> 																	<li class="tpb_a"><a href="#" onclick="goTender()" class="tmd">標案查詢</a></li> 																	<li class="tpb_a"><a href="/prkms/gpaPredict/common/indexGpaPredict"class="tmd">採購預告查詢</a></li> 																	<li class="tpb_a"><a href="/prkms/tpRead/common/indexTpRead"class="tmd">公開閱覽查詢</a></li> 																	<li class="tpb_a"><a href="/prkms/tpAppeal/common/indexTpAppeal"class="tmd">公開徵求查詢</a></li> 																	<li class="tpb_a"><a href="/opas/pspad/pspadClient/readPublicPspadListInit"class="tmd">公示送達查詢</a></li> 																	<li class="tpb_a"><a href="/cscps/ciom/medicineSearch"class="tmd">衛材藥品查詢</a></li> 																	<li class="tpb_a"><a href="/prkms/priority/common/indexTenderPriority"class="tmd">優先採購查詢</a></li> 																	<li class="tpb_a"><a href="/osm/public/proctrg"class="tmd">採購標的分類</a></li> 													</ul> 													<!--第三階選項結束 --> 												</li> 											 							    			</ul> 							    			 							    			 							    			<ul class="sub_nav list" style="margin-bottom:20px"> 							    			 												<li class="li_tp tm"><a class="tmc">財物相關</a> 													<!--第三階選項 --> 													<ul class="sub02"> 																	<li class="tpb_a"><a href="/opas/arpam/public/indexArpam"class="tmd">財物出租查詢</a></li> 																	<li class="tpb_a"><a href="/opas/aspam/public/indexAspam"class="tmd">財物變賣查詢</a></li> 																	<li class="tpb_a"><a href="/tps/pia/PiaPriceWithoutSsoController/query/commonPiaPriceSearch"class="tmd">物調公告查詢</a></li> 													</ul> 													<!--第三階選項結束 --> 												</li> 											 							    			</ul> 							    			 							    			 							    			<ul class="sub_nav list" style="margin-bottom:20px"> 							    			 												<li class="li_tp tm"><a class="tmc">國外採購</a> 													<!--第三階選項 --> 													<ul class="sub02"> 																<li class="tpb_a"><a href="https://gpa.taiwantrade.com.tw/zh/home"class="tmd"title="前往全球政府採購商機(另開頁面)"rel="noopener noreferrer"target="_blank">全球政府採購商機</a></li> 																<li class="tpb_a"><a href="https://www.pcc.gov.tw/pcc/content/index?eid=1980&amp;type=C"class="tmd"title="前往外國政府採購網站(另開頁面)"rel="noopener noreferrer"target="_blank">外國政府採購網站</a></li> 																<li class="tpb_a"><a href="/prkms/foreignGov/common/indexTenderForeignGov"class="tmd"title="前往外國政府採購商情">外國政府採購商情</a></li> 													</ul> 													<!--第三階選項結束 --> 												</li> 																	 							    			</ul> 							    			 							    			 							    			<ul class="sub_nav list" style="margin-bottom:20px"> 							    			 												<li class="li_tp tm"><a class="tmc">廠商相關</a> 													<!--第三階選項 --> 													<ul class="sub02"> 																	<li class="tpb_a"><a href="/osm/public/foreSupp"class="tmd">外國廠商代碼</a></li> 																	<li class="tpb_a"><a href="/vms/stiem/stiemPublic/indexSearchPublic"class="tmd">科技研究機構</a></li> 																	<li class="tpb_a"><a href="/peems/lapeem/lapeemGeneralQuery"class="tmd">效益評估查詢</a></li> 																	<li class="tpb_a"><a href="/vms/jgvm/jgvmPublic/indexReadJgvmPublic"class="tmd">連帶保證廠商</a></li> 																	<li class="tpb_a"><a href="/vms/emlm/emlmPublicSearch/indexSearchEmlmPublic"class="tmd">優良廠商名單</a></li> 																	<li class="tpb_a"><a href="/vms/rvlm/rvlmPublicSearch/indexSearchRvlmPublic"class="tmd">拒絕往來廠商</a></li> 																	<li class="tpb_a"><a href="/vms/rvlm/rvlmPublicSearchRevoked/indexSearchRevokedRvlmPublic"class="tmd">註銷拒絕往來廠商</a></li> 																	<li class="tpb_a"><a href="/vms/gblm/gblmPublicSearch/indexSearchGblmPublic"class="tmd">全球化廠商名單</a></li> 																	<li class="tpb_a"><a href="/vms/gblm/gblmPublicCompositeSearch/indexCompositeSearch"class="tmd">廠商綜合查詢</a></li> 																	<li class="tpb_a"><a href="/tps/tom/obtainment/common/printTokenPublic/printVerifyToken"class="tmd">列印領標憑據</a></li> 																	<li class="tpb_a"><a href="/qdcs/yellowPageQuery"class="tmd">廠商名錄</a></li> 																	<li class="tpb_a"><a href="/piat/dispute/oneOOne/indexSearch"class="tmd">政府採購法第101條停權案例</a></li> 													</ul> 													<!--第三階選項結束 --> 												</li> 											 							    			</ul> 							    			 							    			 							    			<ul class="sub_nav list" style="margin-bottom:20px"> 							    			 												<li class="li_tp tm"><a class="tmc">其他</a> 													<!--第三階選項 --> 													<ul class="sub02"> 																	<li class="tpb_a"><a href="/prkms/rebuild/common/indexRebuild"class="tmd">災區重建工程案件查詢</a></li> 																	<li class="tpb_a"><a href="/peems/lapeem/lapeemGeneralPolit"class="tmd">政治獻金法第七條查詢</a></li> 																	<li class="tpb_a"><a href="/tps/common/atmAbroad/commonSearchAtmAbroad/search"class="tmd">得標外國政府採購案件查詢</a></li> 																	<li class="tpb_a"><a href="/tps/tom/thdTender/query/enterToThdTender"class="tmd">歷史文件瀏覽</a></li> 																	<li class="tpb_a"><a href="/pec/PecQueryPublic/indexPecQueryPublicInit"class="tmd">採購評選委員名單</a></li> 																	<li class="tpb_a"><a href="/wr-report/wr/homeClient/list"class="tmd">採購統計</a></li> 																	<li class="tpb_a"><a href="/pis/bsmizone/readBsmizone"class="tmd">商品檢驗查詢</a></li> 													</ul> 													<!--第三階選項結束 --> 												</li> 											 							    			</ul> 							<!--第二階選項結束 --> 							 							</div> 						</div> 			    	<!--第一階選項 --> 					<li class="li_tp fix_menu"> 						<a href="#" class="bind_menu_a" data-layerid="layer5">下載專區</a> 					</li> 					<!--第一階選項結束 --> 					 					<div class="sub_menu bind_menu_a" id="layer5" data-layerid="layer5" style="display: none;"> 							<i></i> 							<div class="sub_area clearfix" style="margin-bottom:20px"> 							<!--第二階選項 --> 							 							 							    			 							    			 							    			<ul class="sub_nav list" style="margin-bottom:20px"> 							    			 												<li class="li_tp tm"><a class="tmc">下載專區</a> 													<!--下載專區 > 下載專區 > 第三階選項 --> 													<ul class="sub02"> 																<li class="tpb_a"><a href="/pis/prac/declarationClient/bWait"class="tmd" title="前往等標期(另開頁面)" rel="noopener noreferrer"target="_blank">等標期</a></li> 																<li class="tpb_a"><a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=50003004"class="tmd" title="前往拒絕往來名單">拒絕往來名單</a></li> 																<li class="tpb_a"><a href="https://www.pcc.gov.tw/content/list?eid=9807&lang=1"class="tmd" title="前往招標文件範本(另開頁面)" rel="noopener noreferrer"target="_blank">招標文件範本</a></li> 																<li class="tpb_a"><a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=42000000"class="tmd" title="前往共同供應契約">共同供應契約</a></li> 																<li class="tpb_a"><a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=20000003"class="tmd" title="前往系統使用手冊">系統使用手冊</a></li> 																<li class="tpb_a"><a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=60000736"class="tmd" title="前往三代教學影片">三代教學影片</a></li> 																<li class="tpb_a"><a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=20000004"class="tmd" title="前往常用工具下載">常用工具下載</a></li> 																<li class="tpb_a"><a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=60000731"class="tmd" title="前往教育訓練教材">教育訓練教材</a></li> 																<li class="tpb_a"><a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=70000000"class="tmd" title="前往安裝程式環境檢測及障礙排除說明">安裝程式環境檢測及障礙排除說明</a></li> 																<li class="tpb_a"><a href="/tps/tp/OpenData/showList"class="tmd" title="前往資料集下載(另開頁面)" rel="noopener noreferrer"target="_blank">資料集下載</a></li> 																<li class="tpb_a"><a href="https://web.pcc.gov.tw/tps/tp/OpenData/showGPAList"class="tmd" title="前往近半年GPA資料集下載(另開頁面)" rel="noopener noreferrer"target="_blank">近半年GPA資料集下載</a></li> 																<li class="tpb_a"><a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=60000716"class="tmd" title="前往帳號處理">帳號處理</a></li> 																<li class="tpb_a"><a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=50003000"class="tmd" title="前往重要訊息通告">重要訊息通告</a></li> 																<li class="tpb_a"><a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=70000026"class="tmd" title="前往ODF專區">ODF專區</a></li> 																<li class="tpb_a"><a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=50003006"class="tmd" title="前往中央組改文件 ">中央組改文件 </a></li> 																<li class="tpb_a"><a href="/pis/prac/downloadGroupClient/readDownloadGroupClient?id=50003002"class="tmd" title="前往５都改制文件">５都改制文件</a></li> 													</ul> 													<!--下載專區 > 下載專區 > 第三階選項結束 --> 												</li> 											 							    			</ul> 							<!--第二階選項結束 --> 							 							</div> 						</div> 			    	<!--第一階選項 --> 					<li class="li_tp fix_menu"> 						<a href="#" class="bind_menu_a" data-layerid="layer6">相關連結</a> 					</li> 					<!--第一階選項結束 --> 					 					<div class="sub_menu bind_menu_a" id="layer6" data-layerid="layer6" style="display: none;"> 							<i></i> 							<div class="sub_area clearfix" style="margin-bottom:20px"> 							<!--第二階選項 --> 							 							 							    			 							    			 							    			<ul class="sub_nav list" style="margin-bottom:20px"> 							    			 												<li class="li_tp tm"><a class="tmc">憑證</a> 													<!--第三階選項 --> 													<ul class="sub02"> 																		<li class="tpb_a"> 																			<a href="https://gcp.nat.gov.tw/"class="tmd"title="前往政府憑證管理中心(另開頁面)"rel="noopener noreferrer"target="_blank">政府憑證管理中心</a> 																		</li> 																		<li class="tpb_a"> 																			<a href="https://moeaca.nat.gov.tw/"class="tmd"title="前往工商憑證管理中心(另開頁面)"rel="noopener noreferrer"target="_blank">工商憑證管理中心</a> 																		</li> 																		<li class="tpb_a"> 																			<a href="https://moica.nat.gov.tw/main.html"class="tmd"title="前往自然人憑證管理中心(另開頁面)"rel="noopener noreferrer"target="_blank">自然人憑證管理中心</a> 																		</li> 																		<li class="tpb_a"> 																			<a href="https://xca.nat.gov.tw/"class="tmd"title="前往組織及團體憑證管理中心(另開頁面)"rel="noopener noreferrer"target="_blank">組織及團體憑證管理中心</a> 																		</li> 																		<li class="tpb_a"> 																			<a href="https://fido.moi.gov.tw/pt/"class="tmd"title="前往內政部行動自然人憑證系統(另開頁面)"rel="noopener noreferrer"target="_blank">內政部行動自然人憑證系統</a> 																		</li> 													</ul> 													<!--第三階選項結束 --> 												</li> 											 							    			</ul> 							    			 							    			 							    			<ul class="sub_nav list" style="margin-bottom:20px"> 							    			 												<li class="li_tp tm"><a class="tmc">行政院工程會全球資訊網</a> 													<!--第三階選項 --> 													<ul class="sub02"> 																		<li class="tpb_a"> 																			<a href="https://www.pcc.gov.tw/"class="tmd"title="前往行政院工程會全球資訊網(另開頁面)"rel="noopener noreferrer"target="_blank">行政院工程會全球資訊網</a> 																		</li> 													</ul> 													<!--第三階選項結束 --> 												</li> 											 							    			</ul> 							    			 							    			 							    			<ul class="sub_nav list" style="margin-bottom:20px"> 							    			 												<li class="li_tp tm"><a class="tmc">其他</a> 													<!--相關連結 > 其他 > 第三階選項 --> 													<ul class="sub02"> 																<li class="tpb_a"><a href="https://pcic.pcc.gov.tw/pwc-web/"class="tmd" title="前往工程標案管理系統(另開頁面)" rel="noopener noreferrer"target="_blank">工程標案管理系統</a></li> 																<li class="tpb_a"><a href="https://pcic.pcc.gov.tw/pwc-web/"class="tmd" title="前往工程延遲付款通報(另開頁面)" rel="noopener noreferrer"target="_blank">工程延遲付款通報</a></li> 																<li class="tpb_a"><a href="https://ppp.mof.gov.tw/WWW/inv_ann.aspx"class="tmd" title="前往促進民間參與公共建設公告(另開頁面)" rel="noopener noreferrer"target="_blank">促進民間參與公共建設公告</a></li> 																<li class="tpb_a"><a href="https://gcis.nat.gov.tw/mainNew/"class="tmd" title="前往經濟部商工行政服務入口網(另開頁面)" rel="noopener noreferrer"target="_blank">經濟部商工行政服務入口網</a></li> 																<li class="tpb_a"><a href="https://pcic.pcc.gov.tw/pwc-web/"class="tmd" title="前往技師與工程技術顧問公司管理資訊系統(另開頁面)" rel="noopener noreferrer"target="_blank">技師與工程技術顧問公司管理資訊系統</a></li> 																<li class="tpb_a"><a href="https://cloudbm.nlma.gov.tw/CPTL/index.jsp"class="tmd" title="前往內政部國土管理署全國建築管理資訊入口網(另開頁面)" rel="noopener noreferrer"target="_blank">內政部國土管理署全國建築管理資訊入口網</a></li> 																<li class="tpb_a"><a href="https://www.etax.nat.gov.tw"class="tmd" title="前往財政部稅務入口網(另開頁面)" rel="noopener noreferrer"target="_blank">財政部稅務入口網</a></li> 																<li class="tpb_a"><a href="https://www.energylabel.org.tw/purchasing/govgreen/list.aspx"class="tmd" title="前往經濟部能源署節能標章綠色採購網(另開頁面)" rel="noopener noreferrer"target="_blank">經濟部能源署節能標章綠色採購網</a></li> 																<li class="tpb_a"><a href="https://dpws.sfaa.gov.tw/index.jsp"class="tmd" title="前往衛生福利部社會及家庭署身心障礙服務入口網(另開頁面)" rel="noopener noreferrer"target="_blank">衛生福利部社會及家庭署身心障礙服務入口網</a></li> 																<li class="tpb_a"><a href="https://socialcom.mohw.gov.tw/swalPublic/front/taIndex"class="tmd" title="前往衛生福利部社福人員勞動申訴及溝通平臺-違規停止補助名單(另開頁面)" rel="noopener noreferrer"target="_blank">衛生福利部社福人員勞動申訴及溝通平臺-違規停止補助名單</a></li> 																<li class="tpb_a"><a href="https://cloudbm.nlma.gov.tw/bccs/login"class="tmd" title="前往內政部國土管理署工程重機械編管及運用管理系統(另開頁面)" rel="noopener noreferrer"target="_blank">內政部國土管理署工程重機械編管及運用管理系統</a></li> 																<li class="tpb_a"><a href="/pis/srml/linkGroupClient/readLinkGroupClient?id=50003006"class="tmd" title="前往經濟部(投資審議司)公告陸資資訊"rel="noopener noreferrer"target="_blank">經濟部(投資審議司)公告陸資資訊</a></li> 																<li class="tpb_a"><a href="https://www.mine.gov.tw/"class="tmd" title="前往經濟部地質調查及礦業管理中心(另開頁面)" rel="noopener noreferrer"target="_blank">經濟部地質調查及礦業管理中心</a></li> 																<li class="tpb_a"><a href="https://pcic.pcc.gov.tw/pwc-web/service/bidCae001"class="tmd" title="前往廠商承攬公共工程履歷查詢(另開頁面)" rel="noopener noreferrer"target="_blank">廠商承攬公共工程履歷查詢</a></li> 																<li class="tpb_a"><a href="https://pcic.pcc.gov.tw/pwc-web/service/bidCab001"class="tmd" title="前往公共工程人員履歷查詢(另開頁面)" rel="noopener noreferrer"target="_blank">公共工程人員履歷查詢</a></li> 																<li class="tpb_a"><a href="https://pcic.pcc.gov.tw/pwc-web/service/eng0814Query"class="tmd" title="前往技師懲戒資料(另開頁面)" rel="noopener noreferrer"target="_blank">技師懲戒資料</a></li> 																<li class="tpb_a"><a href="https://cloudbm.nlma.gov.tw/CPTL/cpt0503m.do"class="tmd" title="前往內政部國土管理署營造業評鑑結果查詢(另開頁面)" rel="noopener noreferrer"target="_blank">內政部國土管理署營造業評鑑結果查詢</a></li> 																<li class="tpb_a"><a href="/pis/srml/linkGroupClient/readLinkGroupClient?id=50003007"class="tmd" title="前往行政法人相關採購資訊"rel="noopener noreferrer"target="_blank">行政法人相關採購資訊</a></li> 																<li class="tpb_a"><a href="https://ebuying.hinet.net"class="tmd" title="前往採購網加值服務訂閱(另開頁面)" rel="noopener noreferrer"target="_blank">採購網加值服務訂閱</a></li> 																<li class="tpb_a"><a href="https://ccs.hinet.net/line_pwd_cht_big5.htm"class="tmd" title="前往更改HiNet連線密碼(另開頁面)" rel="noopener noreferrer"target="_blank">更改HiNet連線密碼</a></li> 																<li class="tpb_a"><a href="https://e-pay.hinet.net/"class="tmd" title="前往HiNet點數卡購買(另開頁面)" rel="noopener noreferrer"target="_blank">HiNet點數卡購買</a></li> 																<li class="tpb_a"><a href="https://announcement.mol.gov.tw/"class="tmd" title="前往勞動部「違反勞動法令事業單位(雇主)查詢系統」(另開頁面)" rel="noopener noreferrer"target="_blank">勞動部「違反勞動法令事業單位(雇主)查詢系統」</a></li> 													</ul> 													<!--相關連結 > 其他 > 第三階選項結束 --> 												</li> 												 							    			</ul> 							<!--第二階選項結束 --> 							 							</div> 						</div> 			    	<!--第一階選項 --> 					<li class="li_tp fix_menu"> 						<a href="#" class="bind_menu_a" data-layerid="layer7">請求協助</a> 					</li> 					<!--第一階選項結束 --> 					 					<div class="sub_menu bind_menu_a" id="layer7" data-layerid="layer7" style="display: none;"> 							<i></i> 							<div class="sub_area clearfix" style="margin-bottom:20px"> 							<!--第二階選項 --> 							 							 							    			 							    			 							    			<ul class="sub_nav list" style="margin-bottom:20px"> 							    			 												<li class="li_tp tm"><a class="tmc">熱門問答</a> 													<!--第三階選項 --> 													<ul class="sub02"> 																	<li class="tpb_a"><a href="/csci/pf/readCsciFaqStatList"class="tmd">熱門問答</a></li> 													</ul> 													<!--第三階選項結束 --> 												</li> 											 							    			</ul> 							    			 							    			 							    			<ul class="sub_nav list" style="margin-bottom:20px"> 							    			 												<li class="li_tp tm"><a class="tmc">問題檢索</a> 													<!--第三階選項 --> 													<ul class="sub02"> 																	<li class="tpb_a"><a href="/csci/pf/readCsciFaqDetlList"class="tmd">問題檢索</a></li> 													</ul> 													<!--第三階選項結束 --> 												</li> 											 							    			</ul> 							    			 							    			 							    			<ul class="sub_nav list" style="margin-bottom:20px"> 							    			 												<li class="li_tp tm"><a class="tmc">我要發問</a> 													<!--第三階選項 --> 													<ul class="sub02"> 																	<li class="tpb_a"><a href="/csci/pf/feedback"class="tmd">我要發問</a></li> 													</ul> 													<!--第三階選項結束 --> 												</li> 											 							    			</ul> 							<!--第二階選項結束 --> 							 							</div> 						</div> 			    	<!--第一階選項 --> 					<li class="li_tp fix_menu"> 						<a href="#" class="bind_menu_a" data-layerid="layer8">最新功能</a> 					</li> 					<!--第一階選項結束 --> 					 					<div class="sub_menu bind_menu_a" id="layer8" data-layerid="layer8" style="display: none;"> 							<i></i> 							<div class="sub_area clearfix" style="margin-bottom:20px"> 							<!--第二階選項 --> 							 							 							    			 							    			 							    			<ul class="sub_nav list" style="margin-bottom:20px"> 							    			 												<li class="li_tp tm"><a class="tmc">最新功能</a> 													<!--第三階選項 --> 													<ul class="sub02"> 																	<li class="tpb_a"><a href="/osm/public/newSysOn"class="tmd">最新功能</a></li> 													</ul> 													<!--第三階選項結束 --> 												</li> 											 							    			</ul> 							<!--第二階選項結束 --> 							 							</div> 						</div> 			</ul> 		</div> 	</div> </div>
		</div>
	

<!--TOPEND--> 
<!--middle-->

<div class="middle_1">
	<div class="middle_1_cen">
	
	 	<!--首頁統計表 -->
 		
 		<!--首頁統計表結束 -->
	
		<div id="content">
			


<script type="text/javascript" src='/tps/js/jquery-ui.min.js'></script>
<script type="text/javascript" src="/tps/js/jquery-3.3.1.min.js"></script>
<script type="text/javascript" src='/tps/js/commonDlg.js'></script>
<script type="text/javascript" src="/tps/js/purify.min.js"></script>




<script type="text/javascript" src="/ccs/dist/geps3-commons.bundle.js?1750922202912"></script>
<script>

if (typeof("".replaceAll)=="undefined") {
	String.prototype.replaceAll = function(s1,s2) {
		return this.replace(new RegExp(s1, "gm"), s2);
	};
}
$(document).ready(function() {
	document.cookie = 'SameSite=strict;secure;httpOnly'; // 設定cookie-SameSite參數為strict; cookie僅能由https傳送; cookie僅能透過伺服器存取
});
</script>


<div style="color: red;">
	
</div>
<a href="#C" id="AC" name="C" title="中間功能區塊" class="guide" accesskey="C">:::</a>
<div class="middle_2_info_1 mh20" id="loginBreadcrumbs">
	<a href="/pis" title="首頁">首頁</a>
	<span id="breadcrumb"></span>
</div>
<div class="middle_1_cen_big" style="width: 100%; float: left; margin-top: 30px;">











<script src="/tps/js/tab.js"></script> 
<!-- Load jQuery, SimpleModal and Basic JS files -->
<script type="text/javascript" src="/tps/js/DateUtils.js"></script>
<!-- Start javaScript、jQuery  -->
<!-- WTO條約 js -->
<script type="text/javascript" src="/tps/js/treaty.js"></script>
<script type="text/javascript" src="/tps/js/hardword_function.js"></script>
<script type="text/javascript" src="/tps/js/showInstallerWindow.js"></script>
<!-- 新版押標金 -->
<script src="/qdcs/js/F/eqm/ebb.js"></script>
<link rel="stylesheet" href="/qdcs/js/F/eqm/ebb.css">

<style>
  .search_print{
      float: left;
  }
  @-moz-document url-prefix() { /* Firefox 限定*/
	  .button-row {
		  display: flex; /* 啟用 Flexbox 排版模式 */
		  flex-direction: row; /* 水平排列子元素（按鈕） */
		  gap: 0.5px; /* 每個按鈕之間保留 0.5px 空隙 */
		  flex-wrap: wrap; /* 如果太寬，按鈕會自動換行，不會爆版 */
	  }
  }
</style>










<script>

	// 加入我的招標公告樣版
	function createTemplate(){
		$.blockUI({
	        message: $('#saveQuestion'),
	        css: {width: '400px',top: '30%',left: '35%',padding: '15px'}
	    });
	}

	// 樣版名稱是否相同
	function isTemplateName(){
		var newTemplateName = $("#newTemplateName").val();
		var newTemplateType = $("[name=newTemplateType]:checked").val();
		var pmsMainTmp = "";
		var pmsMain = "70933504"; 
		var primaryKey = pmsMainTmp != "" ? pmsMainTmp : pmsMain;
		var isTemp =  pmsMain != "" ? false : true;
		if(newTemplateName == "" || newTemplateName == null){
			$("#qusetion_errorMsg").html("請輸入樣版名稱");
		}else if(newTemplateType == "" || newTemplateType == null){
			$("#qusetion_errorMsg").html("請輸入樣版屬性");
		}else{
			$.ajax({
				type: "POST",
				url: "/tps/TenderManagement/new/tpamIsTemplateName",
				dataType: "text",
				data: {
					newTemplateName: newTemplateName,
					newTemplateType: newTemplateType,
					pmsMain: primaryKey,
					isTemp: isTemp
				},
				success: function(data){
					if(data != null){
						$("#qusetion_errorMsg").text(data);
// 						$("#cancel").hide();
					}
				},
				beforeSend: function(data){
					$("#question_confirm1").show();
					$("#question_confirm2").hide();
				},
				complete: function(data){
					if(data.responseText == ""){
						$("#question_confirm1").hide();
						$("#question_confirm2").show();
						$("#close").show();
						$("#cancel").hide();
						$("#newSave").hide();
					}else{
						$("#question_confirm1").hide();
// 						$("#cancel").hide();
					}
				},
				error: function(jqXHR) {
					alert("發生錯誤: " + jqXHR.status);    
				}
			});
		}
	}

	// 取消 or 關閉加入我的招標公告樣版
	function templateClose(){
		$("#question_confirm1").hide();
		$("#question_confirm2").hide();
		$("#newTemplateName").val("");
		$("#qusetion_errorMsg").text("");
// 		$("input[name=newTemplateType]").val([""]);
		$("#cancel").show();
		$("#newSave").show();
		$("#close").hide();
		$.unblockUI();
	}
				
	
</script>
 
 	
 	
 	
 	
 



<link rel="stylesheet" type="text/css" href="/tps/css/custom-loader.css"/>


<script>
	var $ = jQuery.noConflict();
	var activateApplicationBtn = 'uploadNodeJs';
	var applicationInstallerBtn = 'installerBtn';
	var isNeedRunInitialCheck = 'N';
	var operation = 'TPAM';
	var version = "";
	function buildHintBlockUIContent(type, id, textId, indicatorId, confirmBtnDivId, nodeJsConfirmBtnId) {
		/*
			<div id="hint" style="display: none; text-align: center;">
				<div id="hintText">Please download and install Document transfer program</div>
				<div id="indicator" class="loader_custom loader-default_custom"></div>
				<div id="confirmBtnDiv" class="bt_cen1" style="margin-top: 25px; margin-bottom: 10px;">
					<a id="nodeJsConfirmBtn" href="javascript:void(0)" onclick="$.unblockUI({ fadeOut: 0 });">Confirm</a>
				</div>
			</div>
			
			<div id="initialHint" style="display: none; text-align: center;">
			<div id="initialHintText">Please download and install Document transfer program</div>
			<div id="loadingIndicator" class="loader_custom loader-default_custom"></div>
		<!-- 	<div id="initialConfirmBtnDiv" class="bt_cen1" style="margin-top: 25px; margin-bottom: 10px;"> -->
		<!-- 		<a id="initialNodeJsConfirmBtn" href="javascript:void(0)" onclick="$.unblockUI({ fadeOut: 0 });">Confirm</a> -->
		<!-- 	</div> -->
		</div>
		*/
		var hint = document.createElement('div');
		hint.id = id;
		hint.style.display = 'none';
		hint.style.textAlign = 'center';
		var hintText = document.createElement('div');
		hintText.id = textId;
		var indicator = document.createElement('div');
		indicator.id = indicatorId;
		indicator.classList.add('loader_custom');
		indicator.classList.add('loader-default_custom');
		
		var confirmBtn = null;
		var nodeJsConfirmBtn = null;
		if(type !== 'simple') {
			confirmBtn = document.createElement('div');
			confirmBtn.id = confirmBtnDivId
			confirmBtn.classList.add('bt_cen1');
			
			nodeJsConfirmBtn = document.createElement('a');
			nodeJsConfirmBtn.id = nodeJsConfirmBtnId
			nodeJsConfirmBtn.href = 'javascript:void(0)';
			nodeJsConfirmBtn.onclick = function() { $.unblockUI({ fadeOut: 0 }); };
			nodeJsConfirmBtn.innerText = 'Confirm';
			confirmBtn.appendChild(nodeJsConfirmBtn);
		}
		
		hint.appendChild(hintText);
		hint.appendChild(indicator);
		if(type !== 'simple') {
			hint.appendChild(confirmBtn);			
		}
		return hint;
	}
	
	function getBlockUICssStyleObject(isEng, isNeedCheckInfo) {
		if(isEng !== null && isEng !== undefined) {
			if(isEng) {
				// 英文版style
				return { width: '400px',height: '250px' ,top: '45%',left: '40%' };
			} else {
				// 中文版style
				return { width: '600px',height: '160px' ,top: '45%',left: '40%' };
			}
		}
		
		if(isNeedCheckInfo !== null && isNeedCheckInfo !== undefined) {
			if(isNeedCheckInfo) {
				// 中文版style
				return { width: '600px',height: '160px' ,top: '45%',left: '40%' };
			}
		}
	}
	
	function getALinkDisabledCssObject() {
		return {
			color: 'rgb(84, 84, 84)',
			cursor: 'default',
			backgroundColor: 'rgb(235, 235, 228)',
			border: '0.5px solid #000'
		}
	}
	
	function getHintText(isEng, isNeedCheckInfo) {
		if(isEng !== null && isEng !== undefined) {
			if(isEng) {
				// 英文版訊息
				return 'Checking for Document transfer program availability...';
			} else {
				// 中文版訊息
				return '檢查文件傳輸程式...';
			}
		}
		
		if(isNeedCheckInfo !== null && isNeedCheckInfo !== undefined) {
			if(isNeedCheckInfo) {
				// 中文版訊息
				return '檢查用戶端文件傳輸程式是否啟動...';
			}
		}
	}
	
	function getRemainderText(isEng, isNeedCheckInfo, checkVersion) {
		if (checkVersion) {
			if(isEng !== null && isEng !== undefined) {
				if(isEng) {
					// 英文版訊息
					return 'Please download and install Latest Version '+version+' Document transfer program';
				} else {
					// 中文版訊息
					return '尚未安裝最新版文件傳輸程式，建議先安裝最新版'+version+'。(更新方式:桌面右下角「招標文件傳輸程式」圖示>點選滑鼠右鍵>檢查更新)';
				}
			}
		} else {
			if(isEng !== null && isEng !== undefined) {
				if(isEng) {
					// 英文版訊息
					return 'Please download and install Document transfer program';
				} else {
					// 中文版訊息
					return '文件傳輸程式尚未安裝或未啟動，請先安裝並執行文件傳輸程式。若服務已啟動，請重新載入此頁面';
				}
			}
		}
		
		if(isNeedCheckInfo !== null && isNeedCheckInfo !== undefined) {
			if(isNeedCheckInfo) {
				// 中文版訊息
				return '執行環境檢測結果\n您的電腦尚未安裝或未啟動文件傳輸程式。\n若尚未安裝，請至下列網址下載並安裝；若未啟動，請點選個人電腦桌面或功能選單上之文件傳輸圖示，啟動文件傳輸程式之執行';
			}
		}
	}
	
	// 0 => formSelector
	// 1 => isEng
	// 2 => isNeedCheckInfo
	// 3 => custom callback function
	function checkNodeJs() {
		// provide arguments for formSelector
		//console.log("checkNodeJs");
		var formSelector = null;
		var isEng = null;
		var isNeedCheckInfo = null;
		var callback = null;
		if(arguments.length > 3) callback = arguments[3];
		if(arguments.length > 2) isNeedCheckInfo = arguments[2];
		if(arguments.length > 1) isEng = arguments[1];
		formSelector = arguments[0];
		
		// $('#hint')[0];
		var hintBlockDiv = buildHintBlockUIContent('subtle', 'hint', 'hintText', 'indicator', 'confirmBtnDiv', 'nodeJsConfirmBtn');
		var blockUICssObject = getBlockUICssStyleObject(isEng, isNeedCheckInfo);
		var infoText = getHintText(isEng, isNeedCheckInfo);
		$(hintBlockDiv).find('#hintText').text(infoText);
		$(hintBlockDiv).find('#nodeJsConfirmBtn').text(isEng !== null && isEng ? 'Confirm' : '確認');
		$(hintBlockDiv).find('#confirmBtnDiv').hide();
		$(hintBlockDiv).find('#indicator').addClass('is-active');
		$.blockUI({
			message: hintBlockDiv,
			css: blockUICssObject,
	        onOverlayClick: function() { $.unblockUI({ fadeOut: 0 }) }
		});
		
		$.ajax({
			url: "https://"+window.location.host+"/electron/tpsCheckVersion.html",
			type: 'GET',
			success: function(res) {
				// 檢測成功, 打開btn
				console.log(res);
                res = res.replaceAll("\'", "\"")
				var result = JSON.parse(res);
				version = result.windows;
			},
			error: function(jqXHR) {
				console.error(jqXHR);
			}
		});

		$.ajax({
			url: 'http://localhost:3000/getVersion',
			type: 'GET',
			success: function(res) {
				$.unblockUI({ fadeOut: 0 });
				console.log(res);
                $.ajax({
                    url: "/tps/tpam/tenderUpload/checkNodeJsVersion",
                    type: 'POST',
                    data: {
                        version: res
                    },
                    success: function(response) {
                        let isVersionOk = response.isVersionOk;
                        let codeValue = response.codeValue !== null ? response.codeValue : version;
                        if(res == '0.0.0' || res < version) {
                            $.unblockUI({ fadeOut: 0 });
                            infoText = getRemainderText(isEng, false, true);
                            let warningText = isEng ?
                                'Starting from January 4, 2025, due to security upgrades, only version ' + codeValue + ' of the Document Transfer Program will be supported. Please update and test the program in advance to avoid any disruption to procurement operations.' :
                                '招標文件傳輸程式因安全性升級，114年1月4日起限' + codeValue + '版本方能使用，請您預先更新及測試，避免影響採購作業。';
                            $(hintBlockDiv).find('#hintText').text(infoText).append('<br><span style="color: red;">' + warningText + '</span>');
                            $(hintBlockDiv).find('#nodeJsConfirmBtn').text(isEng !== null && isEng ? 'Confirm' : '確認');
                            $(hintBlockDiv).find('#nodeJsConfirmBtn').click(function() {
                                $.unblockUI({ fadeOut: 0 });
                                if (!isVersionOk) {
                                    // 若版本檢查不通過，關閉 BlockUI
                                    return;
                                }
                                if(callback !== null) {
                                    callback();
                                } else {
                                    $(formSelector).submit();
                                }
                            });
                            $(hintBlockDiv).find('#confirmBtnDiv').show();
                            $(hintBlockDiv).find('#indicator').removeClass('is-active');
                            $.blockUI({
                                message: hintBlockDiv,
                                css: blockUICssObject,
                                onOverlayClick: function() { $.unblockUI({ fadeOut: 0 });
                                    if(callback !== null) {
                                        callback();
                                    } else {
                                        $(formSelector).submit();
                                    }}
                            });

                            if($(document.body).css('overflowY') !== 'auto') {
                                $(document.body).css('overflowY', 'auto');
                            }
                        } else {
                            $.unblockUI({ fadeOut: 0 });
                            if(callback !== null) {
                                callback();
                            } else {
                                $(formSelector).submit();
                            }
                        }
                        $('#indicator').removeClass('is-active');
                    },
                    error: function(jqXHR) {
                        console.error(jqXHR);
                    }
                });
			},
			error: function(jqXHR) {
				if(jqXHR.status === 200) {
					if(callback !== null) {
						callback();
					} else {
						$(formSelector).submit();
					}
				} else {
					$.unblockUI({ fadeOut: 0 });
					infoText = infoText = getRemainderText(isEng, false, true);
					$(hintBlockDiv).find('#hintText').text(infoText);
					$(hintBlockDiv).find('#nodeJsConfirmBtn').text(isEng !== null && isEng ? 'Confirm' : '確認');
					$(hintBlockDiv).find('#nodeJsConfirmBtn').click(function() {
						$.unblockUI({ fadeOut: 0 });
	    				if(callback !== null) {
	    					callback();
	    				} else {
	    					$(formSelector).submit();
	    				}
					});
					$(hintBlockDiv).find('#confirmBtnDiv').show();
					$(hintBlockDiv).find('#indicator').removeClass('is-active');
					$.blockUI({
	                    message: hintBlockDiv,
	                    css: blockUICssObject,
	                    onOverlayClick: function() { $.unblockUI({ fadeOut: 0 }) }
	                });
					
					if($(document.body).css('overflowY') !== 'auto') {
						$(document.body).css('overflowY', 'auto');						
					}
				}
			}
		});
	}
	
	// 0 => activateApplicationBtnId
	// 1 => applicationInstallerBtnId
	// 2 => isEng
	function checkForNodeJsAvailability() {
		var isNeedPopup = false;
		var activateBtn = null;
		var installerBtn = null;
		var isEng = false;
		if(arguments.length > 0) {
			isNeedPopup = arguments[0];
			activateBtn = arguments[1];
			installerBtn = arguments[2];
		}
		if(arguments.length > 3) {
			isEng = arguments[3];
		}
		
		var hintBlockDiv = buildHintBlockUIContent('simple', 'initialHint', 'initialHintText', 'loadingIndicator', '', ''); // initialHintDiv;
		var blockUICssObject = getBlockUICssStyleObject(isEng, null);
		$(hintBlockDiv).find('#initialHintText').text(getHintText(isEng, false));
		$(hintBlockDiv).find('#loadingIndicator').addClass('is-active');
		if(isNeedPopup) {
			$.blockUI({
				message: hintBlockDiv,
				css: blockUICssObject,
				onOverlayClick: function() { $.unblockUI({ fadeOut: 0 }) }
			});			
		}
		
		var hintElementDiv = document.createElement('div');
		var hintElement = document.createElement('span');
		var styleObj = {
				display: 'block',
		        color: 'red',
				fontWeight: 'bold',
				width: '100%'
		};
		Object.assign(hintElementDiv.style, styleObj);
		var innerSpanStyle = {
				display: 'block',
				width: '35rem'
		};
		Object.assign(hintElement.style, innerSpanStyle);
		hintElement.id = 'initialCheckHint';
		hintElement.innerText = getRemainderText(isEng, false, false); // '文件傳輸程式尚未安裝或未啟動，請先安裝並執行文件傳輸程式';
		hintElementDiv.appendChild(hintElement);
		$.ajax({
			url: 'http://localhost:3000/',
			type: 'GET',
			dataType: 'text',
			success: function() {
				// 檢測成功, 打開btn
				if(isNeedPopup) {
					$.unblockUI({ fadeOut: 0 });
					$(hintBlockDiv).find('#loadingIndicator').removeClass('is-active');					
				}
				$('#' + activateBtn).prop('disabled', false);
				Object.assign($("#" + installerBtn + " button").eq(0).attr("style"), getALinkDisabledCssObject());
// 				$('#' + installerBtn + ' a').attr('href', 'javascript:void(0)');
// 				$('#' + installerBtn + ' a').prop('disabled', true);
				if($('#initialCheckHint').length > 0) {
					$('#initialCheckHint').remove();
				}
				if($(document.body).css('overflowY') !== 'auto') {
					$(document.body).css('overflowY', 'auto');						
				}
			},
			error: function(jqXHR) {
				if(isNeedPopup) {
					$.unblockUI({ fadeOut: 0 });
					$(hintBlockDiv).find('#loadingIndicator').removeClass('is-active');					
				}
				if(jqXHR.status === 200) {
					// 檢測成功, 打開btn
					$('#' + activateBtn).prop('disabled', false);
					$('#' + installerBtn).prop('disabled', true);
					if($('#initialCheckHint').length > 0) {
						$('#initialCheckHint').remove();
					}
				} else {
					// 失敗, 關閉btn, 顯示紅字提示訊息
					$('#' + activateBtn).prop('disabled', true);
// 					$('#' + installerBtn).prop('disabled', false);
					$('#' + installerBtn).append(hintElementDiv);
				}
				if($(document.body).css('overflowY') !== 'auto') {
					$(document.body).css('overflowY', 'auto');						
				}
			}
		});
	}

	function nodejsShowPromptMessage(){
		$("#uploadMsg").attr("onclick", "checkNodeJs('#uploadForm', false)");
		$.blockUI({
             message: $("#showUploadMsg"),
             css: {width: '700px',height: '275px' ,top: '35%',left: '26%'},
             onOverlayClick: $.unblockUI
        });
	}
</script>

<script>
	// page load check for node.js doc-transfer availability
	$(function() {
		if(isNeedRunInitialCheck !== null && isNeedRunInitialCheck !== undefined && isNeedRunInitialCheck === 'Y') {
			checkForNodeJsAvailability(true, activateApplicationBtn, applicationInstallerBtn);
		}
	});
</script>
<script type="text/javascript" src="/tps/js/jquery.blockUI.js"></script>
<script>
	var YaN = {
		"Y" : "是",
		"N" : "否"
	};
	var isCommon = "true";
	var isStructureSafe = "true";
	/**
	 *創建數字轉換金額格式的公共元件
	 *@param id          //html document 元素
	 *@param messageid   //顯示中文格式 document 元素
	 *@param defval      //預設值
	 **/
	 function currency(id, messageid, defval) {
		var currencyConfig = {
			defaultValue : defval,
			callback : function(currencyData) {
				console.log(currencyData);
				$(id).text(currencyData.formateValue + "元");
				$(messageid).html(currencyData.chineseValue);
			}
		}
		var component = new Geps3.Currency(currencyConfig);
	}
	
	//本採購案是否屬於建築工程  是，本案水管、電氣與建築工程合併招標
	function setBudgetBuild2(value){
		$("#span_isPipesConstructionJoint").hide();
		$("#span_isPipesConstruction2").hide();
		$("#span_isPipesConstruction3").hide();
		$("#span_isPipesConstruction4").hide();		
		$("#budget2_span").hide();
		if(value != ""){
			$("#budget2_span").show();
			if(value >= 5000000 && value < 50000000){
				$("#span_isPipesConstruction2").show();
				if (YaN[""] != undefined){
						$("#div_isPipesConstruction2").html("<b>"+YaN[""]+"</b>");
					}				
				if("" == "Y"){
					$("#span_isPipesConstructionJoint").show();
					if (YaN[""]!= undefined){
						$("#div_isPipesConstructionJoint").html("<b>"+YaN[""]+"</b>");
					}
					if("" == "N"){
						if(isCommon == "false"){
							$("#span_isPipesConstruction3").show();
							$("#span_isPipesConstruction4").show();
						}
						if (YaN[""]!= undefined){
							$("#div_isPipesConstruction3").html("<b>"+YaN[""]+"</b>");
						}
						if (YaN[""]!= undefined){
							$("#div_isPipesConstruction4").html("<b>"+YaN[""]+"</b>");
						}
					}else{
						$("#span_isPipesConstruction3").hide();
						$("#span_isPipesConstruction4").hide();
					}
				}else{
					$("#span_isPipesConstructionJoint").hide();
				}
			}else if(value >= 50000000){				
				$("#span_isPipesConstruction2").hide();
				$("#span_isPipesConstructionJoint").hide();
				if(isCommon == "false"){
					$("#span_isPipesConstruction3").show();
					$("#span_isPipesConstruction4").show();
				}
				if (YaN[""]!= undefined){
					$("#div_isPipesConstruction3").html("<b>"+YaN[""]+"</b>");
				}
				if (YaN[""]!= undefined){
					$("#div_isPipesConstruction4").html("<b>"+YaN[""]+"</b>");
				}
			}
		}
	}

	function downloadTenderDocuments(pkPmsMain){
		getNodeJSFormDataWithCallback(pkPmsMain, function(data) {
			$("#tenderInfo").attr("value", data);
			checkNodeJs('#nodeJSForm', false, false);
		});
	}

	// 取得NodeJS Form 資料
	function getNodeJSFormDataWithCallback(pkPmsMain, fn) {
		$.ajax({
			type : "post", // post/get
			url : "/tps/QueryTenderNotice/query/getNodeJSFormData",
			data : {
				pkPmsMain : pkPmsMain
			},
			dataType : "text",
			success : function(data) {
				if (data.indexOf("structureVerifyFail") > 0) {
					alert("檢驗檔案結構有問題，請洽客服中心！")
				} else {
					fn(data);
				}
			},
			error : function(e) {
				alert(e.responseText);
			}
		});
	}

	/*********************************************************************************************
	 **                                    畫面Loading begin                                                                                           
	 *********************************************************************************************/
	
	 $(document).ready(function() {
			Geps3.Tooltip.exec(document.getElementsByClassName('qq'));
							
			//標的分類
			var PmsProctrgCate = "2";
			var DmsProctrgCate = "381";
			var fkPmsTenderWay = "1";

			if(PmsProctrgCate == "3") {
				$("#tr_isRecruting101").show();
			} else{
				$("#tr_isRecruting101").hide();
			}
			
			//當採購性質為「工程類(1)」及「財務類(2)」時，隱藏「是否屬推動募兵制暫行條例第10條第1項」欄位
			var GPA = "" != "" ? "" : "N";
			var oid = "";
			if (PmsProctrgCate == "3" && oid.startsWith('2.16.886.101.20003.20003')) {
				if ((fkPmsTenderWay != "2" && fkPmsTenderWay != "12" && GPA == "N") || (fkPmsTenderWay == "2") || (fkPmsTenderWay == "12")) {
					$("#tr_isRecruting101").show();
					var isRecruting101 = "";
					$("#isRecruting101").text(YaN[isRecruting101]);
				}else{
					$("#tr_isRecruting101").hide();
				}
			} else {
				$("#tr_isRecruting101").hide();
			}					

			var pccSampleVersion = "財物類財物採購契約範本最新版之時間為「113.12.26」&lt;br&gt;財物類採購契約範本附記條款特別聲明最新版之時間為「114.05.20」";
			var splitStr = "";
			if(pccSampleVersion.indexOf("&amp;lt;br&amp;gt;") != -1 ){
				splitStr = "&amp;lt;br&amp;gt;";
			}else if(pccSampleVersion.indexOf("&lt;br&gt;") != -1 ){
				splitStr = "&lt;br&gt;";
			}
			if(splitStr != ""){
				var version = pccSampleVersion.split(splitStr);
				var pccSampleVersionStr = "";
				for(var i = 0 ; i < version.length ; i++){
					pccSampleVersionStr = pccSampleVersionStr + version[i] + "<br>";
				}
			}else{
				pccSampleVersionStr = pccSampleVersion;
			}
			console.log(pccSampleVersionStr);
			$("#NewSampleNotice").html(pccSampleVersionStr);
			
			//當「是否適用WTO政府採購協定」選取 是  時 ，顯示「是否採用電子競價」的欄位 和「是否為商業財物或服務」的欄位
			var GPA = "N";
			var otherA = 'N';
			
				isApplied = 'N';
				agreementType = 'GPA2';
				if((agreementType == "GPA" || agreementType == "GPA2") && isApplied === 'Y') {
					GPA = 'Y';
				}else if((agreementType == "ANZTEC" || agreementType == "ASTEP") && isApplied == "Y"){
					otherA = "Y";
				}
			
				isApplied = 'N';
				agreementType = 'ANZTEC';
				if((agreementType == "GPA" || agreementType == "GPA2") && isApplied === 'Y') {
					GPA = 'Y';
				}else if((agreementType == "ANZTEC" || agreementType == "ASTEP") && isApplied == "Y"){
					otherA = "Y";
				}
			
				isApplied = 'N';
				agreementType = 'ASTEP';
				if((agreementType == "GPA" || agreementType == "GPA2") && isApplied === 'Y') {
					GPA = 'Y';
				}else if((agreementType == "ANZTEC" || agreementType == "ASTEP") && isApplied == "Y"){
					otherA = "Y";
				}
			
			if (GPA == "Y" || otherA == "Y") {
				$("#isAppliedShow").show();
			} else {
				$("#isAppliedShow").hide();
			}

			//『預算金額』金額格式轉換
			
				isVal = "3002120";
				if (isVal.replace(/(^s*)|(s*$)/g, "").length != 0) {
					currency("", "#BmessageArea", isVal);
				}
			
			
			//招標方式
			var PTenderWay = "1";

			//機關自定公告日
			var targetDate = dateTransMinGo("2025-06-26");
			$("#targetDate_1").text(targetDate);

				//是否屬統包
				var isPackage = "N";
				$("#isPackage").text(YaN[isPackage]);
				var isLowestLaw = "";
				var pmsPCValue = "2";
				var fkPmsAwardWay="3";
				var budget = $("#budget").val();
				if("Y"==isPackage){								
					$("#div_isPackage").show();
					$("#tr_expectBenefit").show();
					//1.是否將工程或財物採購中之設計與施工、供應、安裝或一定期間之維修等併於同一採購契約辦理招標
					var isPackageInContract = "";
					$("#isPackageInContract").text(YaN[isPackageInContract]);
					//2.招標文件是否已依「統包實施辦法」規定辦理
					var isPackageLaw = "";
					$("#isPackageLaw").text(YaN[isPackageLaw]);
					if(isLowestLaw == "Y"){
						$("#isPackageNotice").hide();
					}else{
						if((fkPmsAwardWay=="1"||fkPmsAwardWay=="2") && isLowestLaw == "N"){
							$("#isPackageNotice").show();
						}
					}
				}else{
					// 顯示 是否已依照「重大公共工程開工要件注意事項」辦理
					
					if (pmsPCValue == "1" && budget >= 1500000 && isPackage == "N") {
			        	$("#tr_isMajorPcBwNotes").show(); 
		           	}else{
			        	$("#tr_isMajorPcBwNotes").hide(); 
			        }
					$("#div_isPackage").hide();
					$("#tr_expectBenefit").hide();					
				}

				// 是否已依照「重大公共工程開工要件注意事項」辦理
				var isMajorPcBwNotes = "";
// 				$("#isMajorPcBwNotes").text(YaN[isMajorPcBwNotes]);
				if (isMajorPcBwNotes.replace(/(^s*)|(s*$)/g, "").length !=0 && isMajorPcBwNotes != null) {
					if(isMajorPcBwNotes == "Y"){
						$("#tr_bwCheckListResult").show();
						$("#tr_isDocHasBidTodos").show();
			        }else if(isMajorPcBwNotes == "N"){
			        	$("#tr_bwCheckListResult").hide();
			        	$("#tr_isDocHasBidTodos").hide();
			        }
				}

				//是否提供電子領標
				var isEobtain = "Y";
				
				var checkSpdt = "2025-07-14 17:00:00";
				var checkTargetDate = "2025-06-26";
				var checkSysSpdt = "2025-07-14 17:00:00.0";	// 截止投標日，會依颱風天順延
				var dateNow = new Date();	//現在時間
				var nowValueOf = Date.parse(dateNow).valueOf();
				var targetDateValueOf = Date.parse(checkTargetDate).valueOf();
				var spdtValueOf = Date.parse(checkSpdt).valueOf();
				var sysSpdtValueOf = Date.parse(checkSysSpdt).valueOf();

				// 為機關端，是否提供電子領標為是，且已過截止投標時間，才可下載歷史招標文件。如果還沒過截止投標時間，則顯示檢驗上傳標案
				var isGov = "";
				if (("" == "********") && isEobtain == "Y"){
					if (nowValueOf > spdtValueOf) {
						$("#tenderFileDownload").show();
						$("#validateTenderFileDownload").show();
					} else {
						$("#tenderFileDownload").hide();
						$("#validateTenderFileDownload").show();
					}
				}else{
					$("#tenderFileDownload").hide();
				}
				
				//新版押標金 
				if(false && nowValueOf < sysSpdtValueOf && isGov!== 'Y') {
	 				getEbbInputForm("ebbInputDiv", 70933504, "");
				}
				
				 /***
				  * 2022-12-09
				  * 原：採購性質「財物類(2)」或「勞務類(3)」且採購金額未達公告金額者，顯示「是否於招標文件載明優先決標予身心障礙福利機構團體或庇護工場」欄位
				  * 新：採購性質「財物類(2)」或「勞務類(3)」且採購金額小於1百萬者，顯示「是否於招標文件載明優先決標予身心障礙福利機構團體或庇護工場」欄位
				  * 2022/12/22 周會會議改為採購金額小 等 於1百萬者
				  	2024/08/29 採購金額小 等 於1百50萬者,2024/10/22  改為採購金額小於150萬者
				  ***/
				var procurementAmount = "3002120";
				if ((PmsProctrgCate == "2" || PmsProctrgCate == "3") && parseInt(procurementAmount) < 1500000) {
					$("#tr_isAwardDisability").show();
					var isAwardDisability = "";					
					$("#isAwardDisability").text(YaN[isAwardDisability]);
					if (isAwardDisability == "Y") {
						$("#tr_priorityCate").show();
						var fkPmsPriorityCate1 = "";						
						$("#fkPmsPriorityCate1").text("");
						var fkPmsPriorityCate2 = "";
						$("#fkPmsPriorityCate2").text("");	
					} else {
						$("#tr_priorityCate").hide();
					}
				}

				// 廠商資格摘要
				var vendorDescs = $("#vendorDescInput").val();
				if(!("1" == "12") && vendorDescs.indexOf("廠商登記或設立之證明") < 0 && vendorDescs.indexOf("廠商納稅之證明。如營業稅或所得稅") < 0 && vendorDescs.indexOf("廠商依工業團體法或商業團體法加入工業或商業團體之證明") < 0){
					$("#vendorDescsTitle").hide();
					$("#vendorDescsTitleText").hide();
				}

				// 聯絡電話(英)(區碼)
				var engTelArea = parseInt("");
				$("#engTelAreaText").text(engTelArea.toString());
				$("#engTelArea").text(engTelArea.toString());

				// 傳真電話(英)(區碼)
				var engFaxArea = parseInt("");
				$("#engFaxAreaText").text(engFaxArea.toString());
				$("#engFaxArea").text(engFaxArea.toString());

				if(PTenderWay != "2" && PTenderWay != "12"){
					// WTO
					readyTreatyCheck();
				}

				var bid_free_date = checkEbbTrail();
				
				//最有利標流程(1/4)--參照TpamUtil，在controller的full資料驗證下判斷儲存值 
				var isMostFlag ="false";
				if (isMostFlag == "true") {
					mat_tender_confirm_ready();
				}

				//是否受機關補助 分隔線去除最後一條
				var horLineNum = $(".horLine").length;
				$(".horLine:last").attr("style", "display:none");

				var plusImage = $("#plusImage").val();
				var minusImage = $("#minusImage").val();
				$(".expand").click(function(){
					if ($(this).children("img").attr("src") == plusImage) {
						$(this).children("img").attr("src", minusImage);
						$(this).children("img").attr("alt", "收合清單");
						var type = $(this).attr('id');
						if (type == "tender"){
							$("#rowTpamHistory").show();
						}
						else if (type == "award"){
							$("#rowAtmHistory").show();
						}
						else if (type == "nonAward"){
							$("#rowAtmNonHistory").show();
						}
					}
					else if ($(this).children("img").attr("src") == minusImage) {
						$(this).children("img").attr("src", plusImage);
						$(this).children("img").attr("alt", "展開清單");
						var type = $(this).attr('id');
						if (type == "tender"){
							$("#rowTpamHistory").hide();
						}
						else if (type == "award"){
							$("#rowAtmHistory").hide();
						}
						else if (type == "nonAward"){
							$("#rowAtmNonHistory").hide();
						}
					}
				});

				
					
				// 難字替換
				$('table[summary="機關資料"], table[summary="採購資料"]').find('tbody tr').each(function() {
					var rowCells = $(this).find('td');
					for(var i = 0; i < rowCells.length; i++) {
						replacePageCodeWithHardWordImg(rowCells[i]);
					}
				});

				

				if("true" == "true"){

					$("#loginBreadcrumbs").find("a").eq(0).addClass("t-link");

					var urlOne = "<a href='/prkms/tender/common/basic/indexTenderBasic' title='標案查詢' class='t-link'>標案查詢</a>";
					var urlTwo = "<a href='/prkms/tender/common/basic/indexTenderBasic' title='招標查詢' class='t-link'>招標查詢</a>";

			 		$("#breadcrumb").html(" > 查詢服務 > 標案相關 > " + urlOne + " > " + urlTwo);
				}
				
				// 當「是否提供電子投標」的勾選「是否全部文件電子投標」值有更改時(新增不算)，加入css樣式
				// 當前值
				let currentIsEsubmitAll = '';
				// 前次值
				let priorIsEsubmitAll = '';
				if(priorIsEsubmitAll.length > 0
						&& currentIsEsubmitAll.length > 0
						&& currentIsEsubmitAll !== priorIsEsubmitAll){
					$('#isEsubmitAllBox').css({
						"color": "red",
				    	"font-weight": "bold",
					});
				}
	});
	/*********************************************************************************************
	 **	  								       畫面Loading end 
	 *********************************************************************************************/
	 function showTpamHistory(pkPmsMainHist){
		var url = "";
		if("" == "Y"){ // 機關端
			url = "/tps/QueryTenderNotice/query/historyTenderDetail";
		}else if("true" == "true"){ // 民眾端
			url = "/tps/QueryTender/query/historyTenderDetail";
		}else{ // 廠商端
			url = "/tps/QueryTenderNotice/query/supplier/historyTenderDetail";
		}
		$("#fkPmsMainHistTender").prop("value", pkPmsMainHist);
		$("#historyTenderDetail").attr("action", url);
		$("#historyTenderDetail").submit();
	}

	function showAwardHistory(pkPmsMainHist){
// 		var url = "" == "Y" ? "/tps/atm/AtmAwardQueryController/query/atmAwardDetailHist" : "/tps/atm/AtmAwardWithoutSso/QueryAtmAwardDetailHist";
		var url = "";
		if("" == "Y"){ // 機關端
			url = "/tps/atm/AtmAwardQueryController/query/atmAwardDetailHist";
		}else if("true" == "true"){ // 民眾端
			url = "/tps/atm/AtmAwardWithoutSso/QueryAtmAwardDetailHist";
		}else{ // 廠商端
			url = "/tps/atm/AtmAwardQueryController/query/supplier/atmAwardDetailHist";
		}
		$("#fkPmsMainHistAtm").prop("value", pkPmsMainHist);
		$("#historyAtmDetail").attr("action", url);
		$("#historyAtmDetail").submit();
	}

	function showNonAwardHistory(pkPmsMainHist){
		var url = "" == "Y" ? "/tps/QueryTenderNotice/query/historyNonAtmDetail" : "/tps/QueryTenderNotice/query/supplier/historyNonAtmDetail";
		$("#fkPmsMainHistNonAtm").prop("value", pkPmsMainHist);
		$("#historyNonAtmDetail").attr("action", url);
		$("#historyNonAtmDetail").submit();
	}

	// 電子領標
	function enterToTom() {
		var urlType = "";
		if("" == "true"){
			if("" == "true"){
				urlType = "obtain";
			}else{
				urlType = "official";
			}
		}else{
			urlType = "common";
		}
		$("#paymentDownloadModel").attr('action', "/tps/tom/obtainment/" + urlType + "/tomEntrance");
		$("#paymentDownloadModel").submit();
	}

	// 電子投標 - 已登入
	function eSubmitToTom(){
		$("#signInModel").attr("action", "/qdcs/tbm/TbmForwardTenders/70933504");
		$("#signInModel").submit();
	}

	// 電子投標 - 未登入
	function eSubmitToSignIn(){
		var ipStr = "/qdcs/tbm/TbmForwardTenders/70933504";
		var protocol = location.protocol;
		var hostname = location.hostname
		const encodedStr = btoa(protocol + "//" + hostname + ipStr);
		var fullIp = "/qdcs/sso/init/supp/" + encodedStr;
		console.log(fullIp);
		$("#signInModel").attr("action", fullIp);
		$("#signInModel").submit();
	}

	// 公開取得電子報價單（電子報價） - 未登入
	function enterToSignIn() {
		var ipStr = "/qdcs/eqm/equotation/70933504";
		var protocol = location.protocol;
		var hostname = location.hostname
		const encodedStr = btoa(protocol + "//" + hostname + ipStr);
		var fullIp = "/qdcs/sso/init/supp/" + encodedStr;
		console.log(fullIp);
		$("#signInModel").attr("action", fullIp);
		$("#signInModel").submit();
	}

	// 公開取得電子報價單（電子報價） - 已登入
	function enterToEquotation() {
		$("#signInModel").attr("action", "/qdcs/eqm/equotation/70933504");
		$("#signInModel").submit();
	}

	// 招標文件會員線上瀏覽 - 登入 and 前往線上繳納押標金 - 未登入
	function orderProductlogIn(type){
		var ipStr = "/tps/QueryTenderNotice/query/supplier/searchTenderDetail?pkPmsMain=NzA5MzM1MDQ=";
//		var protocol = location.protocol;
		var protocol = "https:";
		var hostname = location.hostname;
		//const encodedStr = btoa(protocol + "//" + hostname + ipStr);
		var encodedStr = "";
	
		var fullIp = "/tps/sso/init/" + type + "/" + encodedStr;
		console.log(fullIp);

		$("#signInModel").attr("action", fullIp);
		$("#signInModel").submit();
	}

	// 招標文件會員線上瀏覽
	function showBidGlance() {
		// http://ebuying.hinet.net/Web_EPVAS/EPaperBro/EPaperOperation.do?action=memberinfo.doc_glance&docType=1&primaryKey=NzA5MzM1MDQ=
		window.open("?bidType=O&mainPk=NzA5MzM1MDQ=","_blank","")		
	}

	// 歷史招標文件線上瀏覽 - 已登入
	function tenderFileDownload() {
		// 2022-05-10 本機測試可直接通過，加入判斷是否為本機(便於測試)
		var hostname = location.hostname;
		if (isStructureSafe == "true" || (hostname == "localhost" || hostname == "127.0.0.1")) {
			if("" == "Y"){ // 機關端 
				$("#paymentDownloadModel").attr('action', '/tps/QueryTender/query/thdTenderHisResultDownload?caseNo=1140205&orgId=********&tenderSq=01&aa-uid=&aa-pwd=');
			}else if("" == "N" || "true" == "true" || "" == "true"){ // 廠商端 or 民眾端
				$("#paymentDownloadModel").attr('action', '/tps/tom/thdTender/query/thdTenderHisResult?caseNo=1140205&orgId=********&tenderSq=01');
			}
			$("#paymentDownloadModel").submit();
		} else {
			alertBlock();
		}
	}
	
	// 歷史招標文件線上瀏覽 - 未登入
	function tenderFilePay() {
		// 2022-05-10 本機測試可直接通過，加入判斷是否為本機(便於測試)
		var hostname = location.hostname;
		if (isStructureSafe == "true" || (hostname == "localhost" || hostname == "127.0.0.1")) {
			$("#paymentDownloadModel").attr('action', '/tps/QueryTender/query/thdTenderHisResult?caseNo=1140205&orgId=********&tenderSq=01');
			$("#paymentDownloadModel").submit();
		} else {
			alertBlock();
		}
	}

	function showPrintRange() {
		var date = new Date();
		var cssAdd = "";
		var scriptAdd = "$(document).ready(function(){$('body').css('background', '#fff'); $('tr td').css('border-width', '2px');$('tr td').css('border-width', '2px');})";
		var win = window.open(this.href,'demo','toolbar=no,resizable=yes,scrollbars=yes');
		win.document.write("<link rel='stylesheet' type='text/css' href='/tps/css/all.css'/>");
		win.document.write("<link href='/tps/css/css.css' rel='stylesheet' type='text/css' />");
		win.document.write("<title>友善列印</title>");
/* 		win.document.write("<div style='text-align:center; font-size:22pt;'>公開招標公告(稿)</div>"); */		
		win.document.write("<div class='R'><div class='R2'>列印時間：" + formatDateTime(date) + "</div></div>");
		win.document.write("<meta name='viewport' content='width=device-width, initial-scale=1'><script src='https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js'><");
		win.document.write("/script>");
		win.document.write("<script type='text/javascript'>" + scriptAdd + "</");
		win.document.write("script>");
		win.document.write(document.getElementById("printRange2").innerHTML);
		win.document.write(document.getElementById("printRange").innerHTML);
		win.document.write("<style>@media print{p.noPrint{display:none;}}</style>");
		win.document.write("<div><span class='red'>◎</span>以上招標公告內容如與招標文件不一致者，請依政府採購法第41條向招標機關反映。</div>");
		win.document.write("<p align='center' class='noPrint bt_cen2'><input type='button' value='列印' onclick='window.print()'/> <input type='button' value='取消' onclick='window.close()'/></p>");
		win.document.close();
	}
	
	function print_text(){
		var popupWindow =
			window.open('','_myBlank','status=yes,toolbar=no,menubar=no,location=no,resizable=yes,scrollbars=yes,width=997,height=768',true);
		document.getElementById('printText').submit();
	}

	var formatDateTime = function (date) {  
	    var y = date.getFullYear() - 1911;  
	    var m = date.getMonth() + 1;  
	    m = m < 10 ? ('0' + m) : m;  
	    var d = date.getDate();  
	    d = d < 10 ? ('0' + d) : d;  
	    var h = date.getHours(); 
	    h = h < 10 ? ('0' + h) : h;  
	    var minute = date.getMinutes();  
	    minute = minute < 10 ? ('0' + minute) : minute;  
	    return y + '/' + m + '/' + d+' '+h+':'+minute;  
	}

	//招標方式
	var TenderWay = {
		"12" : "公開取得電子報價單",
		"2" : "公開取得報價單或企劃書",
		"1" : "公開招標",
		"4" : "經公開評選或公開徵求之限制性招標",
		"5" : "選擇性招標(建立合格廠商名單)",
		"7" : "選擇性招標(建立合格廠商名單後續邀標)",
		"3" : "選擇性招標(個案)",
		"10" : "電子競價"
	};

	// 文字列印
	function showPrintText(){
		var controller = "true" == "true" && "" != "true" ? "QueryTender" : "QueryTenderNotice";
		var userType = "" == "true" ? "query/supplier": "query";
		var history = "true" == "true" ? "historyTenderDetail" : "searchTenderDetail";

		var url = "/tps/" + controller + "/" + userType + "/" + history;

		if("true" == "true"){
			$("#DirectHistForm").attr("action", url);
		}else{
			$("#DirectForm").attr("action", url);
			$("#DirectForm").attr("method", "get");
		}
		
		var win = window.open('', 'demo', 'status=yes,toolbar=no,menubar=no,location=no,resizable=yes,scrollbars=yes', true);
		if("true" == "true"){
			$("#DirectHistForm").submit();
		}else{
			$("#DirectForm").submit();
		}
	}
	
	// 文字列印公報版
	function printTenderText(){
		const encodedStr = "true" == "true" ? "OTUxNDQwMzc=" : "NzA5MzM1MDQ=";
		var area = "true" == "true" ? "history" : "formal";
		var win = window.open("/tps/QueryTenderNotice/query/printTenderText?primaryKey=" + encodedStr + "&area=" + area,'_blank','status=yes,toolbar=no,menubar=no,location=no,resizable=yes,scrollbars=yes,width=750,height=700',true);
		win.document.close();
	}

	function shareurl(){
		var tenderOrgName="桃園市立內壢高級中等學校";
		var tenderCaseNo="1140205";
		var tenderName= $("#tenderNameText").html();//"化學實驗室整修計畫";
		tenderName = tenderName.trim();
		var targetDate=dateTransMinGo("2025-06-26");
		var isorg="";
		const encodedStr = "NzA5MzM1MDQ=";
		var protocol = location.protocol;
		var hostname = location.hostname;
		var url = protocol + "//" + hostname + "/tps/QueryTender/query/searchTenderDetail?pkPmsMain=";
		if(isorg=="Y"){
			var div = '<div id="shareDiv" style="cursor: default;" >' + 
			'<table><tr><td>' + 
			'公告類別：' + '招標公告' + 
			'</td></tr>'+'<tr><td>招標機關：' + tenderOrgName + 
			'</td></tr>'+'<tr><td>標案案號：' + tenderCaseNo + 
			'</td></tr>'+'<tr><td>標案名稱：' + tenderName + 
			'</td></tr>'+'<tr><td>公告日期：' + targetDate + 
			'</td></tr>'+'<tr><td>連結網址：' + url +encodedStr+ 
			'</table>' + 
			'<table align="left"><tr><td> ' + 
			'<font color="red" >◎此區塊內容僅提供您調整內容並進行複製使用。</font>' + 
			'</td></tr><tr>' + 
			'<td style="padding-left: 225px;">' + 
			'<button class="btn_4c mysp" onclick="window.close()">關閉</button>' + 
			'</td></tr></table>' + 
			'</div>';
		}else{
			var div = '<div id="shareDiv" style="cursor: default;" >' + 
			'<table><tr><td>' + 
			'公告類別：' + '招標公告' + 
			'</td></tr>'+'<tr><td>招標機關：' + tenderOrgName + 
			'</td></tr>'+'<tr><td>標案案號：' + tenderCaseNo + 
			'</td></tr>'+'<tr><td>標案名稱：' + tenderName + 
			'</td></tr>'+'<tr><td>公告日期：' + targetDate + 
			'</td></tr>'+'<tr><td>連結網址：' + url + 
			'</table>' + 
			'<table align="left"><tr><td> ' + 
			'<font color="red" >◎此區塊內容僅提供您調整內容並進行複製使用。</font>' + 
			'</td></tr><tr>' + 
			'<td style="padding-left: 225px;">' + 
			'<button class="btn_4c mysp" onclick="window.close()">關閉</button>' + 
			'</td></tr></table>' + 
			'</div>';
			}
			var scriptText = "$(document).ready(function() {replacePageCodeWithHardWordImg($('#tenderNameText'));$('body').css('background', '#fff');});";
			var win = window.open(this.href,'demo','height=350,width=570,toolbar=no,resizable=yes,scrollbars=yes');
			win.document.write("<link rel='stylesheet' type='text/css' href='/tps/css/all.css'/>");
			win.document.write("<link href='/tps/css/css.css' rel='stylesheet' type='text/css' />");
			win.document.write("<script type='text/javascript' src='/tps/js/hardword_function.js'></");
			win.document.write("script>");
			win.document.write("<meta name='viewport' content='width=device-width, initial-scale=1'><script src='https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js'><");
			win.document.write("/script>");
			win.document.write("<title>網址分享</title>");
			win.document.write("<script type='text/javascript'>" + scriptText + "</");
			win.document.write("script>");
			win.document.write(div);
			win.document.close();
		}
	
	// 資料匯出
	function TenderExport() {
		document.getElementById('downxml').submit();
	}
	
	function openPecExpert(inputPk){
		var url = "/pec/PecQueryPublic/readPecQueryPublicView?inputPK=********";
		if (url != "") {
			window.open(url,'showBidder','status=yes,toolbar=yes,menubar=yes,location=yes,resizable=yes,scrollbars=yes,width=750,height=500',true);	
		} else {
			
			window.open('/pec/PecQueryPublic/readPecQueryPublicView?inputPK=' + inputPk,'showBidder','status=yes,toolbar=yes,menubar=yes,location=yes,resizable=yes,scrollbars=yes,width=750,height=500',true);
		}
	}

	/***********查看合作銀行_start**********/
	var allowedBank = [];
	function showAllowedBank() {
		if (allowedBank.length > 0) {
			if ($('#allowedBankContainer').length > 0) {
				$.blockUI({
					message: $('#allowedBankContainer'), 
					css: {width:'300px',padding:'0px', top:'180px', left:'40%'}
				});
			}
		} else {
			getBankInfo();
		}
	}

	function getBankInfo() {
		var url = "" == "Y" ? "/tps/QueryTenderNotice/query/getEfcsAllowdBank" : "/tps/QueryTenderNotice/query/supplier/getEfcsAllowdBank";
		$.ajax({
			type: "POST",
		    url: url,
		    data: {},
		    cache: false,
		    success: function(result) {
		    	var resultJson = {success:false};
		    	var parseError = false;
		    	try {
		    		resultJson = result;
		    	} catch (e) {
		    		parseError = true;
		    	}

		    	if(!parseError) {
	    	    	if (resultJson.success) {
	    	    		allowedBank = resultJson.allowedBank;
	    	    		var html = "<div><h3>台灣票據交換所合作銀行清單</h3><table>";
	    	    		html += "<thead><tr><th>銀行代碼</th><th>銀行名稱</th></tr></thead>";
	    	    		html += "<tbody>";
	    	    		for(var i=0, len=allowedBank.length; i<len; i++) {
	    	    			html += "<tr><td>";
	    	    			html += allowedBank[i].bankCode;
	    	    			html += "</td><td>";
	    	    			html += allowedBank[i].bankName;
	    	    			html += "</td></tr>";
	    	    		}
	    	    		html += "</tbody>";
	    	    		html += "</table></div>";
	    	    		$("#allowedBankDiv").html(html);
	    	    		setBankDivStyle();
	    	    		showAllowedBank();
	    	    	} else {
	    	    		$.blockUI({message:"發生錯誤：" + resultJson.errMsg });
	      	    	  	setTimeout($.unblockUI, 2000);	      	    	  	
	    	    	}
		    	} else {
		    		$.blockUI({message:"發生錯誤：無法取得台灣票據交換所合作銀行清單"});
	  	    	  	setTimeout($.unblockUI, 2000);      	    	  
		    	}
		      },
		      error: function(req, errMsg) {
			      console.log("errMsg:" + errMsg);
		    	  $.blockUI({message:"發生錯誤：無法取得台灣票據交換所合作銀行清單"});
		    	  setTimeout($.unblockUI, 2000); 
		      }
		});
	}

	function setBankDivStyle() {
		try {
			$("#allowedBankDiv table").css("border", "1px solid #FFFFFF");
			$("#allowedBankDiv table").css("width", "300px");
			$("#allowedBankDiv table").css("text-align", "left");
			$("#allowedBankDiv table").css("border-collapse", "collapse");
			
			$("#allowedBankDiv td, #allowedBankDiv th").css("border", "1px solid #FFFFFF");
			$("#allowedBankDiv td, #allowedBankDiv th").css("padding", "3px 2px");
			
			$("#allowedBankDiv tbody td").css("font-size", "16px");
			$("#allowedBankDiv tr:nth-child(even)").css("background", "#D0E4F5");
			$("#allowedBankDiv thead").css("background", "#0B6FA4");
			$("#allowedBankDiv thead").css("border-bottom", "5px solid #FFFFFF");
			$("#allowedBankDiv thead th").css("font-size", "17px");
			$("#allowedBankDiv thead th").css("font-weight", "bold");
			$("#allowedBankDiv thead th").css("color", "#FFFFFF");
			$("#allowedBankDiv thead th").css("text-align", "left");
			$("#allowedBankDiv thead th").css("border-left", "2px solid #FFFFFF");
			
			$("#allowedBankDiv thead th:first-child").css("border-left", "none");
		} catch (e) {
		}
	}

	function checkEbbTrail() {
		try {
			if ($("#showAllowdBankdiv").length>0) {
				$("#showAllowdBankdiv").show();
			}
		} catch (e) {
		}
		
		var ebbTrailConfig = $("#ebbTrailConfig").val();
		if (ebbTrailConfig != null && ""!=ebbTrailConfig){
			var bid_free_date = ebbTrailConfig.split(",");
			var today=new Date();
			var currentDate = (today.getFullYear()-1911)+''
			+((today.getMonth()+1<10) ? '0'+(today.getMonth()+1) : (today.getMonth()+1)) +''
			+((today.getDate()<10) ? '0'+today.getDate() : today.getDate());
			if (currentDate >= bid_free_date[0] && currentDate <= bid_free_date[1]){
				var newBidFreeDate = bid_free_date[1].substring(0,3)+'/'+bid_free_date[1].substring(3,5)+'/'+bid_free_date[1].substring(5,7);
				bid_free_date[1] = newBidFreeDate;
				bid_free_date[2] = "<strike>12</strike> " + bid_free_date[2];
				bid_free_date[3] = "<strike>20</strike> " + bid_free_date[3];
				return bid_free_date;
			}
		}
		return "";
	}
	/***********查看合作銀行_end**********/
	
	// 加入追蹤標案
	function addTender(pmsMainPK){
		$.ajax({
			type: "POST",
			url: "/tps/QueryTenderNotice/query/supplier/createTracedTender",
			data: {"pkPmsMain" : pmsMainPK},
			dataType: "text",
			timeout:30000,
			success: function(tracedMsg){
				$("#tracedMsg").html(tracedMsg);
				$.blockUI({
					message: $('#tracedTender'), 
					css: {width:'300px',padding:'0px', top:'180px', left:'40%'}
				});
			}
		});
	}

	function alertBlock() {
		$.blockUI({
			message: "<br>檢驗檔案結構有問題，請洽客服中心！<br><br><div class='bt_cen1'><a href='javascript:void(0)' onclick='$.unblockUI()''>關閉</a></div>",
			blockMsgClass:'BlockAdd'	
		});
	}
</script>
<!-- End javaScript、jQuery  -->
<form id="historyTenderDetail" name="historyTenderDetail" action="/tps/QueryTender/query/historyTenderDetail?fkPmsMainHist=OTUxNDQwMzc=" method="post">
	<input type="hidden" id="fkPmsMainHistTender" name="fkPmsMainHist"/>
<div>
<input type="hidden" name="_csrf" value="a6f9b044-a643-4f17-905d-576f9c300589" />
</div></form>
<form id="historyAtmDetail" name="historyAtmDetail" action="/tps/QueryTender/query/historyTenderDetail?fkPmsMainHist=OTUxNDQwMzc=" method="post">
	<input type="hidden" id="fkPmsMainHistAtm" name="fkPmsMainHist" >
<div>
<input type="hidden" name="_csrf" value="a6f9b044-a643-4f17-905d-576f9c300589" />
</div></form>
<form id="historyNonAtmDetail" name="historyNonAtmDetail" action="/tps/QueryTender/query/historyTenderDetail?fkPmsMainHist=OTUxNDQwMzc=" method="post">
	<input type="hidden" id="fkPmsMainHistNonAtm" name="fkPmsMainHist" >
<div>
<input type="hidden" name="_csrf" value="a6f9b044-a643-4f17-905d-576f9c300589" />
</div></form>
<div id="printRange2">
	<input type=hidden id="agreementJsonStr" name="agreementJsonStr" value='{&#34;agreementList&#34;:[{&#34;isConverFromGPA&#34;:&#34;false&#34;,&#34;flag&#34;:&#34;N&#34;,&#34;law&#34;:&#34;&#34;,&#34;isDefaultNotTreatyModifyRsn&#34;:&#34;false&#34;,&#34;isDefaultNotTreatyModifyApplied&#34;:&#34;false&#34;,&#34;isDefaultYesTreaty&#34;:&#34;false&#34;,&#34;threshold&#34;:&#34;&#34;,&#34;isSameOpen&#34;:&#34;true&#34;,&#34;type&#34;:&#34;GPA2&#34;,&#34;isShowTreaty&#34;:&#34;true&#34;,&#34;isShowAgreementLaw&#34;:&#34;true&#34;,&#34;isConvertToOther&#34;:&#34;false&#34;,&#34;isNotShow&#34;:&#34;false&#34;,&#34;excldRsn&#34;:&#34;50003059&#34;,&#34;openPro&#34;:&#34;0&#34;,&#34;openSrv&#34;:&#34;0&#34;,&#34;isApplied&#34;:&#34;N&#34;,&#34;isDefaultNotTreaty&#34;:&#34;true&#34;,&#34;isShowPropertyItemList&#34;:&#34;false&#34;,&#34;isShowServiceItemList&#34;:&#34;false&#34;},{&#34;isConverFromGPA&#34;:&#34;false&#34;,&#34;flag&#34;:&#34;N&#34;,&#34;law&#34;:&#34;&#34;,&#34;isDefaultNotTreatyModifyRsn&#34;:&#34;false&#34;,&#34;isDefaultNotTreatyModifyApplied&#34;:&#34;false&#34;,&#34;isDefaultYesTreaty&#34;:&#34;false&#34;,&#34;threshold&#34;:&#34;&#34;,&#34;isSameOpen&#34;:&#34;true&#34;,&#34;type&#34;:&#34;ANZTEC&#34;,&#34;isShowTreaty&#34;:&#34;true&#34;,&#34;isShowAgreementLaw&#34;:&#34;true&#34;,&#34;isConvertToOther&#34;:&#34;false&#34;,&#34;isNotShow&#34;:&#34;false&#34;,&#34;excldRsn&#34;:&#34;1&#34;,&#34;openPro&#34;:&#34;0&#34;,&#34;openSrv&#34;:&#34;0&#34;,&#34;isApplied&#34;:&#34;N&#34;,&#34;isDefaultNotTreaty&#34;:&#34;true&#34;,&#34;isShowPropertyItemList&#34;:&#34;false&#34;,&#34;isShowServiceItemList&#34;:&#34;false&#34;},{&#34;isConverFromGPA&#34;:&#34;false&#34;,&#34;flag&#34;:&#34;N&#34;,&#34;law&#34;:&#34;&#34;,&#34;isDefaultNotTreatyModifyRsn&#34;:&#34;false&#34;,&#34;isDefaultNotTreatyModifyApplied&#34;:&#34;false&#34;,&#34;isDefaultYesTreaty&#34;:&#34;false&#34;,&#34;threshold&#34;:&#34;&#34;,&#34;isSameOpen&#34;:&#34;true&#34;,&#34;type&#34;:&#34;ASTEP&#34;,&#34;isShowTreaty&#34;:&#34;true&#34;,&#34;isShowAgreementLaw&#34;:&#34;true&#34;,&#34;isConvertToOther&#34;:&#34;false&#34;,&#34;isNotShow&#34;:&#34;false&#34;,&#34;excldRsn&#34;:&#34;1020&#34;,&#34;openPro&#34;:&#34;0&#34;,&#34;openSrv&#34;:&#34;0&#34;,&#34;isApplied&#34;:&#34;N&#34;,&#34;isDefaultNotTreaty&#34;:&#34;true&#34;,&#34;isShowPropertyItemList&#34;:&#34;false&#34;,&#34;isShowServiceItemList&#34;:&#34;false&#34;}]}'/>
	
		<div class="title_1">
			公開招標
			公告
		</div>
	
	<br>
	
		<p>公告日：<span id="targetDate_1"></span></p>
	
</div> <!-- id="printRange2" 結尾  -->

	


	<div class="R3">
		




<style>
#share-bar span {
	color: #ab6805;
}

#share-bar .icon {
	width: 30px;
	height: 30px;
}
</style>
<script>
function sharing(idx) {
	var idx = ($.isNumeric(idx) ? parseInt(idx) : -1);

	var sharingUrl = encodeURIComponent('https:\/\/web.pcc.gov.tw\/tps\/QueryTender\/query\/historyTenderDetail?fkPmsMainHist=OTUxNDQwMzc=');
	var isEng = 'false' === 'true';
	var isAtmAward = '' === 'true';
	var isNonAtmAward = '' === 'true';
	var textArr = [
		(isEng ? 'Government e-Procurement System - Tender Notice' : '政府電子採購網－招標公告'),
		'[' + (isEng ? 'Procuring entity' : '機關名稱') + ']桃園市立內壢高級中等學校',
		'[' + (isEng ? 'Subject of procurement' : '標案名稱') + ']化學實驗室整修計畫',
		'[' + (isEng ? 'Job number' : '標案案號') + ']1140205',
		'[' + (isEng ? 'Link' : '連結網址') + ']'
	];
	if(isAtmAward){
		textArr = [
			(isEng ? 'Government e-Procurement System - Tender Notice' : '政府電子採購網－決標公告'),
			'[' + (isEng ? 'Procuring entity' : '機關名稱') + ']桃園市立內壢高級中等學校',
			'[' + (isEng ? 'Subject of procurement' : '標案名稱') + ']化學實驗室整修計畫',
			'[' + (isEng ? 'Job number' : '標案案號') + ']1140205',
			'[' + (isEng ? 'Link' : '連結網址') + ']'
		];
	}
	if(isNonAtmAward){
		textArr = [
			(isEng ? 'Government e-Procurement System - Tender Notice' : '政府電子採購網－無法決標公告'),
			'[' + (isEng ? 'Procuring entity' : '機關名稱') + ']桃園市立內壢高級中等學校',
			'[' + (isEng ? 'Subject of procurement' : '標案名稱') + ']化學實驗室整修計畫',
			'[' + (isEng ? 'Job number' : '標案案號') + ']1140205',
			'[' + (isEng ? 'Link' : '連結網址') + ']'
		];
	}
	var text = encodeURIComponent(textArr.join('\r\n'));
	textArr.pop(); // remove last item: link
	var textWithoutLink = encodeURIComponent(textArr.join('\r\n'));
	var href;
	switch (idx) {
		case 1:
			href = 'http://www.facebook.com/share.php?u=' + sharingUrl;
			break;
		case 2:
			href = 'https://twitter.com/share?url=' + sharingUrl + '&text=' + text;
			break;
		case 3:
			href = 'http://www.plurk.com/?qualifier=shares&status=' + sharingUrl + encodeURIComponent(' \r\n') + textWithoutLink;
			break;
		case 4:
			href = 'https://social-plugins.line.me/lineit/share?url=' + sharingUrl + '&text=' + textWithoutLink;
			break;
		case 5:
			href = 'mailto:?subject=' + textArr[0] + '&body=' + text + sharingUrl;
			if (href.length > 2034) {
				alert('此則分享內容字數較多，若無法順利寄送郵件，請改用FireFox。');
			}
			break;
	}
	if (href) {
		$('<a>', { href : href, target : '_blank', rel: 'noopener noreferrer' })[0].click();
	}
	return false; // for prevent browser
}
</script>

<div id="share-bar">
	
	
	<span>分享</span>
	<a href="javascript:;" onclick="sharing(1);" title='分享至facebook(另開新視窗)'>
		<img class="icon" src='/tps/images/icon_01.png' alt='分享至facebook(另開新視窗)' />
	</a>
	<a href="javascript:;" onclick="sharing(2);" title='分享至twitter(另開新視窗)'>
		<img class="icon" src='/tps/images/icon_02.png' alt='分享至twitter(另開新視窗)' />
	</a>
	<a href="javascript:;" onclick="sharing(3);" title='分享至plurk(另開新視窗)'>
		<img class="icon" src='/tps/images/icon_03.png' alt='分享至plurk(另開新視窗)' />
	</a>
	<a href="javascript:;" onclick="sharing(4);" title='分享至line(另開新視窗)'>
		<img class="icon" src='/tps/images/icon_04.png' alt='分享至line(另開新視窗)' />
	</a>
	<a href="javascript:;" onclick="sharing(5);" title="寄送電子郵件(另開新視窗)">
		<img class="icon" src='/tps/images/icon_05.png' alt="寄送電子郵件(另開新視窗)" />
	</a>
</div>
	</div>
	<div  style="float:right;">
		
		
	
		<div class="search_print" style="cursor: pointer;"  >
			<img src="/tps/images/print.jpg" alt="文字列印">
			<a href="javascript:void(0)" onclick="showPrintText();" title=文字列印>文字列印</a>
		</div>
		<div class="search_print" style="cursor: pointer;">
			<img src="/tps/images/print.jpg" alt="友善列印">
			<a href="javascript:void(0)" onclick="showPrintRange();" title=友善列印>友善列印</a>
		</div>

	</div>

	<br>
	<div id="validateTenderFileDownload" style="display: none;">
		<br/>
		<form id="form_downloadFile" rel="noopener noreferrer" action="http://localhost:3000/docDownload/step1" method="POST" target="_blank">
				<input type="hidden" name="tenderInfo" value="">
				<!-- 機關端 叫檢驗上傳文件 -->
		<div>
<input type="hidden" name="_csrf" value="a6f9b044-a643-4f17-905d-576f9c300589" />
</div></form>
		
		
	</div>
	<div class="acc tbc3" style="font-size: 18px; text-align: center;">
		<span id="hintMessage"></span>
	</div>

	<div id="printRange">
		
		<!--表格開始-->
	<table class="tb_01" summary="機關資料">
		<caption>機關資料</caption>
		<tr class="tb_s06">
			<td rowspan="10" class="tbg_L1">機關資料</td>
		</tr>
		<tr class="tb_s06">
			<td headers="tb_02" class="tbg_1">機關代碼</td>
			<td headers="tb_02" class="tbg_2">
				********
				</td>
		</tr>
		
		<tr class="tb_s07">
			<td headers="tb_02" class="tbg_1">機關名稱</td>
			<td headers="tb_02" class="tbg_2">
				桃園市立內壢高級中等學校
			</td>
		</tr>
		<tr class="tb_s06">
			<td headers="tb_02" class="tbg_1">單位名稱</td>
			<td headers="tb_02" class="tbg_2">
				總務處
			</td>
		</tr>
		<tr class="tb_s07">
			<td headers="tb_02" class="tbg_1">機關地址</td>
			<td headers="tb_02" class="tbg_2">
				
					320
				
				
				桃園市
				中壢區
				成章四街120號
			</td>
		</tr>
		<tr class="tb_s06">
			<td headers="tb_02" class="tbg_1">聯絡人</td>
			<td headers="tb_02" class="tbg_2">
				吳介仁
			</td>
		</tr>
		<tr class="tb_s07">
			<td headers="tb_02" class="tbg_1">聯絡電話</td>
			<td headers="tb_02" class="tbg_2">
				
					(03)
				
				
					4528080
				
				
					#252
				
			</td>
		</tr>
		<tr class="tb_s06">
			<td headers="tb_02" class="tbg_1">傳真號碼</td>
			<td headers="tb_02" class="tbg_2">
				
					(03)
				
				
					4341383
				
			</td>
		</tr>
		<tr class="tb_s07">
			<td headers="tb_02" class="tbg_1">電子郵件信箱</td>
			<td headers="tb_02" class="tbg_2">
				<EMAIL>
			</td>
		</tr>
	</table>

	<!--表格結束-->
	<!--表格開始-->
	<table class="tb_04 sp2" summary="採購資料">
		<caption>採購資料</caption>
		<tr class="tb_s06">
			<td rowspan="28" class="tbg_L2">採購資料</td>
		</tr>
		<tr class="tb_s06">
			<td headers="tb_02" class="tbg_4">標案案號</td>
			<td headers="tb_02" class="tbg_4R">
				1140205
			</td>
		</tr>
		<tr class="tb_s07">
			<td headers="tb_02" class="tbg_4">標案名稱</td>
			<td headers="tb_02" class="tbg_4R" id="tenderNameText">
				化學實驗室整修計畫
			</td>
		</tr>
		<tr class="tb_s06">
			<td headers="tb_02" class="tbg_4">標的分類</td>
			<td headers="tb_02" class="tbg_4R">
				財物類<br>
				381 - 傢具
			</td>
		</tr>
		
			
			<tr class="tb_s07">
				<td headers="tb_02" class="tbg_4">財物採購性質</td>
				<td headers="tb_02" class="tbg_4R">
					<span id="fkTpamProperty">買受,定製</span>
				</td>
			</tr>
		
		
		
		<tr class="tb_s07">
			<td headers="tb_02" class="tbg_4">採購金額級距</td>
			<td headers="tb_02" class="tbg_4R">
				<span id="fkPmsProcurementRange">公告金額以上未達查核金額</span>
			</td>
		</tr>
		
		
		<tr class="tb_s06">
			<td headers="tb_02" class="tbg_4">辦理方式</td>
			<td headers="tb_02" class="tbg_4R">
				<span id="fkTpamHowBid">自辦</span>
				
			</td>
		</tr>
		<tr class="tb_s07">
			<td headers="tb_02" class="tbg_4">依據法條</td>
			<td headers="tb_02" class="tbg_4R">
				<div id="fkTpamByLaw">採購法第18條、第19條</div>
				
			</td>
		</tr>
		
			<tr class="tb_s06" id="tr_isApplied">
				<td headers="tb_02" class="tbg_4">是否適用條約或協定之採購</td>
				<td headers="tb_02" class="tbg_4R" style="padding: 5px;">
					
						
						
						
					


<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<link href="/tps/css/jquery-ui.min.css" rel="stylesheet">
<link href="/tps/css/text.css" rel="stylesheet">
</head>
<body>






<style type="text/css">
	.gpa_shift {
		margin-left: 30px;
	}
	.gpa_shift2 {
		margin-left: 60px;
	}
	.gpa_error_msg {
		color:#FF0000;
		font-size:10pt;
	}
</style>


	
	<span style="color: blue; font-weight: bold;">是否適用WTO政府採購協定(GPA)：</span>
	
	
		否 
		
	


	<hr>
	
	<span style="color: blue; font-weight: bold;">是否適用臺紐經濟合作協定(ANZTEC)：</span>
	
	
		否
		
	


	<hr>
	
	<span style="color: blue; font-weight: bold;">是否適用臺星經濟夥伴協定(ASTEP)：</span>
	
	
		否
		
	

</body>
</html>		
				</td>
			</tr>		
			
			
			
		
		
			<tr class="tb_s06">
				<td headers="tb_02" class="tbg_4">本採購是否屬「具敏感性或國安(含資安)疑慮之業務範疇」採購</td>
				<td headers="tb_02" class="tbg_4R">
					<span id="isSensitive">否</span>
				</td>
			</tr>
			<tr class="tb_s07">
				<td headers="tb_02" class="tbg_4">本採購是否屬「涉及國家安全」採購</td>
				<td headers="tb_02" class="tbg_4R">
					<span id="isAffectSec">否</span>
				</td>
			</tr>
			
				<tr class="tb_s06">
					<td headers="tb_02" class="tbg_4">預算金額</td>
					<td headers="tb_02" class="tbg_4R">
						<div>3,002,120元</div>
						
						
						<input type="hidden" id="budget" value="3002120"/>
					</td>
				</tr>
			
			<tr class="tb_s07">
				<td headers="tb_02" class="tbg_4">預算金額是否公開</td>
				<td headers="tb_02" class="tbg_4R">
					<span id="budgetIsPdt">是</span>
					
						
					
				</td>
			</tr>
		
		
		
			
				
			<tr class="tb_s06">
				<td headers="tb_02" class="tbg_4">後續擴充</td>
				<td headers="tb_02" class="tbg_4R">
					<span id="fuRite">是</span>
									
						<div id="div_fuRiteComment" class="atsp">
							依政府採購法第22條第1項第7款，須敘明後續擴充之期間、金額或數量：
							<div style="white-space: pre-line;">標餘款額度內辦理後續擴充</div>
						</div>
					
				</td>
			</tr>
			
			<tr class="tb_s07">
				<td headers="tb_02" class="tbg_4">是否受機關補助</td>
				<td headers="tb_02" class="tbg_4R">
					<span id="isGrant">否</span>
					
					
				</td>
			</tr>
		
		
		
		
		<!-- 2022-11-01 - 需求單 - 111-10-01-招標公告新增「是否提供英文招標文件」欄位相關功能_系統功能增修規劃書_1021 -->
        
        
		<!-- 2023-03-20 - 111-12-01-招標及決標公告新增「是否涉及政策及業務宣導業務」欄位 -->
		
			<tr class="tb_s06">
				<td headers="tb_02" class="tbg_4">是否為政策及業務宣導業務</td>
				<td headers="tb_02" class="tbg_2 ">
					否
				</td>
			</tr>
		
	</table>

	<!--表格結束-->
	<!--表格開始-->
	<table class="tb_05 sp2" summary="招標資料">
		<caption>招標資料</caption>
		<tr class="tb_s06">
			<td rowspan="27" class="tbg_L3">招標資料</td>
		</tr>
		<tr class="tb_s06">
			<td headers="tb_02" class="tbg_5">招標方式</td>
			<td headers="tb_02" class="tbg_4R">
				<span id="fkPmsTenderWay">公開招標</span>
			</td>
		</tr>
		
			<tr class="tb_s07">
				<td headers="tb_02" class="tbg_5">決標方式</td>
				<td headers="tb_02" class="tbg_4R">
					<table>
						<tr>
							<td style="border: none;">
								<span id="fkPmsAwardWay">最有利標</span>
							</td>
							<td style="border: none;">
								
								
									
									
										
										
									
									
									
										<a href="javascript:void(0);" style="float: left;" title=採購評選委員名單 onclick="openPecExpert('********');"><div class="btn_4c mysp2">採購評選委員名單</div></a>
									
								
							</td>
						</tr>
					</table>
				</td>
			</tr>
		
		
		
		<tr class="tb_s07">
			<td headers="tb_02" class="tbg_5">新增公告傳輸次數</td>
			<td headers="tb_02" class="tbg_4R">
				01
			</td>
		</tr>
		
		<tr class="tb_s06">
			<td headers="tb_02" class="tbg_5">招標狀態</td>
			<td headers="tb_02" class="tbg_4R">
				<span id="fkTpamTenderStatus">第一次公開招標</span>
			</td>
		</tr>
		<!-- false -->
		
		<tr class="tb_s07">
			<td headers="tb_02" class="tbg_5">
				
					
						公告日
						
					
				
			</td>
			<td headers="tb_02" class="tbg_4R">
				
					<span id="targetDate">114/06/26</span>
				
			</td>
		</tr>
		
		
			
		
		
			<tr class="tb_s06">
				<td headers="tb_02" class="tbg_5">是否複數決標</td>
				<td headers="tb_02" class="tbg_4R">
					<span id="isMultipleAward">否</span>
				</td>
			</tr>
			<tr class="tb_s07">
				<td headers="tb_02" class="tbg_5">是否訂有底價</td>
				<td headers="tb_02" class="tbg_4R">
					<div id="isGovernmentEstimate">否</div>
					
				</td>
			</tr>
		
		
			<tr class="tb_s06">
				<td headers="tb_02" class="tbg_5">價格是否納入評選</td>
				<td headers="tb_02" class="tbg_4R">
					<div class="tbc1L">
						是
					</div>
				</td>
			</tr>
		
		
		
			<tr class="tb_s06">
				<td headers="tb_02" class="tbg_5">所占配分或權重是否為20%以上</td>
				<td headers="tb_02" class="tbg_4R">
					<div class="tbc1L">
						是
					</div>
				</td>
			</tr>
		
		
		
		
		<tr class="tb_s06">
			<td headers="tb_02" class="tbg_5">是否屬特殊採購</td>
			<td headers="tb_02" class="tbg_4R">
				<span id="isSpecial">否</span>
			</td>
		</tr>
		<tr class="tb_s07">
			<td headers="tb_02" class="tbg_5">是否已辦理公開閱覽</td>
			<td headers="tb_02" class="tbg_4R">
				<span id="isReadbidTpam">否</span>
				
				
					
				
			</td>
		</tr>
		
			<tr class="tb_s06">
				<td headers="tb_02" class="tbg_5">是否屬統包</td>
				<td headers="tb_02" class="tbg_4R">
					<span id="isPackage" style="display: none;"></span>
					否
				</td>
			</tr>
		
		
		<tr class="tb_s06" id="tr_expectBenefit" style="display: none;">
			<td headers="tb_02" class="tbg_5">本案完成後所應達到之功能、效益、標準、品質或特性</td>
			<td headers="tb_02" class="tbg_4R">
				<div id="expectBenefit" style="white-space: pre-line"></div>
			</td>
		</tr>
		
			<tr class="tb_s07">
				<td headers="tb_02" class="tbg_5">是否屬共同供應契約採購</td>
				<td headers="tb_02" class="tbg_4R">
					<span id="isCpp" style="display: none;"></span>
					否
				</td>
			</tr>
			<tr class="tb_s06">
				<td headers="tb_02" class="tbg_5">是否屬二以上機關之聯合採購(不適用共同供應契約規定)</td>
				<td headers="tb_02" class="tbg_4R">
					<span id="isJointProcurement" style="display: none;"></span>
					否
				</td>
			</tr>
			<tr class="tb_s07">
				<td headers="tb_02" class="tbg_5">是否應依公共工程專業技師簽證規則實施技師簽證</td>
				<td headers="tb_02" class="tbg_4R">
					
						
						
							<span id="isEngineer">否</span>
						
					
				</td>
			</tr>
		
		
		<tr class="tb_s06">
			<td headers="tb_02" class="tbg_5">是否採行協商措施</td>
			<td headers="tb_02" class="tbg_4R">
				<span id="nego">否</span>
			</td>
		</tr>
		<tr class="tb_s07">
			<td headers="tb_02" class="tbg_5">是否適用採購法第104條或105條或招標期限標準第10條或第4條之1</td>
			<td headers="tb_02" class="tbg_4R">
				<span id="isWait">否</span>
				
			</td>
		</tr>
		<tr class="tb_s06">
			<td headers="tb_02" class="tbg_5">是否依據採購法第106條第1項第1款辦理</td>
			<td headers="tb_02" class="tbg_4R">
				<span id="isLaw106">否</span>
					
			</td>
		</tr>
	</table>

	<!--表格結束-->
	<!--表格開始-->
	<table class="tb_06 sp2" summary="領投開標">
		<caption>領投開標</caption>
		<tr class="tb_s06">
			<td rowspan="99" class="tbg_L4">領投開標</td>
		</tr>
		<tr class="tb_s06">
			<td headers="tb_02" class="tbg_6">是否提供電子領標</td>
			<td headers="tb_02" class="tbg_4R">
			<span id="isEobtain">是</span>
						
				<div class="tbc">
					<div class="tbc2">
						<div class="tbc2a">
							<div class="tbc2L atsp">機關文件費(機關實收)</div>
							<div class="tbc2R">
								<div class="cen2" id="deptCharge">0元</div>
							</div>
						</div>
						<div class="tbc2a">
							<div class="tbc2L atsp">
								系統使用費
								<img src="/tps/images/question_mark.jpg" alt="小助手圖示" class="qq" title="系統使用費用為機關文件費之10%，若不足20元以20元計">
								</div>
							<div class="tbc2R">
								<div class="cen2" id="systemCharge">20元</div>
							</div>
						</div>
						<div class="tbc2a">
							<div class="tbc2L atsp">
								文件代收費
								<img src="/tps/images/question_mark.jpg" alt="小助手圖示" class="qq" title="文件代收費為機關文件費之5%">							
							</div>
							<div class="tbc2R">
								<div class="cen2" id="docCharge">0元</div>
							</div>
						</div>
						<div class="tbc2a">
							<div class="tbc2L atsp">總計</div>
							<div class="tbc2R">
								<div class="cen2" id="sumCharge">20元</div>
							</div>
						</div>
							
					</div>
						
					<div class="tbc1">
						是否提供現場領標：
						<span id="isPhyObtain">否</span>
						
					</div>
					
						<div class="tbc1">
							<hr>
							
							
							
								<span class="link">
									<a href="/tps/QueryTender/query/downloadNoticeDocument?docType=1&pdt=2025-06-19&orgId=********&cno=1140205&seq=01&UpdSq=01&pkPmsMain=NzA5MzM1MDQ=" style="color:blue; text-decoration: underline;" title=投標須知下載>投標須知下載</a>
								</span>
							
						</div>
					
				</div>
				
				
			</td>
		</tr>
		<tr class="tb_s06">
			<td headers="tb_02" class="tbg_6">是否提供電子投標</td>
			<td headers="tb_02" class="tbg_4R">
				<span id="isEsubmit">否</span>
				
				
				
			</td>
		</tr> 		
		
		<tr class="tb_s06">
			<td headers="tb_02" class="tbg_6">截止投標</td>
			<td headers="tb_02" class="tbg_4R">
				<span id="spdt">114/07/14 17:00</span>
				
				
			</td>
		</tr>
		
			<tr class="tb_s06">
				<td headers="tb_02" class="tbg_6">開標時間</td>
				<td headers="tb_02" class="tbg_4R">
					<span id="opdt" style="display: none;"></span>
					114/07/15 10:00	
					
				</td>
			</tr>
			<tr class="tb_s06">
				<td headers="tb_02" class="tbg_6">開標地點</td>
				<td headers="tb_02" class="tbg_4R">
					<span class="hardword">行政大樓3樓家長會辦公室(桃園市中壢區成章四街120號)</span>	
				</td>
			</tr>
			
			<tr class="tb_s06">
				<td headers="tb_02" class="tbg_6">是否須繳納押標金</td>
				<td headers="tb_02" class="tbg_4R">
					是，且提供廠商線上繳納押標金	
	<!-- 				<span id="isDeposite"></span> -->
					
					
						<div class="atsp">
							<input type="hidden" id="nationalAccount" value=""/>
							<div style="word-break: break-all;">押標金額度：150000</div>
							
								<div>機關押標金指定收款機關單位：桃園市立內壢高級中等學校</div>

									<div>機關押標金指定收款帳戶：桃園市立內壢高級中等學校保管金專戶</div>

								
								
								
								
								
									<div class="tbc4">● 線上繳納押標金服務將連結到台灣票據交換所「金融業代收即時服務平台」，提供廠商轉帳存入機關帳戶，廠商必須備有讀卡機與銀行金融卡才能作業，且距截止投標期限不足5分鐘時，將不允許線上繳納押標金，請廠商提早作業。</div>
									<div class="tbc4">● 廠商線上繳納押標金時，將由台灣票據交換所收取轉帳手續費，轉帳金額10萬元以下手續費每次10元，超過10萬元手續費每次20元。</div>
								
							
						</div>
					
					
				</td>
			</tr>
			
		
		<tr class="tb_s06">
			<td headers="tb_02" class="tbg_6">投標文字</td>
			<td headers="tb_02" class="tbg_4R">
				<span id="fkTpamBlang">正體中文</span>
			</td>
		</tr>
		<tr class="tb_s06">
			<td headers="tb_02" class="tbg_6">收受投標文件地點</td>
			<td headers="tb_02" class="tbg_4R">
				
					<span class="hardword">本校總務處(320桃園市中壢區成章四街120號1樓)</span>
				
				
			</td>
		</tr>

	</table>
	<!--表格結束-->
	<!--表格開始-->
	<table class="tb_07 sp2" summary="其他" >
		<caption>其他</caption>
		<tr class="tb_s06">
			<td rowspan="99" class="tbg_L5">其他</td>
		</tr>
		<tr class="tb_s06">
			<td headers="tb_02" class="tbg_7">是否依據採購法第99條</td>
			<td headers="tb_02" class="tbg_4R">
				<span id="adaptLaw">否</span>
				
			</td>
		</tr>
		
		
		
			
		
		
		<tr id="tr_priorityCate" class="tb_s06" style="display: none;">
			<td headers="tb_02" class="tbg_7">身心障礙福利機構團體或庇護工場生產物品及服務</td>
			<td headers="tb_02" class="tbg_4R">
				<div class="tbc2L">
					項目&nbsp:&nbsp
					
					<br> 分類&nbsp:&nbsp
					
					<span id="fkPmsPriorityCate1" style="display:none;"></span>
					<span id="fkPmsPriorityCate2" style="display:none;"></span>
				</div>
			</td>
		</tr>
		
			<tr class="tb_s06">
				<td headers="tb_02" class="tbg_7">履約地點</td>
				<td headers="tb_02" class="tbg_4R">
					<div id="fkPmsExecuteLocation">桃園市(非原住民地區)</div>
					
				</td>
			</tr>
			<tr class="tb_s06">
				<td headers="tb_02" class="tbg_7">履約期限</td>
				<td headers="tb_02" class="tbg_4R">
					決標日起45天
				</td>
			</tr>
		
		<tr class="tb_s06">
			<td headers="tb_02" class="tbg_7">
			
				
					是否刊登公報
							
			</td>
			<td headers="tb_02" class="tbg_4R">
				<span id="isGazette">是</span>
			</td>
		</tr>
		
		
			<tr class="tb_s06">
				<td headers="tb_02" class="tbg_7">本案採購契約是否採用主管機關訂定之範本</td>
				<td headers="tb_02" class="tbg_4R">
					<span id="isUsePccSample">是</span>
					
				</td>
			</tr>
			
			
		
		<!-- 物調欄位 -->
		
			<tr class="tb_s06">
				<td headers="tb_02" class="tbg_7">契約是否訂有依物價指數調整價金規定</td>
				<td headers="tb_02" class="tbg_4R">
					<div>
						<div id="isCommodityPriceRule">
							否，招標文件未訂物價指數調整條款
						</div>
						
						
							<div id = "fkAtmNoCommodity">
								無預算
							</div>
						
					</div>
				</td>
			</tr>
		
		
		<tr class="tb_s06">
			<td headers="tb_02" class="tbg_7">廠商資格摘要</td>
			<td headers="tb_02" class="tbg_4R">
				
				
				
					
						<div><ol><li style='list-style: decimal inside;'>廠商登記或設立之證明</li><p class='atsp'>投標廠商之基本資格須符合以下任一資格：</p><ol><li class='atsp' style='list-style: decimal inside;'>具公司登記</li><li class='atsp' style='list-style: decimal inside;'>具商業登記</li></ol></div>
						
					
				
				
				<input type="hidden" id="vendorDescInput" value="[[3Gen]]廠商登記或設立之證明,具公司登記,具商業登記"/>
			</td>
		</tr>

		
		

		<tr class="tb_s06">
			<td headers="tb_02" class="tbg_7">是否訂有與履約能力有關之基本資格</td>
			<td headers="tb_02" class="tbg_4R">
				否
				
			</td>
		</tr>
		
		
		
		<tr class="tb_s06">
			<td headers="tb_02" class="tbg_7">附加說明</td>
			<td headers="tb_02" class="tbg_4R">
				<table style="width: 100%;">
					
					
				</table>
			</td>
		</tr>
		<tr class="tb_s06">
			<td headers="tb_02" class="tbg_7">是否刊登英文公告</td>
			<td headers="tb_02" class="tbg_4R">
				<div id="isEng">否</div>
				
			</td>
		</tr>
		<tr class="tb_s06">
			<td headers="tb_02" class="tbg_7">疑義、異議、申訴及檢舉受理單位</td>
			<td headers="tb_02" class="tbg_4R">
				<table>
					<tr>
						<td style="border: 0px; width: 150px;">疑義、異議受理單位</td>
						<td style="border: 0px;">
							桃園市立內壢高級中等學校
							
						</td>
					</tr>
						<tr>
							<td colspan="2" style="border: 0px;">
								<hr>
							</td>
						</tr>
						<tr>
							<td style="border: 0px;">申訴受理單位</td>
							<td style="border: 0px;">
								<span style="white-space: pre-line;">桃園市政府採購申訴審議委員會-(地址：330桃園市桃園區縣府路1號、電話：03-3322101分機5717、傳真：03-3391726)</span>
								
							</td>
						</tr>
					<tr>
						<td colspan="2" style="border: 0px;">
							<hr>
						</td>
					</tr>
					<tr>
						<td style="border: 0px;">檢舉受理單位</td>
						<td style="border: 0px;">
							<span style="white-space: pre-line;">桃園市調查處-(地址：330桃園市桃園區縣府路19號;桃園南門郵局第7-19號信箱、電話：03-3328888、傳真：03-3347847)
法務部調查局-(地址：231新北市新店區中華路74號;新店郵政60000號信箱、電話：02-29177777、傳真：02-29188888)
法務部廉政署-(地址：100臺北市中正區博愛路166號;100006國史館郵局第153號信箱、電話：0800286586、傳真：02-23811234)
中央採購稽核小組-(地址：110臺北市信義區松仁路3號9樓、電話：02-87897548、傳真：02-87897554)
地方政府-桃園市政府採購稽核小組-(地址：330桃園市桃園區縣府路1號11樓、電話：03-3322101分機6634-6636、傳真：03-3324575、檢舉專線(03)3391466)</span>
							
						</td>
					</tr>
				</table>
			</td>
		</tr>
		
	</table>
	
		
	
	
	
	
	
		

	
	<!--表格結束-->
	
</div>
<!--搜尋結果列表-->
<!-- 作業歷程 -->
    

	<!-- 作業歷程 -->
 
            

	<!--換頁結束-->
	<!--結束-->
	<!--查詢結果備註-->
	<!--註解-->

	<div class="search_note">
		<div style="display: inline-block; vertical-align: top; font-size: 1.2em;">註：</div>
		<table class="g_search_note" style="display: inline-block;">
			
			<tr>
				<td></td>
				<td>
					<span class="red">◎</span>
				</td>
				<td>以上招標公告內容如與招標文件不一致者，請依政府採購法第41條向招標機關反映。</td>
			</tr>
			
			
			
			<tr>
                <td></td>
                <td>
                    <span class="red">◎</span>
                </td>
                <td>
	                <a href="https://planpe.pcc.gov.tw/prms/explainLetter/readPrmsExplainLetterContentDetail?pkPrmsRuleContent=75000880&_csrf=6b3a8399-8ffb-43c9-8bfb-af0eab3be07e"
	                   target="_blank" rel="noopener noreferrer" title="信用證明解釋函(工程企字第1110018568號)">
	                    信用證明解釋函(工程企字第1110018568號)
	                </a>
                </td>
            </tr>
			<tr>
                <td></td>
                <td>
                    <span class="red">◎</span>
                </td>
                <td>
		            <a href="https://planpe.pcc.gov.tw/prms/explainLetter/readPrmsExplainLetterContentDetail?pkPrmsRuleContent=75001420&_csrf=1decc732-2fbe-4fc2-bb24-1bc63d2348b6" 
		               target="_blank" rel="noopener noreferrer" title="納稅證明解釋函(工程企字第1120010202號)">
		                 納稅證明解釋函(工程企字第1120010202號)
		            </a>
	            </td>
            </tr>
		</table>
		
		<div class="m1b">
			<div class="bt_cen2">
			
			<a href="#" onclick="history.go(-1)" title=返回>返回</a>
		</div>
	</div>
	
	
	</div>
	<!--註解結束-->
	
	
		
		
			
		
	
	<form id="paymentDownloadModel" name="paymentDownloadModel" action="/tps/tom/obtainment/common/tomEntrance" method="post">
		<input id="primaryKey" name="primaryKey" value="70933504" type="hidden" value=""/>
		<input id="signonKey" name="signonKey" type="hidden" value=""/>
		
			<input type="hidden" name="login" value="N" />
		
	<div>
<input type="hidden" name="_csrf" value="a6f9b044-a643-4f17-905d-576f9c300589" />
</div></form>
	<form id="signInModel" name="signInModel" action="/tps/" method="GET">
		<input type="hidden" name="_csrf" value="a6f9b044-a643-4f17-905d-576f9c300589"/>
	</form>
	
<!-- 	<div id="tenderPrint" style="display: none;"> -->
		

<!-- 	</div> -->
	
	<div id="tracedTender" style="display:none; cursor: default; margin-top: 20px;">
		<div id="tracedMsg"></div>
		<br/>
		<div class="bt_cen1">
			<a href="javascript:void(0)" onclick="$.unblockUI()" title=關閉>關閉</a>
		</div>
	</div>

<!-- 新版押標金 -->
<div id="ebbInputDiv"></div>
<form id="nodeJSForm" rel="noopener noreferrer" action="http://localhost:3000/docDownload/step1" method="post" target="_blank">
	<input type="hidden" id="tenderInfo" name="tenderInfo" value="" />
<div>
<input type="hidden" name="_csrf" value="a6f9b044-a643-4f17-905d-576f9c300589" />
</div></form>
<form id="DirectForm" name="DirectForm" action="/tps/QueryTenderNotice/query/searchTenderDetail" method="post" target="demo">
	<input type='hidden' name='pkPmsMain' value='NzA5MzM1MDQ=' />
	<input type="hidden" name="method" value="text"/>
<div>
<input type="hidden" name="_csrf" value="a6f9b044-a643-4f17-905d-576f9c300589" />
</div></form>
<form id="DirectHistForm" name="DirectHistForm" action="/tps/QueryTenderNotice/query/historyTenderDetail" method="post" target="demo">
	<input type='hidden' name='fkPmsMainHist' value='OTUxNDQwMzc=' />
	<input type="hidden" name="method" value="text"/>
<div>
<input type="hidden" name="_csrf" value="a6f9b044-a643-4f17-905d-576f9c300589" />
</div></form>
</div>
			<!--登入區塊 -->
			
			<!--登入區塊結束 -->
		</div>
		
	</div>
</div>

	<!--頁簽-->
	
	<!--頁簽結束--> 


	<!--footer-->
	
		
		
			


<!--footer-->
<div class="footer">
 <div class="footer_a_cen"><a href="#Z" name="Z" title="下方相關連結區" accesskey="Z" class="z-acceky">:::</a>
  <div class="footer_a_cen">
    <div class="footer_a"> 
    	<img src="/pis/images/minor_logo.png" alt="行政院工程委員會商標圖">
    	<a rel="noopener noreferrer" target="_blank" href="https://accessibility.moda.gov.tw/Applications/Detail?category=20230224201311" title="無障礙網站(另開視窗)">
    		<img src="/pis/images/ap2.0.png" border="0" width="88" height="31" alt="通過A無障礙網頁檢測"></a>
     </div>
    <div class="footer_a">
		<div class="footer_a1">
			<div class="footer_a1_1">
				<p>免費系統客服電話：0800-080-512</p>
				<p>工程會電話：(02)87897500</p>
				<p>採購申訴審議委員會電話：(02)87897530</p>
				<p>政府電子採購網 版權所有 © 2021</p>
			</div>
			<div class="footer_a1_2">
				<p>系統客服傳真：02-33229691</p>
				<p>工程會地址：110207臺北市信義區松仁路3號9樓</p>
				<p>中央採購稽核小組電話：(02)87897548</p>
				<p>歡迎本區第 <span><strong class="counterValue"></strong></span> 位訪客(至098/12/31累計76003935人次)</p>
			</div>
			<div class="footer_a1_3">
				<p>緊急聯絡電話：(07)2280795</p>
				<br><br>
				<p>傳真：(02)87897554</p>
			</div>
		</div>
	<div class="footer_a2">
		<a href="/pis/images/RWD_QRcode_L_1080328.jpg" class="qrcode_preview" title="手機上網領標QR Code二維條碼">
			<img src="/pis/images/qr.jpg" alt="行動版QR Code圖示" title="行動版QR Code圖示">
		</a>
	</div>
      
    </div>
    <div style="height: 30px"></div>
    <div class="footer_a">
	   	
			
			
				<div class="col-lg-2">
					<p><img src="/tps/images/link.png" alt=""><a  href="/pis/pia/client/mail" style="color: #fff" title="聯絡我們">聯絡我們</a></p>
				</div>
				<div class="col-lg-2">
					<p><img src="/tps/images/link.png" alt=""><a  href="/pis/prac/declarationClient/right" style="color: #fff" title="著作權聲明">著作權聲明</a></p>
				</div>
				<div class="col-lg-2">
					<p><img src="/tps/images/link.png" alt=""><a  href="/pis/prac/declarationClient/accessibility" style="color: #fff" title="無障礙聲明">無障礙聲明</a></p>
				</div>
				<div class="col-lg-2">
					<p><img src="/tps/images/link.png" alt=""><a  href="/pis/prac/declarationClient/security" style="color: #fff" title="安全保護聲明">安全保護聲明</a></p>
				</div>
				<div class="col-lg-2">
					<p><img src="/tps/images/link.png" alt=""><a  href="/pis/prac/declarationClient/private" style="color: #fff" title="個人隱私聲明">個人隱私聲明</a></p>
				</div>
				<div class="col-lg-2">
					<p><img src="/tps/images/link.png" alt=""><a  href="/osm/public/sysDownRcd" style="color: #fff" title="停機紀錄">停機紀錄</a></p>
				</div>
			
		
    </div>
  </div>
  <div class="footer_b">
<!--       <div class="col-lg-6"> -->
<!--         <div class="footer_text_a">政府電子採購網  版權所有 © 2020</div> -->
<!--       </div> -->
<!--       <div class="col-lg-6"> -->
<!--         <div class="footer_text_b">歡迎本區第 <span><strong class="counterValue"></strong></span> 位訪客(至098/12/31累計76003935人次)</div> -->
<!--       </div> -->
      <div class="col-lg-6">
        <div class="footer_text_a">支援瀏覽器版本為Edge，及Firefox及Chrome瀏覽器解析度1280 X 960</div>
      </div>
      <div class="col-lg-6">
        <div class="footer_text_b">台灣時間為<span id="nowTimeSpan"></span></div>
      </div>
    </div>
	  		<!--頁腳內容導盲磚--> 
  </div>
</div>

<!--<a href="#Z" class="guide" id="AZ" title="下方選單連結區，此區塊列有[聯絡我們]、[著作權聲明]、[無障礙聲明]等連結" accesskey="Z" name="Z">:::</a> -->

<!--footerEND--> 




<script type="text/javascript">
	$(document).ready(function() {
		generateNowTime();
		setInterval("generateNowTime()", 10000);
		
		// 20110714 顯示訪客人數
		var isT5Env = '';
		if(isT5Env != 'Y') {
			getCounterValue(); 
		}
	
	});

	function generateNowTime() {
		var time = new Date() ;
		var nowstr = time.getHours() + '點' + time.getMinutes() + '分';
		$("#nowTimeSpan").text(nowstr);
	}
	
	function getCounterValue(){
		jQuery.ajax({
					url: "/pis/pia/client/counter/counterValue?refresh=Y",
					type: "POST",
					dataType: "html",
					timeout:30000,
					error: function(XMLHttpRequest, textStatus, errorThrown){
						if (textStatus == 'timeout'){
							// timeout
							jQuery('strong.counterValue').text('');
						}else{
							// error
							jQuery('strong.counterValue').text('error');
						}
						ajaxEvent = false;
					},
					beforeSend: function(){
						// busy
						ajaxEvent = true;			
			   		},
			   		complete: function(){
			   			ajaxEvent = false;
			   		},
					success: function(doc){
						if(doc.toLowerCase){
							if(doc.toLowerCase().indexOf("</body>")!=-1){			   				
								window.location.href="/pis";
							}else{
								jQuery('strong.counterValue').text(doc);
							}
						}					
					}
				});
	}
	
	function refreshNewCount(){

		jQuery.ajax({
					url: "/pis/pia/client/counter/refreshNewCount?refresh=Y",
					type: "POST",
					dataType: "html",
					timeout:30000,
					error: function(XMLHttpRequest, textStatus, errorThrown){
						if (textStatus == 'timeout'){
							// timeout
							jQuery('strong.counterValue').text('');
						}else{
							// error
							jQuery('strong.counterValue').text('error');
						}
						ajaxEvent = false;
					},
					beforeSend: function(){
						// busy
						ajaxEvent = true;			
			   		},
			   		complete: function(){
			   			ajaxEvent = false;
			   		},
					success: function(doc){
						ajaxEvent = false;			
					}
				});
	}
	
	function goTender(){
		refreshNewCount();
		window.location.href = "/prkms/tender/common/basic/indexTenderBasic";
	}
	
</script>
		
	
<!--footerEND--> 

<!--首頁下拉選單的JS--> 
<script>
$(".selemenu").click(function(){
	$(this).next().slideToggle();
	$(this).parents().siblings().find(".citylist,.citylist2").slideUp();
	})


	$(function() {
		//設定TITLE
		var loginBreadcrumbsArr = $("#loginBreadcrumbs").text().split('>');

		if (loginBreadcrumbsArr.length >= 1) {

			document.title = '政府電子採購網-' + loginBreadcrumbsArr[loginBreadcrumbsArr.length - 1].trim();

			if (loginBreadcrumbsArr[loginBreadcrumbsArr.length - 1].trim() == '') {
				var lnetTrackArr = $("#netTrack").text().split('>');
				console.log('netTrack' + $("#netTrack").text());

				document.title = '政府電子採購網-' + lnetTrackArr[lnetTrackArr.length - 1].trim();
			}

		}
	});
</script>

</body>
</html>