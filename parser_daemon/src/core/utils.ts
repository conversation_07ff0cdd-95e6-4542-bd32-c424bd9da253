// 通用工具函数

// 生成唯一ID
export function generateId(prefix: string = "tender"): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `${prefix}_${timestamp}_${random}`;
}

// 延迟函数
export function delay(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

// 安全的JSON解析
export function safeJsonParse<T>(jsonString: string, fallback: T): T {
  try {
    return JSON.parse(jsonString);
  } catch {
    return fallback;
  }
}

// 格式化文件大小
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 B";

  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

// 格式化处理时间
export function formatDuration(ms: number): string {
  if (ms < 1000) return `${ms}ms`;
  if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
  if (ms < 3600000) return `${(ms / 60000).toFixed(1)}m`;
  return `${(ms / 3600000).toFixed(1)}h`;
}

// 计算成功率
export function calculateSuccessRate(success: number, total: number): string {
  if (total === 0) return "0%";
  return `${((success / total) * 100).toFixed(1)}%`;
}

// URL 相关工具
export class UrlUtils {
  // 从标案URL提取ID
  static extractTenderIdFromUrl(url: string): string | null {
    // 例如: https://web.pcc.gov.tw/tps/main/pms/tps/atm/atmAwardAction.do?newEdit=false&searchMode=common&searchType=basic&searchTarget=ATM&orgName=&orgId=&hid_1=&tenderName=&tenderStatus=&tenderId=&radioTenderStatus=&tenderStartDateStr=&tenderEndDateStr=&radioTenderSpecial=&bidWay=&awdNo=108-12345
    const patterns = [
      /awdNo=([^&]+)/,
      /tenderCaseNo=([^&]+)/,
      /pk=([^&]+)/,
      /id=([^&]+)/,
    ];

    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match) return match[1];
    }

    return null;
  }

  // 标准化URL
  static normalizeUrl(url: string): string {
    if (!url.startsWith("http")) {
      url = "https://web.pcc.gov.tw" + (url.startsWith("/") ? "" : "/") + url;
    }
    return url;
  }
}

// 文本处理工具
export class TextUtils {
  // 提取金额数字
  static extractAmount(text: string): number {
    // 移除常见的金额分隔符和单位
    const cleanText = text
      .replace(/[,，]/g, "")
      .replace(/元|萬|万|千|百/g, "")
      .replace(/\s/g, "");

    const match = cleanText.match(/(\d+(?:\.\d+)?)/);
    return match ? parseFloat(match[1]) : 0;
  }

  // 标准化机关名称
  static normalizeAgencyName(name: string): string {
    return name
      .replace(/^\s*\d+\.\s*/, "") // 移除开头的数字序号
      .replace(/\s*\([^)]*\)\s*$/g, "") // 移除结尾的括号内容
      .trim();
  }

  // 提取联系方式
  static extractPhone(text: string): string {
    const phonePattern =
      /(?:\(?\d{2,4}\)?\s*[-\s]?\d{3,4}[-\s]?\d{4}|\d{10,11})/;
    const match = text.match(phonePattern);
    return match ? match[0].trim() : "";
  }

  static extractEmail(text: string): string {
    const emailPattern = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/;
    const match = text.match(emailPattern);
    return match ? match[0].trim() : "";
  }
}

// 日志工具
export class Logger {
  private static verbose = false;

  static setVerbose(verbose: boolean) {
    this.verbose = verbose;
  }

  static info(message: string) {
    console.log(`[INFO] ${new Date().toISOString()} - ${message}`);
  }

  static warn(message: string) {
    console.warn(`[WARN] ${new Date().toISOString()} - ${message}`);
  }

  static error(message: string) {
    console.error(`[ERROR] ${new Date().toISOString()} - ${message}`);
  }

  static debug(message: string) {
    if (this.verbose) {
      console.log(`[DEBUG] ${new Date().toISOString()} - ${message}`);
    }
  }
}
