import { TenderData } from "../../../shared/types";

// Temporary interfaces for compatibility
interface ListItem {
  id: string;
  url: string;
  title: string;
  publishDate: string;
  agency: string;
}

interface ParseResult<T> {
  success: boolean;
  data?: T;
  errors?: string[];
  processed: number;
  skipped: number;
}

// 网站解析器接口
export interface SiteParser {
  name: string;
  parseList(html: string, filename: string): Promise<ListItem[]>;
  parseDetail(html: string, url: string): Promise<TenderData>;
  validateListHtml(html: string): boolean;
  validateDetailHtml(html: string): boolean;
}

// 基础解析器抽象类
export abstract class BaseParser implements SiteParser {
  abstract name: string;

  abstract parseList(html: string, filename: string): Promise<ListItem[]>;
  abstract parseDetail(html: string, url: string): Promise<TenderData>;

  // 默认HTML验证逻辑
  validateListHtml(html: string): boolean {
    return (
      html.length > 100 &&
      !html.includes("驗證碼") &&
      !html.includes("系統錯誤")
    );
  }

  validateDetailHtml(html: string): boolean {
    return (
      html.length > 500 &&
      !html.includes("驗證碼") &&
      !html.includes("系統錯誤") &&
      !html.includes("查無此標案")
    );
  }

  // 通用错误处理
  protected handleError(error: any, context: string): never {
    const message = `${this.name} Parser Error in ${context}: ${
      error.message || error
    }`;
    console.error(message);
    throw new Error(message);
  }

  // 通用文本清理
  protected cleanText(text: string): string {
    return text
      .replace(/\s+/g, " ")
      .replace(/&nbsp;/g, " ")
      .replace(/&amp;/g, "&")
      .replace(/&lt;/g, "<")
      .replace(/&gt;/g, ">")
      .replace(/&quot;/g, '"')
      .trim();
  }

  // 数字提取
  protected extractNumber(text: string): number {
    const match = text.replace(/[,，]/g, "").match(/\d+/);
    return match ? parseInt(match[0]) : 0;
  }

  // 日期标准化 (处理民国历)
  protected normalizeDate(dateStr: string): string {
    try {
      // 处理民国历格式 "114/6/24" -> "2025/6/24"
      const rocMatch = dateStr.match(/(\d{2,3})\/(\d{1,2})\/(\d{1,2})/);
      if (rocMatch) {
        const rocYear = parseInt(rocMatch[1]);
        const month = rocMatch[2].padStart(2, "0");
        const day = rocMatch[3].padStart(2, "0");
        const westernYear = rocYear + 1911;
        return `${westernYear}-${month}-${day}`;
      }

      // 处理其他格式
      return dateStr;
    } catch (error) {
      console.warn(`Date normalization failed for: ${dateStr}`);
      return dateStr;
    }
  }
}
