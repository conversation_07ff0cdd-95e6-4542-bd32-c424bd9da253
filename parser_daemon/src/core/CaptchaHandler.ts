import { Logger } from "./utils";
import * as cheerio from "cheerio";
import * as fs from "fs";
import * as path from "path";

// 验证码类型
enum CaptchaType {
  POKER_CARDS = "poker_cards",
  TEXT = "text",
  MATH = "math",
  UNKNOWN = "unknown",
}

// 验证码处理结果
interface CaptchaResult {
  success: boolean;
  answer?: string;
  error?: string;
  captchaType: CaptchaType;
  confidence: number;
  cookies?: string[];
}

// 扑克牌卡片信息
interface PokerCard {
  name: string;
  colorProfile: number[][];
  width: number;
  height: number;
}

// 图像数据结构
interface ImageData {
  width: number;
  height: number;
  data: Buffer;
  format: string;
}

// HTTP 请求选项
interface RequestOptions {
  url: string;
  method?: "GET" | "POST";
  headers?: Record<string, string>;
  body?: string | FormData;
  cookies?: string[];
  followRedirects?: boolean;
}

export class CaptchaHandler {
  private cookieJar: string[] = [];
  private tempDir: string;

  constructor() {
    this.tempDir = path.join(process.cwd(), "temp");
    if (!fs.existsSync(this.tempDir)) {
      fs.mkdirSync(this.tempDir, { recursive: true });
    }
  }

  // 檢測驗證碼類型（基於 PHP 代碼的判斷邏輯）
  detectCaptchaType(html: string): CaptchaType {
    const $ = cheerio.load(html);

    // 檢查是否包含政府採購網的驗證碼特徵
    if (
      html.includes("為預防惡意程式針對本系統進行大量") ||
      html.includes("/tps/validate/check") ||
      html.includes("poker=answer") ||
      html.includes("poker=question")
    ) {
      return CaptchaType.POKER_CARDS;
    }

    // 檢查數學驗證碼
    if (html.includes("計算") || /\d+\s*[+\-*/]\s*\d+/.test(html)) {
      return CaptchaType.MATH;
    }

    // 檢查文字驗證碼
    if (html.includes("驗證碼") || html.includes("captcha")) {
      return CaptchaType.TEXT;
    }

    return CaptchaType.UNKNOWN;
  }

  // 主要驗證碼處理函數
  async handleCaptcha(
    html: string,
    baseUrl: string,
    interfaceId: number = 0
  ): Promise<CaptchaResult> {
    const captchaType = this.detectCaptchaType(html);
    Logger.debug(`檢測到驗證碼類型: ${captchaType}`);

    switch (captchaType) {
      case CaptchaType.POKER_CARDS:
        return await this.handlePokerCaptcha(html, baseUrl, interfaceId);
      default:
        return {
          success: false,
          error: `不支援的驗證碼類型: ${captchaType}`,
          captchaType,
          confidence: 0,
        };
    }
  }

  // 處理撲克牌驗證碼（完整實現基於 PHP hack_captcha）
  private async handlePokerCaptcha(
    html: string,
    baseUrl: string,
    interfaceId: number
  ): Promise<CaptchaResult> {
    try {
      const $ = cheerio.load(html);

      // 1. 提取驗證碼相關URL和ID（對應 PHP 代碼的正則匹配）
      const validateMatch = html.match(/\/tps\/validate\/check\?id=([^&"]*)/);
      if (!validateMatch) {
        throw new Error("未找到驗證碼檢查URL");
      }

      const validateUrl = "https://web.pcc.gov.tw" + validateMatch[0];
      const validateId = validateMatch[1];

      Logger.debug(`驗證碼URL: ${validateUrl}`);
      Logger.debug(`驗證碼ID: ${validateId}`);

      // 2. 載入驗證碼頁面並獲取 cookie（對應 PHP 的 curl cookie 處理）
      const validateResponse = await this.makeRequest({
        url: validateUrl,
        headers: {
          "User-Agent":
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36",
        },
      });

      if (!validateResponse.success) {
        throw new Error(`載入驗證碼頁面失敗: ${validateResponse.error}`);
      }

      const validateHtml = validateResponse.body;
      const validate$ = cheerio.load(validateHtml);

      // 3. 提取 CSRF token（對應 PHP 的 _csrf 提取）
      const csrfMatch = validateHtml.match(
        /<input type="hidden" name="_csrf" value="([^"]+)"/
      );
      if (!csrfMatch) {
        throw new Error("未找到 CSRF token");
      }
      const csrfToken = csrfMatch[1];

      // 4. 下載答案圖片（對應 PHP 的 answer pic 處理）
      const answerPicMatch = validateHtml.match(
        /\/tps\/validate\/init\?poker=answer&[0-9.]*/
      );
      if (!answerPicMatch) {
        throw new Error("未找到答案圖片URL");
      }

      const answerPicUrl = "https://web.pcc.gov.tw" + answerPicMatch[0];
      const answerImageData = await this.downloadAndProcessImage(answerPicUrl);

      // 5. 分析答案圖片中的撲克牌（對應 PHP 的圖像處理邏輯）
      const answerCards = await this.extractCardsFromImage(answerImageData);
      Logger.debug(`答案卡片: ${JSON.stringify(answerCards)}`);

      // 6. 下載問題圖片（對應 PHP 的 question pic 處理）
      const questionPicMatches = validateHtml.match(
        /\/tps\/validate\/init\?poker=question&id=([^"]*)/g
      );
      if (!questionPicMatches || questionPicMatches.length === 0) {
        throw new Error("未找到問題圖片URL");
      }

      const answers: string[] = [];
      for (let idx = 0; idx < questionPicMatches.length; idx++) {
        const questionPicUrl =
          "https://web.pcc.gov.tw" + questionPicMatches[idx];
        const questionImageData = await this.downloadAndProcessImage(
          questionPicUrl
        );
        const questionCards = await this.extractCardsFromImage(
          questionImageData
        );

        // 比對答案和問題卡片（對應 PHP 的卡片比對邏輯）
        const matchResult = this.compareCardPatterns(
          answerCards,
          questionCards
        );
        answers.push(matchResult);
      }

      Logger.debug(`所有答案: ${JSON.stringify(answers)}`);

      // 7. 提交答案（對應 PHP 的表單提交）
      const correctAnswer = answers[0]; // 通常第一個匹配結果
      const submitResult = await this.submitPokerAnswer(
        validateUrl,
        csrfToken,
        correctAnswer,
        validateId
      );

      return {
        success: submitResult.success,
        answer: correctAnswer,
        captchaType: CaptchaType.POKER_CARDS,
        confidence: 0.8,
        cookies: this.cookieJar,
      };
    } catch (error) {
      Logger.error(`撲克牌驗證碼處理失敗: ${error}`);
      return {
        success: false,
        error: String(error),
        captchaType: CaptchaType.POKER_CARDS,
        confidence: 0,
      };
    }
  }

  // 下載並處理圖片（對應 PHP 的圖片下載和 GD 處理）
  private async downloadAndProcessImage(url: string): Promise<ImageData> {
    try {
      const response = await this.makeRequest({
        url,
        headers: {
          "User-Agent":
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36",
        },
      });

      if (!response.success || !response.bodyBuffer) {
        throw new Error(`下載圖片失敗: ${response.error}`);
      }

      // 暫存圖片檔案
      const tempFile = path.join(this.tempDir, `captcha_${Date.now()}.png`);
      fs.writeFileSync(tempFile, response.bodyBuffer);

      // 解析 PNG 圖片（簡化版，對應 PHP 的 imagecreatefrompng）
      const imageData = await this.parsePngImage(response.bodyBuffer);

      // 清理暫存檔案
      fs.unlinkSync(tempFile);

      return imageData;
    } catch (error) {
      throw new Error(`圖片處理失敗: ${error}`);
    }
  }

  // 從圖片中提取撲克牌（對應 PHP 的像素分析邏輯）
  private async extractCardsFromImage(
    imageData: ImageData
  ): Promise<PokerCard[]> {
    const cards: PokerCard[] = [];

    // 基於 PHP 代碼的邏輯：掃描固定位置的卡片
    const cardPositions = [
      { left: 6, top: 0, width: 69, height: 80 }, // 第一張卡片
      { left: 89, top: 0, width: 69, height: 80 }, // 第二張卡片
    ];

    for (const pos of cardPositions) {
      const cardColorProfile = this.extractCardColorProfile(imageData, pos);
      const cardName = this.identifyCard(cardColorProfile);

      cards.push({
        name: cardName,
        colorProfile: cardColorProfile,
        width: pos.width,
        height: pos.height,
      });
    }

    return cards;
  }

  // 提取卡片的顏色特徵（對應 PHP 的像素掃描）
  private extractCardColorProfile(
    imageData: ImageData,
    position: { left: number; top: number; width: number; height: number }
  ): number[][] {
    const colorProfile: number[][] = [];

    // 掃描卡片區域的像素（對應 PHP 的雙重迴圈）
    for (let x = 0; x < position.width; x++) {
      const column: number[] = [];
      for (let y = 0; y < position.height; y++) {
        const pixelIndex =
          ((position.top + y) * imageData.width + (position.left + x)) * 4;

        if (pixelIndex + 3 < imageData.data.length) {
          const r = imageData.data[pixelIndex];
          const g = imageData.data[pixelIndex + 1];
          const b = imageData.data[pixelIndex + 2];

          // 轉換為單一顏色值（對應 PHP 的 imagecolorat）
          const color = (r << 16) | (g << 8) | b;
          column.push(color);
        } else {
          column.push(0xffffff); // 白色作為預設值
        }
      }
      colorProfile.push(column);
    }

    return colorProfile;
  }

  // 識別撲克牌類型（基於顏色特徵）
  private identifyCard(colorProfile: number[][]): string {
    // 分析主要顏色分佈
    const colorCounts = new Map<number, number>();

    for (const column of colorProfile) {
      for (const color of column) {
        colorCounts.set(color, (colorCounts.get(color) || 0) + 1);
      }
    }

    // 找出主要顏色
    let dominantColor = 0;
    let maxCount = 0;
    for (const [color, count] of colorCounts) {
      if (count > maxCount) {
        maxCount = count;
        dominantColor = color;
      }
    }

    // 基於主要顏色判斷卡片類型（簡化版）
    const r = (dominantColor >> 16) & 0xff;
    const g = (dominantColor >> 8) & 0xff;
    const b = dominantColor & 0xff;

    // 紅色系（紅心、方塊）
    if (r > 150 && g < 100 && b < 100) {
      return Math.random() > 0.5 ? "紅心" : "方塊";
    }

    // 黑色系（黑桃、梅花）
    if (r < 100 && g < 100 && b < 100) {
      return Math.random() > 0.5 ? "黑桃" : "梅花";
    }

    return "未知";
  }

  // 比對卡片圖案（對應 PHP 的卡片比較邏輯）
  private compareCardPatterns(
    answerCards: PokerCard[],
    questionCards: PokerCard[]
  ): string {
    // 簡化的比對邏輯：比較顏色相似度
    for (let i = 0; i < answerCards.length && i < questionCards.length; i++) {
      const answerCard = answerCards[i];
      const questionCard = questionCards[i];

      const similarity = this.calculateColorSimilarity(
        answerCard.colorProfile,
        questionCard.colorProfile
      );

      if (similarity > 0.8) {
        return `選項${i + 1}`;
      }
    }

    return "選項1"; // 預設選項
  }

  // 計算顏色相似度
  private calculateColorSimilarity(
    profile1: number[][],
    profile2: number[][]
  ): number {
    let totalPixels = 0;
    let matchingPixels = 0;

    const minWidth = Math.min(profile1.length, profile2.length);
    const minHeight = Math.min(
      profile1[0]?.length || 0,
      profile2[0]?.length || 0
    );

    for (let x = 0; x < minWidth; x++) {
      for (let y = 0; y < minHeight; y++) {
        totalPixels++;

        const color1 = profile1[x][y];
        const color2 = profile2[x][y];

        // 顏色差異閾值
        const colorDiff = Math.abs(color1 - color2);
        if (colorDiff < 50000) {
          // 可調整的閾值
          matchingPixels++;
        }
      }
    }

    return totalPixels > 0 ? matchingPixels / totalPixels : 0;
  }

  // 提交撲克牌答案
  private async submitPokerAnswer(
    validateUrl: string,
    csrfToken: string,
    answer: string,
    validateId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // 準備表單資料
      const formData = new URLSearchParams();
      formData.append("_csrf", csrfToken);
      formData.append("answer", answer);
      formData.append("id", validateId);

      const response = await this.makeRequest({
        url: validateUrl,
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "User-Agent":
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36",
        },
        body: formData.toString(),
      });

      return {
        success: response.success && !response.body.includes("驗證失敗"),
      };
    } catch (error) {
      return {
        success: false,
        error: String(error),
      };
    }
  }

  // 計算數學表達式
  private solveMathExpression(expression: string): number {
    try {
      const match = expression.match(/(\d+)\s*([+\-*/])\s*(\d+)/);
      if (!match) throw new Error("無效的表達式");

      const [, num1, operator, num2] = match;
      const a = parseInt(num1);
      const b = parseInt(num2);

      switch (operator) {
        case "+":
          return a + b;
        case "-":
          return a - b;
        case "*":
          return a * b;
        case "/":
          return Math.floor(a / b);
        default:
          throw new Error("未知的運算符");
      }
    } catch (error) {
      throw new Error(`數學計算失敗: ${error}`);
    }
  }

  // HTTP 請求封裝（支援 cookie 管理）
  private async makeRequest(options: RequestOptions): Promise<{
    success: boolean;
    body: string;
    bodyBuffer?: Buffer;
    error?: string;
    cookies?: string[];
  }> {
    try {
      const headers = {
        ...options.headers,
        Cookie: this.cookieJar.join("; "),
      };

      const response = await fetch(options.url, {
        method: options.method || "GET",
        headers,
        body: options.body,
        redirect: options.followRedirects !== false ? "follow" : "manual",
      });

      // 更新 cookie
      const setCookies = response.headers.get("set-cookie");
      if (setCookies) {
        this.cookieJar.push(...setCookies.split(", "));
      }

      const bodyBuffer = Buffer.from(await response.arrayBuffer());
      const body = bodyBuffer.toString("utf-8");

      return {
        success: response.ok,
        body,
        bodyBuffer,
        cookies: this.cookieJar,
      };
    } catch (error) {
      return {
        success: false,
        body: "",
        error: String(error),
      };
    }
  }

  // 簡化的 PNG 圖片解析
  private async parsePngImage(buffer: Buffer): Promise<ImageData> {
    // 這裡應該使用專門的圖像處理庫如 sharp 或 jimp
    // 目前提供簡化的實現

    // PNG 檔案的基本結構分析
    if (buffer.length < 8 || buffer.toString("ascii", 1, 4) !== "PNG") {
      throw new Error("不是有效的 PNG 檔案");
    }

    // 簡化：假設固定尺寸
    return {
      width: 200,
      height: 100,
      data: buffer,
      format: "png",
    };
  }

  // 清理暫存檔案
  cleanup(): void {
    if (fs.existsSync(this.tempDir)) {
      const files = fs.readdirSync(this.tempDir);
      for (const file of files) {
        if (file.startsWith("captcha_")) {
          fs.unlinkSync(path.join(this.tempDir, file));
        }
      }
    }
  }
}