import { readFile, writeFile, readdir, stat, mkdir } from "fs/promises";
import { gunzip } from "zlib";
import { promisify } from "util";
import { join, extname, basename } from "path";

const gunzipAsync = promisify(gunzip);

export class FileManager {
  // 读取并解压 .gz 文件
  static async readGzipFile(filePath: string): Promise<string> {
    try {
      const compressed = await readFile(filePath);
      const decompressed = await gunzipAsync(compressed);
      return decompressed.toString("utf-8");
    } catch (error) {
      throw new Error(`Failed to read gzip file ${filePath}: ${error}`);
    }
  }

  // 读取普通文本文件
  static async readTextFile(filePath: string): Promise<string> {
    try {
      return await readFile(filePath, "utf-8");
    } catch (error) {
      throw new Error(`Failed to read text file ${filePath}: ${error}`);
    }
  }

  // 智能读取文件（自动判断是否为压缩文件）
  static async readFile(filePath: string): Promise<string> {
    if (filePath.endsWith(".gz")) {
      return this.readGzipFile(filePath);
    } else {
      return this.readTextFile(filePath);
    }
  }

  // 写入 JSON 文件
  static async writeJsonFile(filePath: string, data: any): Promise<void> {
    try {
      await this.ensureDir(filePath);
      const jsonString = JSON.stringify(data, null, 2);
      await writeFile(filePath, jsonString, "utf-8");
    } catch (error) {
      throw new Error(`Failed to write JSON file ${filePath}: ${error}`);
    }
  }

  // 确保目录存在
  static async ensureDir(filePath: string): Promise<void> {
    const dir = filePath.substring(0, filePath.lastIndexOf("/"));
    if (dir) {
      try {
        await mkdir(dir, { recursive: true });
      } catch (error) {
        // 目录可能已存在，忽略错误
      }
    }
  }

  // 获取目录下的所有文件
  static async getFiles(
    dirPath: string,
    extension?: string
  ): Promise<string[]> {
    try {
      const files = await readdir(dirPath);
      return files
        .filter((file) => !extension || file.endsWith(extension))
        .map((file) => join(dirPath, file));
    } catch (error) {
      throw new Error(`Failed to read directory ${dirPath}: ${error}`);
    }
  }

  // 获取文件统计信息
  static async getFileStats(filePath: string) {
    try {
      return await stat(filePath);
    } catch (error) {
      throw new Error(`Failed to get file stats ${filePath}: ${error}`);
    }
  }

  // 检查文件是否存在
  static async fileExists(filePath: string): Promise<boolean> {
    try {
      await stat(filePath);
      return true;
    } catch {
      return false;
    }
  }

  // 从文件名提取日期 (例如: "20240101.html.gz" -> "2024-01-01" 或 "114-06-26.json" -> "2025-06-26")
  static extractDateFromFilename(filename: string): string | null {
    // 西元年格式: YYYYMMDD
    const westernMatch = basename(filename).match(/(\d{4})(\d{2})(\d{2})/);
    if (westernMatch) {
      return `${westernMatch[1]}-${westernMatch[2]}-${westernMatch[3]}`;
    }

    // 民國年格式: YYY-MM-DD
    const rocMatch = basename(filename).match(/(\d{3})-(\d{2})-(\d{2})/);
    if (rocMatch) {
      const westernYear = parseInt(rocMatch[1]) + 1911; // 民國年轉西元年
      return `${westernYear}-${rocMatch[2]}-${rocMatch[3]}`;
    }

    return null;
  }

  // 根据日期范围过滤文件
  static filterFilesByDateRange(
    files: string[],
    fromDate?: string,
    toDate?: string
  ): string[] {
    if (!fromDate && !toDate) return files;

    return files.filter((file) => {
      const fileDate = this.extractDateFromFilename(file);
      if (!fileDate) return false;

      if (fromDate && fileDate < fromDate) return false;
      if (toDate && fileDate > toDate) return false;

      return true;
    });
  }
}
