import { Logger } from "./utils";
import * as https from "https";
import * as http from "http";
import { URL } from "url";

// 网络接口管理
interface NetworkInterface {
  id: number;
  isAvailable: boolean;
  lastFailedAt?: number;
  failureCount: number;
}

// 请求配置
interface RequestConfig {
  url: string;
  method?: "GET" | "POST";
  headers?: Record<string, string>;
  body?: string;
  timeout?: number;
  retries?: number;
  useProxy?: boolean;
}

// 响应结果
interface RequestResult {
  success: boolean;
  data?: string;
  error?: string;
  statusCode?: number;
  fromCache?: boolean;
  interfaceId?: number;
  hasCaptcha?: boolean;
}

export class NetworkManager {
  private interfaces: NetworkInterface[] = [];
  private currentInterfaceIndex = 0;
  private skipInterfaces: Record<number, number> = {}; // interfaceId -> timestamp
  private waitTime = 3; // 等待时间（秒）- 測試環境使用較短時間
  private proxySecret = "";
  private proxyUrl = "";
  private lastWarnTime = 0; // 上次警告時間，用於抑制重複警告
  private warnCooldown = 30; // 警告冷卻時間（秒）- 增加到30秒減少警告頻率
  private interfaceRecoveryTime = 60; // 接口恢復時間（秒）- 60秒後重新嘗試不可用的接口

  // 常用的 User-Agent 池
  private userAgents = [
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/59.0.3071.86 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
  ];

  constructor(interfaceCount: number = 1) {
    this.initializeInterfaces(interfaceCount);
    this.loadProxyConfig();
  }

  // 初始化网络接口
  private initializeInterfaces(count: number) {
    for (let i = 0; i < count; i++) {
      this.interfaces.push({
        id: i,
        isAvailable: true,
        failureCount: 0,
      });
    }
    Logger.info(`Initialized ${count} network interfaces`);
  }

  // 加载代理配置
  private loadProxyConfig() {
    this.proxySecret = process.env.PROXY_SECRET || "";
    this.proxyUrl = process.env.PROXY_URL || "";

    if (this.proxyUrl) {
      Logger.info(`Proxy configured: ${this.proxyUrl}`);
    }
  }

  // 获取下一个可用的网络接口
  private getNextAvailableInterface(): NetworkInterface | null {
    const now = Date.now() / 1000;

    // 首先嘗試恢復長時間不可用的接口
    this.tryRecoverInterfaces(now);

    // 如果所有接口都不可用，嘗試強制恢復最舊的接口
    if (Object.keys(this.skipInterfaces).length === this.interfaces.length) {
      const recoveredInterface = this.forceRecoverOldestInterface();
      if (recoveredInterface) {
        Logger.info(`Force recovered interface ${recoveredInterface.id} due to all interfaces unavailable`);
        return recoveredInterface;
      }

      // 如果所有接口長時間不可用，執行緊急重置
      const oldestSkipTime = Math.min(...Object.values(this.skipInterfaces));
      if (now - oldestSkipTime > this.interfaceRecoveryTime * 3) {
        this.resetAllInterfaces();
        return this.interfaces[0]; // 返回第一個接口
      }

      // 只有在冷卻時間過後才發出警告，避免重複警告
      if (now - this.lastWarnTime > this.warnCooldown) {
        Logger.warn(
          `All interfaces unavailable, waiting ${this.waitTime} seconds`
        );
        this.lastWarnTime = now;
      }
      return null;
    }

    // 轮转寻找可用接口
    for (let i = 0; i < this.interfaces.length; i++) {
      this.currentInterfaceIndex =
        (this.currentInterfaceIndex + 1) % this.interfaces.length;
      const interfaceId = this.currentInterfaceIndex;

      // 检查接口是否被跳过，以及是否已经过了等待时间
      if (
        !this.skipInterfaces[interfaceId] ||
        this.skipInterfaces[interfaceId] < now - this.waitTime
      ) {
        delete this.skipInterfaces[interfaceId];
        return this.interfaces[interfaceId];
      }
    }

    return null;
  }

  // 嘗試恢復長時間不可用的接口
  private tryRecoverInterfaces(now: number) {
    const recoveryThreshold = now - this.interfaceRecoveryTime;
    let recoveredCount = 0;

    for (const interfaceIdStr in this.skipInterfaces) {
      const interfaceId = parseInt(interfaceIdStr);
      const skipTime = this.skipInterfaces[interfaceId];

      // 如果接口被跳過的時間超過恢復閾值，則恢復該接口
      if (skipTime < recoveryThreshold) {
        delete this.skipInterfaces[interfaceId];
        this.interfaces[interfaceId].isAvailable = true;
        // 重置失敗計數，給接口一個新的機會
        this.interfaces[interfaceId].failureCount = Math.max(0, this.interfaces[interfaceId].failureCount - 1);
        recoveredCount++;
        Logger.info(`Interface ${interfaceId} recovered after ${this.interfaceRecoveryTime} seconds`);
      }
    }

    if (recoveredCount > 0) {
      Logger.info(`Recovered ${recoveredCount} interfaces`);
    }
  }

  // 強制恢復最舊的接口（當所有接口都不可用時）
  private forceRecoverOldestInterface(): NetworkInterface | null {
    if (Object.keys(this.skipInterfaces).length === 0) {
      return null;
    }

    // 找到最舊的被跳過的接口
    let oldestInterfaceId = -1;
    let oldestTime = Infinity;

    for (const interfaceIdStr in this.skipInterfaces) {
      const interfaceId = parseInt(interfaceIdStr);
      const skipTime = this.skipInterfaces[interfaceId];

      if (skipTime < oldestTime) {
        oldestTime = skipTime;
        oldestInterfaceId = interfaceId;
      }
    }

    if (oldestInterfaceId >= 0) {
      delete this.skipInterfaces[oldestInterfaceId];
      this.interfaces[oldestInterfaceId].isAvailable = true;
      // 重置失敗計數，給接口一個新的機會
      this.interfaces[oldestInterfaceId].failureCount = 0;
      return this.interfaces[oldestInterfaceId];
    }

    return null;
  }

  // 重置所有接口（緊急恢復機制）
  private resetAllInterfaces() {
    Logger.warn("Resetting all interfaces due to prolonged unavailability");
    this.skipInterfaces = {};
    for (const iface of this.interfaces) {
      iface.isAvailable = true;
      iface.failureCount = 0;
      iface.lastFailedAt = undefined;
    }
  }

  // 标记接口为不可用
  private markInterfaceUnavailable(interfaceId: number) {
    this.skipInterfaces[interfaceId] = Date.now() / 1000;
    this.interfaces[interfaceId].lastFailedAt = Date.now();
    this.interfaces[interfaceId].failureCount++;
    this.interfaces[interfaceId].isAvailable = false;

    // 只在第一次標記時發出警告，避免重複警告
    if (this.interfaces[interfaceId].failureCount === 1) {
      Logger.warn(`Interface ${interfaceId} marked as unavailable`);
    }
  }

  // 构建代理URL
  private buildProxyUrl(originalUrl: string, interfaceId?: number): string {
    if (!this.proxyUrl || !this.proxySecret) {
      return originalUrl;
    }

    const signature = this.generateSignature(originalUrl);
    let proxyUrl = `${this.proxyUrl}?url=${encodeURIComponent(
      originalUrl
    )}&sig=${signature}`;

    if (interfaceId !== undefined) {
      proxyUrl += `&if_id=${interfaceId}`;
    }

    return proxyUrl;
  }

  // 生成代理签名
  private generateSignature(url: string): string {
    // 简化的签名生成，实际应该使用 crypto
    const crypto = require("crypto");
    return crypto
      .createHash("md5")
      .update(this.proxySecret + url)
      .digest("hex");
  }

  // 获取随机 User-Agent
  private getRandomUserAgent(): string {
    return this.userAgents[Math.floor(Math.random() * this.userAgents.length)];
  }

  // 检测是否为验证码页面
  private isCaptchaPage(content: string): boolean {
    const captchaIndicators = [
      "為預防惡意程式針對本系統進行大量",
      "Web Page Blocked",
      "驗證碼",
      "captcha",
      "Please verify",
    ];

    return captchaIndicators.some((indicator) => content.includes(indicator));
  }

  // 检测是否为错误页面
  private isErrorPage(content: string): boolean {
    const errorIndicators = [
      "政府電子採購網_失敗訊息畫面",
      "500 Internal Server Error",
      "Internal Server Error",
      "502 Proxy Error",
      "您尚未登入或已被登出本系統",
      // 更精確的錯誤檢測 - 避免誤判正常頁面模板
      "發生錯誤，請重新操作",
      "系統發生錯誤",
      "操作失敗",
    ];

    // 檢查是否真的是錯誤頁面，而不是包含錯誤訊息模板的正常頁面
    const hasErrorIndicator = errorIndicators.some((indicator) => content.includes(indicator));

    // 如果包含正常頁面的標識，則不是錯誤頁面
    const normalPageIndicators = [
      "政府電子採購網",
      "每日決標資料查詢",
      "標案資料",
      "機關資料",
      "採購資料",
    ];

    const hasNormalIndicator = normalPageIndicators.some((indicator) => content.includes(indicator));

    // 只有在有錯誤指示符且沒有正常頁面指示符時才判定為錯誤頁面
    return hasErrorIndicator && !hasNormalIndicator;
  }

  // 检测页面完整性
  private isValidPage(content: string): boolean {
    if (content.length < 100) return false;
    if (this.isErrorPage(content)) return false;

    // 验证码页面虽然需要特殊处理，但仍然是有效的响应
    // 只要不是错误页面就认为是有效的
    return true;
  }

  // 处理验证码（基础实现）
  private async handleCaptcha(
    content: string,
    interfaceId: number
  ): Promise<string | null> {
    Logger.warn(`Captcha detected on interface ${interfaceId}`);

    // 标记当前接口不可用
    this.markInterfaceUnavailable(interfaceId);

    // 基础的验证码处理策略：
    // 1. 等待一段时间
    // 2. 尝试使用其他接口
    // 实际应用中可以实现图像识别等高级功能

    await this.delay(5000); // 等待5秒

    return null; // 返回null表示需要重试
  }

  // 延迟函数
  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  // 使用 Node.js 內建模組的 HTTP 請求 (fetch 的備用方案)
  private async nodeHttpRequest(
    url: string,
    options: {
      method?: string;
      headers?: Record<string, string>;
      body?: string;
      timeout?: number;
    }
  ): Promise<{ ok: boolean; status: number; statusText: string; text: () => Promise<string> }> {
    return new Promise((resolve, reject) => {
      const parsedUrl = new URL(url);
      const isHttps = parsedUrl.protocol === "https:";
      const client = isHttps ? https : http;

      const requestOptions = {
        hostname: parsedUrl.hostname,
        port: parsedUrl.port || (isHttps ? 443 : 80),
        path: parsedUrl.pathname + parsedUrl.search,
        method: options.method || "GET",
        headers: options.headers || {},
        timeout: options.timeout || 30000,
      };

      const req = client.request(requestOptions, (res) => {
        let data = "";

        res.on("data", (chunk) => {
          data += chunk;
        });

        res.on("end", () => {
          resolve({
            ok: res.statusCode! >= 200 && res.statusCode! < 300,
            status: res.statusCode!,
            statusText: res.statusMessage || "OK",
            text: async () => data,
          });
        });
      });

      req.on("error", (error) => {
        reject(error);
      });

      req.on("timeout", () => {
        req.destroy();
        reject(new Error("Request timeout"));
      });

      if (options.body) {
        req.write(options.body);
      }

      req.end();
    });
  }

  // 执行HTTP请求
  async request(config: RequestConfig): Promise<RequestResult> {
    const {
      url,
      method = "GET",
      headers = {},
      body,
      timeout = 30000,
      retries = 3,
    } = config;

    let lastError = "";

    for (let attempt = 0; attempt < retries; attempt++) {
      // 获取可用接口
      const networkInterface = this.getNextAvailableInterface();

      if (!networkInterface) {
        await this.delay(this.waitTime * 1000);
        continue;
      }

      try {
        Logger.debug(
          `Attempt ${attempt + 1}/${retries} using interface ${
            networkInterface.id
          }`
        );

        // 构建请求URL（暫時禁用代理避免測試超時）
        const requestUrl = url; // 直接使用原始 URL，不通過代理

        // 准备请求头
        const requestHeaders = {
          "User-Agent": this.getRandomUserAgent(),
          Referer:
            "https://web.pcc.gov.tw/prkms/prms-viewDailyTenderListClient.do?root=tps",
          ...headers,
        };

        // 执行请求
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        Logger.debug(`Making fetch request to: ${requestUrl}`);
        Logger.debug(`Request method: ${method}`);
        Logger.debug(`Request headers: ${JSON.stringify(requestHeaders)}`);

        let response;
        try {
          // 首先嘗試使用 fetch
          response = await fetch(requestUrl, {
            method,
            headers: requestHeaders,
            body,
            signal: controller.signal,
          });
          Logger.debug(`Fetch response received: ${response ? 'defined' : 'undefined'}`);

          // 如果 fetch 返回 undefined，使用 Node.js 內建模組作為備用
          if (!response) {
            Logger.debug("Fetch returned undefined, using Node.js HTTP client as fallback");
            response = await this.nodeHttpRequest(requestUrl, {
              method,
              headers: requestHeaders,
              body,
              timeout,
            });
            Logger.debug(`Node.js HTTP client response received: ${response ? 'defined' : 'undefined'}`);
          }
        } catch (fetchError) {
          Logger.debug(`Fetch threw error: ${fetchError}, trying Node.js HTTP client`);
          try {
            response = await this.nodeHttpRequest(requestUrl, {
              method,
              headers: requestHeaders,
              body,
              timeout,
            });
            Logger.debug(`Node.js HTTP client response received: ${response ? 'defined' : 'undefined'}`);
          } catch (nodeError) {
            throw new Error(`Both fetch and Node.js HTTP failed. Fetch: ${fetchError instanceof Error ? fetchError.message : String(fetchError)}, Node: ${nodeError instanceof Error ? nodeError.message : String(nodeError)}`);
          }
        }

        clearTimeout(timeoutId);

        // 檢查 response 是否存在
        if (!response) {
          throw new Error("Fetch returned undefined response");
        }

        if (!response.ok) {
          Logger.warn(
            `HTTP error on interface ${networkInterface.id}: ${response.status} ${response.statusText}`
          );
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const content = await response.text();
        Logger.info(
          `Response received on interface ${networkInterface.id}, status: ${response.status}, content length: ${content.length}`
        );

        // 验证响应内容
        if (!this.isValidPage(content)) {
          Logger.warn(
            `Invalid page detected on interface ${networkInterface.id}, content length: ${content.length}`
          );
          if (this.isErrorPage(content)) {
            Logger.warn(`Error page detected: ${content.substring(0, 200)}`);
            this.markInterfaceUnavailable(networkInterface.id);
            continue; // 重试
          }
          // 其他无效情况也重试
          Logger.warn(
            `Other invalid page condition, content preview: ${content.substring(
              0,
              100
            )}`
          );
          continue;
        }

        // 检查是否有验证码（但不标记为失败）
        if (this.isCaptchaPage(content)) {
          Logger.warn(
            `Captcha detected on interface ${networkInterface.id}, returning with captcha flag`
          );
          return {
            success: true,
            data: content,
            statusCode: response.status,
            interfaceId: networkInterface.id,
            hasCaptcha: true, // 添加验证码标志
          };
        }

        Logger.debug(
          `Request successful via interface ${networkInterface.id}, content length: ${content.length}`
        );

        return {
          success: true,
          data: content,
          statusCode: response.status,
          interfaceId: networkInterface.id,
        };
      } catch (error) {
        lastError = error instanceof Error ? error.message : String(error);
        Logger.warn(
          `Request failed on interface ${networkInterface.id}: ${lastError}`
        );

        this.markInterfaceUnavailable(networkInterface.id);

        // 等待一段时间再重试
        await this.delay(1000 * (attempt + 1));
      }
    }

    return {
      success: false,
      error: `Failed after ${retries} attempts. Last error: ${lastError}`,
    };
  }

  // 批量请求（带并发控制）
  async batchRequest(
    configs: RequestConfig[],
    concurrency: number = 3
  ): Promise<RequestResult[]> {
    const results: RequestResult[] = [];

    for (let i = 0; i < configs.length; i += concurrency) {
      const batch = configs.slice(i, i + concurrency);
      const batchPromises = batch.map((config) => this.request(config));
      const batchResults = await Promise.all(batchPromises);

      results.push(...batchResults);

      // 批次间的延迟
      if (i + concurrency < configs.length) {
        await this.delay(1000);
      }
    }

    return results;
  }

  // 获取接口状态
  getInterfaceStats() {
    return {
      total: this.interfaces.length,
      available: this.interfaces.filter(
        (iface) => !this.skipInterfaces[iface.id]
      ).length,
      skipped: Object.keys(this.skipInterfaces).length,
      interfaces: this.interfaces.map((iface) => ({
        id: iface.id,
        isAvailable: !this.skipInterfaces[iface.id],
        failureCount: iface.failureCount,
        lastFailedAt: iface.lastFailedAt,
      })),
    };
  }

  // 簡單的網路連接測試
  async testConnection(): Promise<boolean> {
    try {
      Logger.debug("Testing network connection...");
      let response;

      try {
        // 首先嘗試 fetch
        response = await fetch("https://www.google.com", {
          method: "HEAD",
          signal: AbortSignal.timeout(5000), // 5 秒超時
        });

        // 如果 fetch 返回 undefined，使用備用方案
        if (!response) {
          Logger.debug("Fetch returned undefined in testConnection, using Node.js HTTP client");
          response = await this.nodeHttpRequest("https://www.google.com", {
            method: "HEAD",
            timeout: 5000,
          });
        }
      } catch (fetchError) {
        Logger.debug(`Fetch failed in testConnection: ${fetchError}, trying Node.js HTTP client`);
        response = await this.nodeHttpRequest("https://www.google.com", {
          method: "HEAD",
          timeout: 5000,
        });
      }

      const isConnected = response !== undefined && response.ok;
      Logger.debug(`Network connection test result: ${isConnected ? 'connected' : 'failed'}`);
      return isConnected;
    } catch (error) {
      Logger.debug(`Network connection test failed: ${error}`);
      return false;
    }
  }

}
