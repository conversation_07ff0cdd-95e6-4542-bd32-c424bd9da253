#!/usr/bin/env bun

import { parseArgs } from "util";
import { join } from "path";
import { FileManager } from "./core/FileManager";
import { PCCParser } from "./sites/pcc";
import { TenderData } from "../shared/types";

// Temporary interfaces for compatibility
interface ParserConfig {
  inputDir: string;
  outputDir: string;
  site: string;
  dateFrom?: string;
  dateTo?: string;
  incremental?: boolean;
  verbose?: boolean;
}

interface ParseStats {
  totalFiles: number;
  processedFiles: number;
  failedFiles: number;
  totalTenders: number;
  startTime: Date;
  endTime?: Date;
  errors: string[];
}
import {
  Logger,
  formatDuration,
  formatFileSize,
  calculateSuccessRate,
} from "./core/utils";

// 解析命令行参数
function parseArguments(): ParserConfig {
  const { values } = parseArgs({
    args: process.argv.slice(2),
    options: {
      site: { type: "string", default: "pcc" },
      input: { type: "string", default: "./list" },
      output: { type: "string", default: "./parsed" },
      from: { type: "string" },
      to: { type: "string" },
      incremental: { type: "boolean", default: false },
      verbose: { type: "boolean", default: false },
      help: { type: "boolean", default: false },
    },
    allowPositionals: true,
  });

  if (values.help) {
    printHelp();
    process.exit(0);
  }

  return {
    site: values.site!,
    inputDir: values.input!,
    outputDir: values.output!,
    dateFrom: values.from,
    dateTo: values.to,
    incremental: values.incremental,
    verbose: values.verbose,
  };
}

function printHelp() {
  console.log(`
BidAcumen Parser Daemon
用法:
  bun run src/index.ts [選項]

選項:
  --site <name>        要解析的網站 (預設: pcc)
  --input <dir>        輸入檔案目錄 (預設: ./list)
  --output <dir>       輸出目錄 (預設: ./parsed)
  --from <date>        開始日期 (格式: YYYY-MM-DD)
  --to <date>          結束日期 (格式: YYYY-MM-DD)
  --incremental        增量解析模式
  --verbose            詳細輸出
  --help               顯示幫助資訊

範例:
  bun run src/index.ts --site=pcc --from=2024-01-01 --to=2024-01-31
  bun run src/index.ts --input=./data/list --output=./data/parsed --verbose
  bun run src/index.ts --incremental
  `);
}

// 主解析函数
async function main() {
  const config = parseArguments();

  // 设置日志级别
  Logger.setVerbose(config.verbose || false);

  Logger.info(`Starting BidAcumen Parser Daemon`);
  Logger.info(`Site: ${config.site}`);
  Logger.info(`Input: ${config.inputDir}`);
  Logger.info(`Output: ${config.outputDir}`);

  const stats: ParseStats = {
    totalFiles: 0,
    processedFiles: 0,
    failedFiles: 0,
    totalTenders: 0,
    startTime: new Date(),
    errors: [],
  };

  try {
    // 创建解析器实例
    const parser = createParser(config.site);
    if (!parser) {
      throw new Error(`Unsupported site: ${config.site}`);
    }

    // 获取输入文件列表
    const files = await getInputFiles(config);
    stats.totalFiles = files.length;

    Logger.info(`Found ${files.length} files to process`);

    if (files.length === 0) {
      Logger.warn("No files found to process");
      return;
    }

    // 解析文件
    for (const file of files) {
      try {
        await processFile(file, parser, config, stats);
        stats.processedFiles++;
      } catch (error) {
        stats.failedFiles++;
        const errorMsg = `Failed to process ${file}: ${error}`;
        stats.errors.push(errorMsg);
        Logger.error(errorMsg);
      }
    }

    stats.endTime = new Date();
    printStats(stats);
  } catch (error) {
    Logger.error(`Fatal error: ${error}`);
    process.exit(1);
  }
}

// 创建解析器实例
function createParser(site: string) {
  switch (site.toLowerCase()) {
    case "pcc":
      return new PCCParser();
    default:
      return null;
  }
}

// 获取输入文件列表
async function getInputFiles(config: ParserConfig): Promise<string[]> {
  // 支援 .gz 和 .json 檔案
  const gzFiles = await FileManager.getFiles(config.inputDir, ".gz");
  const jsonFiles = await FileManager.getFiles(config.inputDir, ".json");
  const files = [...gzFiles, ...jsonFiles];

  // 根据日期范围过滤
  const filteredFiles = FileManager.filterFilesByDateRange(
    files,
    config.dateFrom,
    config.dateTo
  );

  // 增量模式：过滤已处理的文件
  if (config.incremental) {
    const unprocessedFiles = [];
    for (const file of filteredFiles) {
      const outputFile = getOutputPath(file, config.outputDir);
      const exists = await FileManager.fileExists(outputFile);
      if (!exists) {
        unprocessedFiles.push(file);
      }
    }
    return unprocessedFiles;
  }

  return filteredFiles;
}

// 处理单个文件
async function processFile(
  filePath: string,
  parser: PCCParser,
  config: ParserConfig,
  stats: ParseStats
) {
  Logger.debug(`Processing file: ${filePath}`);

  const fileStats = await FileManager.getFileStats(filePath);
  Logger.debug(`File size: ${formatFileSize(fileStats.size)}`);

  let items: any[] = [];
  let tenders: TenderData[] = [];

  if (filePath.endsWith(".json")) {
    // 處理已經爬取好的 JSON 檔案
    const fileContent = await FileManager.readFile(filePath);
    const data = JSON.parse(fileContent);

    if (data.tenders && Array.isArray(data.tenders)) {
      tenders = data.tenders;
      stats.totalTenders += tenders.length;
      Logger.debug(`Found ${tenders.length} tenders in JSON file`);
    } else {
      Logger.warn(`Invalid JSON structure in ${filePath}`);
    }
  } else {
    // 處理原始 HTML 檔案
    const html = await FileManager.readFile(filePath);

    // 解析列表页面
    items = await parser.parseList(html, filePath);
    Logger.debug(`Found ${items.length} tender items`);

    // 實際爬取每個標案的詳細頁面
    Logger.info(`Starting to crawl ${items.length} tender details...`);

    const BATCH_SIZE = 10;
    const DELAY_BETWEEN_REQUESTS = 1500; // 1.5 秒間隔
    const DELAY_BETWEEN_BATCHES = 3000; // 3 秒批次間隔

    for (let i = 0; i < items.length; i += BATCH_SIZE) {
      const batch = items.slice(i, i + BATCH_SIZE);
      const batchNumber = Math.floor(i / BATCH_SIZE) + 1;
      const totalBatches = Math.ceil(items.length / BATCH_SIZE);

      Logger.info(
        `Processing batch ${batchNumber}/${totalBatches} (${batch.length} items)`
      );

      for (let j = 0; j < batch.length; j++) {
        const item = batch[j];
        const itemIndex = i + j + 1;

        try {
          Logger.debug(
            `[${itemIndex}/${items.length}] Crawling: ${item.title}`
          );

          // 使用 parser 的 fetchTenderDetail 方法
          const tenderData = await parser.fetchTenderDetail(item.url);

          // 補充 list 中的基本資訊
          tenderData.id = item.id;
          tenderData.title = item.title || tenderData.title;
          tenderData.agency = item.agency || tenderData.agency;

          tenders.push(tenderData);
          stats.totalTenders++;

          Logger.debug(
            `[${itemIndex}] Success: ${tenderData.title} - Budget: ${tenderData.budget}`
          );
        } catch (error) {
          Logger.warn(
            `[${itemIndex}] Failed to crawl detail for ${item.id}: ${error}`
          );
          // 創建一個基本的 TenderData 作為備用
          const fallbackTender = createFallbackTenderData(item);
          tenders.push(fallbackTender);
          stats.totalTenders++;
        }

        // 延遲避免被封
        if (j < batch.length - 1 || i + BATCH_SIZE < items.length) {
          await new Promise((resolve) =>
            setTimeout(resolve, DELAY_BETWEEN_REQUESTS)
          );
        }
      }

      // 批次間延遲
      if (i + BATCH_SIZE < items.length) {
        Logger.info(
          `Batch ${batchNumber} completed, waiting ${
            DELAY_BETWEEN_BATCHES / 1000
          }s...`
        );
        await new Promise((resolve) =>
          setTimeout(resolve, DELAY_BETWEEN_BATCHES)
        );
      }
    }

    Logger.info(
      `Crawling completed: ${tenders.length}/${items.length} tenders successfully processed`
    );
  }

  // 保存解析结果
  const outputPath = getOutputPath(filePath, config.outputDir);
  await FileManager.writeJsonFile(outputPath, {
    sourceFile: filePath,
    processedAt: new Date().toISOString(),
    itemCount: items.length,
    tenderCount: tenders.length,
    successRate:
      items.length > 0
        ? `${((tenders.length / items.length) * 100).toFixed(1)}%`
        : "N/A",
    items,
    tenders,
  });

  Logger.info(
    `Processed ${filePath}: ${items.length} items, ${tenders.length} tenders`
  );
}

// Create fallback TenderData when detail page crawling fails
function createFallbackTenderData(item: any): TenderData {
  return {
    id: item.id,
    title: item.title,
    agency: item.agency,
    budget: 0,
    deadline: "",
    publishDate: item.publishDate,
    projectId: item.id,
    status: "open" as const,
    category: "",
    location: "",
    description: "",
    aiScore: 0,
    tenderType: "tender",
    agencyInfo: {
      agencyCode: "",
      agencyName: item.agency,
      unitName: "",
      agencyAddress: "",
      contactPerson: "",
      contactPhone: "",
      faxNumber: "",
      email: "",
    },
    procurementInfo: {
      caseNumber: item.id,
      caseName: item.title,
      targetCategory: "",
      budgetRange: "",
      budgetAmount: 0,
      isBudgetPublic: false,
    },
    tenderInfo: {
      tenderMethod: "",
      awardMethod: "",
      announcementCount: 0,
      announcementDate: item.publishDate,
      hasReservePrice: false,
    },
  };
}

// 获取输出文件路径
function getOutputPath(inputPath: string, outputDir: string): string {
  const fileName =
    inputPath.split("/").pop()?.replace(".html.gz", ".json") || "output.json";
  return join(outputDir, fileName);
}

// 打印统计信息
function printStats(stats: ParseStats) {
  const duration = stats.endTime!.getTime() - stats.startTime.getTime();

  console.log("\n=== 解析完成 ===");
  console.log(`处理时间: ${formatDuration(duration)}`);
  console.log(`总文件数: ${stats.totalFiles}`);
  console.log(`成功处理: ${stats.processedFiles}`);
  console.log(`处理失败: ${stats.failedFiles}`);
  console.log(
    `成功率: ${calculateSuccessRate(stats.processedFiles, stats.totalFiles)}`
  );
  console.log(`总标案数: ${stats.totalTenders}`);

  if (stats.errors.length > 0) {
    console.log("\n=== 错误列表 ===");
    stats.errors.forEach((error, index) => {
      console.log(`${index + 1}. ${error}`);
    });
  }

  console.log("\n解析器运行完成!");
}

// 错误处理
process.on("uncaughtException", (error) => {
  Logger.error(`Uncaught Exception: ${error.message}`);
  process.exit(1);
});

process.on("unhandledRejection", (reason, promise) => {
  Logger.error(`Unhandled Rejection at: ${promise}, reason: ${reason}`);
  process.exit(1);
});

// 启动程序
main().catch((error) => {
  Logger.error(`Application error: ${error}`);
  process.exit(1);
});
