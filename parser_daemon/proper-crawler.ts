import { PCCParser } from "./src/sites/pcc";
import { writeFileSync } from "fs";
import { gzip } from "zlib";
import { promisify } from "util";

const gzipAsync = promisify(gzip);

async function properCrawl() {
  console.log("🕷️ 開始正確的爬取流程...");

  const parser = new PCCParser();

  try {
    const dateStr = "114年06月26日";
    console.log(`📅 爬取日期: ${dateStr}`);

    // 第一步：爬取 list 頁面的原始 HTML
    console.log("📄 第一步：爬取標案列表頁面...");

    const listUrl = `https://web.pcc.gov.tw/prkms/tender/common/noticeDate/readPublish?dateStr=${encodeURIComponent(
      dateStr
    )}`;

    const result = await parser["networkManager"].request({
      url: listUrl,
      useProxy: false,
      retries: 5,
    });

    if (!result.success) {
      throw new Error(`Failed to fetch list page: ${result.error}`);
    }

    const listHtml = result.data!;
    console.log(`✅ 成功獲取列表頁面，長度: ${listHtml.length}`);

    // 保存原始 list HTML 為 .gz 檔案
    const compressed = await gzipAsync(Buffer.from(listHtml, "utf-8"));
    writeFileSync(`./list/114-06-26.html.gz`, compressed);
    console.log("💾 列表頁面已壓縮保存為: ./list/114-06-26.html.gz");

    // 第二步：解析 list 獲得 tender 列表
    console.log("🔍 第二步：解析標案列表...");
    const listItems = await parser.parseList(listHtml, "114-06-26");
    console.log(`📋 找到 ${listItems.length} 個標案項目`);

    if (listItems.length === 0) {
      console.log("⚠️ 沒有找到標案項目");
      return;
    }

    // 顯示前幾個項目
    console.log("\n📄 前 3 個標案項目:");
    listItems.slice(0, 3).forEach((item, index) => {
      console.log(`${index + 1}. ID: ${item.id}`);
      console.log(`   標題: ${item.title}`);
      console.log(`   機關: ${item.agency}`);
      console.log(`   URL: ${item.url}`);
      console.log("   ---");
    });

    // 第三步：爬取前 5 個詳細頁面作為示例（避免太多請求）
    console.log("\n🔍 第三步：爬取詳細頁面 (示例前5個)...");

    for (let i = 0; i < Math.min(5, listItems.length); i++) {
      const item = listItems[i];
      console.log(`📄 正在爬取詳細頁面 ${i + 1}/5: ${item.id}`);

      try {
        const detailResult = await parser["networkManager"].request({
          url: item.url,
          useProxy: false,
          retries: 3,
        });

        if (detailResult.success) {
          const detailHtml = detailResult.data!;
          console.log(`✅ 成功獲取詳細頁面，長度: ${detailHtml.length}`);

          // 保存詳細頁面
          writeFileSync(`./temp/detail_${item.id}.html`, detailHtml);

          // 嘗試解析詳細頁面
          try {
            const tenderData = await parser.parseDetail(detailHtml, item.url);
            console.log(`✅ 成功解析: ${tenderData.title}`);
            console.log(`   機關: ${tenderData.agency}`);
            console.log(`   預算: ${tenderData.budget}`);
          } catch (parseError) {
            console.log(`⚠️ 解析失敗: ${parseError}`);
          }
        } else {
          console.log(`❌ 爬取詳細頁面失敗: ${detailResult.error}`);
        }

        // 延遲避免被封
        await new Promise((resolve) => setTimeout(resolve, 1000));
      } catch (error) {
        console.log(`❌ 爬取 ${item.id} 失敗: ${error}`);
      }
    }

    console.log("\n🎯 正確的爬取流程完成！");
    console.log("💡 現在你應該有:");
    console.log("   1. list/114-06-26.html.gz (列表頁面)");
    console.log("   2. temp/detail_*.html (詳細頁面)");
    console.log("   3. 可以用 parser 處理這些檔案生成正確的 JSON");
  } catch (error) {
    console.error(`❌ 爬取失敗: ${error}`);
  }
}

properCrawl();
