// 測試 NetworkManager 的問題
import { NetworkManager } from "./src/core/NetworkManager.js";

console.log("=== 測試 NetworkManager ===");

async function testNetworkManager() {
  try {
    const networkManager = new NetworkManager(1);
    
    console.log("1. 測試基本請求...");
    const result = await networkManager.request({
      url: "https://httpbin.org/get",
      method: "GET",
      timeout: 10000,
      retries: 1,
    });

    console.log("NetworkManager 結果:", {
      success: result.success,
      statusCode: result.statusCode,
      dataLength: result.data?.length,
      error: result.error,
    });

    if (result.data) {
      console.log("數據預覽:", result.data.substring(0, 200));
    }

  } catch (error) {
    console.error("測試失敗:", error);
  }
}

testNetworkManager();
