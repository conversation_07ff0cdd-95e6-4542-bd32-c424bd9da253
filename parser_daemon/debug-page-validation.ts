// 調試頁面驗證邏輯
import { NetworkManager } from "./src/core/NetworkManager";

async function debugPageValidation() {
  console.log("=== 調試頁面驗證邏輯 ===");
  
  // 直接獲取政府網站內容
  const response = await fetch("https://web.pcc.gov.tw/prkms/prms-viewDailyTenderListClient.do?root=tps");
  const content = await response.text();
  
  console.log("頁面基本信息:");
  console.log("- 狀態碼:", response.status);
  console.log("- 內容長度:", content.length);
  console.log("- 內容開頭:", content.substring(0, 300));
  
  // 測試錯誤指示符
  const errorIndicators = [
    "政府電子採購網_失敗訊息畫面",
    "500 Internal Server Error", 
    "Internal Server Error",
    "502 Proxy Error",
    "錯誤訊息",
    "您尚未登入或已被登出本系統",
  ];
  
  console.log("\n錯誤指示符檢查:");
  for (const indicator of errorIndicators) {
    const found = content.includes(indicator);
    if (found) {
      console.log(`❌ 找到錯誤指示符: "${indicator}"`);
      // 找到匹配的上下文
      const index = content.indexOf(indicator);
      const context = content.substring(Math.max(0, index - 50), index + indicator.length + 50);
      console.log(`   上下文: ${context}`);
    } else {
      console.log(`✅ 未找到: "${indicator}"`);
    }
  }
  
  // 測試 NetworkManager 的私有方法
  const networkManager = new NetworkManager(1);
  
  // 由於方法是私有的，我們需要用 any 來訪問
  const isErrorPage = (networkManager as any).isErrorPage(content);
  const isValidPage = (networkManager as any).isValidPage(content);
  
  console.log("\nNetworkManager 驗證結果:");
  console.log("- isErrorPage:", isErrorPage);
  console.log("- isValidPage:", isValidPage);
}

debugPageValidation().catch(console.error);
