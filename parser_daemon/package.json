{"name": "bidacumen-parser-daemon", "version": "1.0.0", "description": "BidAcumen Parser Daemon for tender data extraction", "main": "src/index.ts", "scripts": {"start": "bun run src/index.ts", "dev": "bun run --watch src/index.ts", "build": "tsc", "test": "jest", "test:unit": "jest test/unit", "test:integration": "jest test/integration", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "type-check": "tsc --noEmit"}, "dependencies": {"cheerio": "^1.0.0-rc.12", "axios": "^1.6.0", "puppeteer": "^21.5.0", "p-limit": "^4.0.0"}, "devDependencies": {"@types/node": "^20.8.0", "typescript": "^5.2.0", "vitest": "^1.0.0", "@vitest/coverage-v8": "^1.0.0", "jsdom": "^23.0.0", "happy-dom": "^12.0.0", "@types/cheerio": "^0.22.0", "msw": "^2.0.0"}, "keywords": ["parser", "tender", "bidacumen", "typescript", "bun"], "author": "BidAcumen Team", "license": "MIT", "type": "module"}