import { PCCParser } from "./src/sites/pcc";
import { FileManager } from "./src/core/FileManager";
import { TenderData, ListItem } from "./src/types";

async function testCrawler() {
  console.log("🧪 開始測試爬取流程 (前5個標案)...");

  const parser = new PCCParser();

  try {
    // 讀取已解析的 list items
    console.log("📄 讀取標案列表...");

    const listData = await FileManager.readFile("parsed/114-06-26.json");
    const parsedData = JSON.parse(listData);
    const allItems: ListItem[] = parsedData.items;

    // 只取前5個項目進行測試
    const items = allItems.slice(0, 5);

    console.log(`📋 測試 ${items.length} 個標案項目`);

    const allTenders: TenderData[] = [];
    const errors: string[] = [];

    for (let i = 0; i < items.length; i++) {
      const item = items[i];

      try {
        console.log(`\n📄 [${i + 1}/${items.length}] 處理: ${item.title}`);
        console.log(`🔗 URL: ${item.url}`);

        // 直接爬取 redirectPublic URL
        const result = await parser["networkManager"].request({
          url: item.url,
          useProxy: false,
          retries: 3,
        });

        if (result.success) {
          const html = result.data!;
          console.log(`✅ [${i + 1}] HTML 長度: ${html.length}`);

          // 檢查是否是真正的詳細頁面
          if (html.includes("機關資料") || html.includes("採購資料")) {
            console.log(`📄 [${i + 1}] 這是詳細頁面，開始解析...`);

            try {
              const tenderData = await parser.parseDetail(html, item.url);

              // 補充基本資訊
              tenderData.id = item.id;
              tenderData.title = item.title || tenderData.title;
              tenderData.agency = item.agency || tenderData.agency;

              allTenders.push(tenderData);
              console.log(`✅ [${i + 1}] 解析成功:`);
              console.log(`   標題: ${tenderData.title}`);
              console.log(`   機關: ${tenderData.agency}`);
              console.log(`   預算: ${tenderData.budget.toLocaleString()} 元`);
            } catch (parseError) {
              console.log(`⚠️ [${i + 1}] 解析失敗: ${parseError}`);
              errors.push(`${item.id}: 解析失敗 - ${parseError}`);
            }
          } else {
            console.log(`🔗 [${i + 1}] 這是重定向頁面，尋找真實 URL...`);

            // 嘗試提取真實 URL
            const realUrl = extractRealDetailUrl(html);
            if (realUrl) {
              console.log(`✅ [${i + 1}] 找到真實 URL: ${realUrl}`);

              // 爬取真實詳細頁面
              const detailResult = await parser["networkManager"].request({
                url: realUrl,
                useProxy: false,
                retries: 3,
              });

              if (detailResult.success) {
                const detailHtml = detailResult.data!;
                console.log(
                  `✅ [${i + 1}] 真實頁面 HTML 長度: ${detailHtml.length}`
                );

                try {
                  const tenderData = await parser.parseDetail(
                    detailHtml,
                    realUrl
                  );

                  tenderData.id = item.id;
                  tenderData.title = item.title || tenderData.title;
                  tenderData.agency = item.agency || tenderData.agency;

                  allTenders.push(tenderData);
                  console.log(
                    `✅ [${
                      i + 1
                    }] 最終解析成功: 預算 ${tenderData.budget.toLocaleString()} 元`
                  );
                } catch (parseError) {
                  console.log(`⚠️ [${i + 1}] 真實頁面解析失敗: ${parseError}`);
                  errors.push(`${item.id}: 真實頁面解析失敗 - ${parseError}`);
                }
              } else {
                console.log(
                  `❌ [${i + 1}] 真實頁面爬取失敗: ${detailResult.error}`
                );
                errors.push(
                  `${item.id}: 真實頁面爬取失敗 - ${detailResult.error}`
                );
              }
            } else {
              console.log(`❌ [${i + 1}] 無法找到真實 URL`);
              errors.push(`${item.id}: 無法找到真實 URL`);
            }
          }
        } else {
          console.log(`❌ [${i + 1}] 爬取失敗: ${result.error}`);
          errors.push(`${item.id}: 爬取失敗 - ${result.error}`);
        }

        // 延遲避免被封
        if (i < items.length - 1) {
          console.log(`⏱️ 等待 3 秒...`);
          await new Promise((resolve) => setTimeout(resolve, 3000));
        }
      } catch (error) {
        console.log(`❌ [${i + 1}] 處理失敗: ${error}`);
        errors.push(`${item.id}: 處理失敗 - ${error}`);
      }
    }

    // 保存測試結果
    const testData = {
      sourceFile: "test crawl of first 5 items",
      processedAt: new Date().toISOString(),
      itemCount: items.length,
      tenderCount: allTenders.length,
      successRate: `${((allTenders.length / items.length) * 100).toFixed(1)}%`,
      items: items,
      tenders: allTenders,
      errors: errors,
    };

    await FileManager.writeJsonFile("parsed/test-results.json", testData);

    console.log("\n🎯 測試完成！");
    console.log(`📊 統計資料:`);
    console.log(`   測試標案數: ${items.length}`);
    console.log(`   成功爬取: ${allTenders.length}`);
    console.log(`   失敗數量: ${errors.length}`);
    console.log(
      `   成功率: ${((allTenders.length / items.length) * 100).toFixed(1)}%`
    );
    console.log(`   輸出檔案: parsed/test-results.json`);

    if (errors.length > 0) {
      console.log(`\n❌ 錯誤列表:`);
      errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`);
      });
    }
  } catch (error) {
    console.error(`❌ 測試失敗: ${error}`);
  }
}

// 從重定向頁面提取真正的詳細頁面 URL
function extractRealDetailUrl(html: string): string | null {
  // 檢查是否包含 JavaScript 重定向
  const jsRedirectMatch = html.match(
    /window\.location\.href\s*=\s*['"]([^'"]+)['"]/
  );
  if (jsRedirectMatch) {
    let url = jsRedirectMatch[1];
    if (url.startsWith("/")) {
      url = "https://web.pcc.gov.tw" + url;
    }
    return url;
  }

  // 檢查是否包含 meta refresh
  const metaRefreshMatch = html.match(
    /<meta[^>]+http-equiv\s*=\s*['"]refresh['"][^>]+content\s*=\s*['"][^;]+;\s*url=([^'"]+)['"]/i
  );
  if (metaRefreshMatch) {
    let url = metaRefreshMatch[1];
    if (url.startsWith("/")) {
      url = "https://web.pcc.gov.tw" + url;
    }
    return url;
  }

  // 檢查是否包含詳細頁面的直接連結 (搜尋用戶提到的三種類型)
  const detailLinkMatch =
    html.match(
      /href\s*=\s*['"]([^'"]*(?:searchTenderDetail|QueryAtmAwardDetail|showAtmNonAwardHistory)[^'"]*)['"]/
    ) ||
    html.match(/href\s*=\s*['"]([^'"]*pkPmsMain[^'"]*)['"]/) ||
    html.match(/href\s*=\s*['"]([^'"]*pkAtmMain[^'"]*)['"]/) ||
    html.match(/href\s*=\s*['"]([^'"]*fkPmsMainHist[^'"]*)['"]/);

  if (detailLinkMatch) {
    let url = detailLinkMatch[1];
    if (url.startsWith("/")) {
      url = "https://web.pcc.gov.tw" + url;
    }
    return url;
  }

  // 檢查頁面中是否有框架或 iframe
  const iframeMatch = html.match(/<iframe[^>]+src\s*=\s*['"]([^'"]+)['"]/i);
  if (iframeMatch) {
    let url = iframeMatch[1];
    if (url.startsWith("/")) {
      url = "https://web.pcc.gov.tw" + url;
    }
    return url;
  }

  return null;
}

testCrawler();
