// 測試政府網站訪問
import { NetworkManager } from "./src/core/NetworkManager";

async function testGovernmentSite() {
  console.log("=== 測試政府網站訪問 ===");
  
  const networkManager = new NetworkManager(1);
  
  console.log("測試政府網站...");
  const result = await networkManager.request({
    url: "https://web.pcc.gov.tw/prkms/prms-viewDailyTenderListClient.do?root=tps",
    method: "GET",
    timeout: 15000,
    retries: 1,
  });

  console.log("政府網站結果:", {
    success: result.success,
    statusCode: result.statusCode,
    dataLength: result.data?.length,
    error: result.error,
    hasCaptcha: result.hasCaptcha,
  });

  if (result.data) {
    console.log("內容預覽:", result.data.substring(0, 300));
  }
}

testGovernmentSite().catch(console.error);
