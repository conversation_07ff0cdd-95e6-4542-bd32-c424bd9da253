{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "outDir": "./dist", "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "include": ["src/**/*", "test/**/*"], "exclude": ["node_modules", "dist"], "types": ["bun-types"]}