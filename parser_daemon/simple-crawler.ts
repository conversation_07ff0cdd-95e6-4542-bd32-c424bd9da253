import { PCCParser } from "./src/sites/pcc";
import { writeFileSync } from "fs";

async function crawl() {
  console.log("🕷️ 開始爬取標案資料...");

  const parser = new PCCParser();

  try {
    // 爬取 113年06月22日 的資料 (對應 2024-06-22)
    const tenderData = await parser.fetchTenderList("113年06月22日", true);

    console.log(`✅ 成功爬取到 ${tenderData.length} 筆標案資料`);

    if (tenderData.length > 0) {
      // 將資料保存為 JSON 檔案
      const data = {
        date: "113年06月22日",
        crawledAt: new Date().toISOString(),
        count: tenderData.length,
        tenders: tenderData,
      };

      writeFileSync("./list/113-06-22.json", JSON.stringify(data, null, 2));
      console.log("💾 資料已保存到: ./list/113-06-22.json");

      // 顯示第一筆資料
      console.log("\n📄 第一筆標案資料:");
      console.log({
        id: tenderData[0].id,
        title: tenderData[0].title,
        agency: tenderData[0].agency,
      });
    } else {
      console.log("⚠️ 該日期無標案資料，嘗試最近的日期...");

      // 嘗試今天的日期
      const today = new Date();
      const rocYear = today.getFullYear() - 1911;
      const dateStr = `${rocYear}年${String(today.getMonth() + 1).padStart(
        2,
        "0"
      )}月${String(today.getDate()).padStart(2, "0")}日`;

      console.log(`🔄 嘗試爬取今天 (${dateStr}) 的資料...`);
      const todayData = await parser.fetchTenderList(dateStr, true);

      if (todayData.length > 0) {
        const data = {
          date: dateStr,
          crawledAt: new Date().toISOString(),
          count: todayData.length,
          tenders: todayData,
        };

        writeFileSync(
          `./list/${rocYear}-${String(today.getMonth() + 1).padStart(
            2,
            "0"
          )}-${String(today.getDate()).padStart(2, "0")}.json`,
          JSON.stringify(data, null, 2)
        );
        console.log(`💾 今天的資料已保存，共 ${todayData.length} 筆`);
      }
    }
  } catch (error) {
    console.error(`❌ 爬取失敗: ${error}`);
  }
}

crawl();
