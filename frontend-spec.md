Of course. After a thorough review, I have identified several necessary pages for a complete user journey, including authentication flows and standard informational pages.

Here is the definitive and complete list of all frontend pages and key states required for the application.

---

### **Final Frontend Pages & Features Specification**

This document serves as the complete blueprint for all pages, components, and key states for the "BidAcumen" frontend application.

---

### **Part 1: Core Application Pages**

These are the primary pages where users will spend most of their time.

**1. Homepage (`/`)**

- **Objective:** To introduce the product's value proposition to new visitors and serve as an entry point.
- **Features:**
  - `Hero Section`: Compelling headline, brief product description, and a primary "Sign Up Free" CTA.
  - `Main Search Bar`: A simple keyword search bar that directs to the `/search` page.
  - `AI Teaser Module`: For guests, it shows blurred AI analysis to encourage signup. For free users, it's an upsell block for the Premium plan.

**2. Search Results Page (`/search`)**

- **Objective:** To enable powerful, intuitive searching for tenders.
- **Features:**
  - `Filter Sidebar`: Offers Basic filters for guests, unlocks more for Free users, and provides full access (including Semantic Search) to Premium users.
  - `Results List`: Displays a limited list for guests and a full list for registered users. Premium users see a color-coded `AI Opportunity Score` next to each result.
  - `Saved Search Functionality`: A "Save Search" button for registered users to create email alerts.

**3. Tender Details Page (`/tender/{id}`)**

- **Objective:** To provide all public tender information while making the exclusive AI analysis the clear and compelling reason to upgrade to Premium.
- **Features:**
  - `Tender Header`: Displays title, key dates, and a `Status Tag` (e.g., [Open], [Awarded]).
  - `Public Information Section`: All official data, cleanly presented.
  - `AI Insight Module`: A locked, visually distinct upsell box for Free users. For Premium users, it's fully unlocked, displaying the Score, Summary, Checklists, and a `Historical Data` analysis of the issuing authority.

**4. AI Recommendation Hub (`/dashboard`)**

- **Objective:** To serve as the primary, high-value landing page for Premium subscribers, delivering on the core promise of the product.
- **Features:**
  - `Page Access Control`: Inaccessible to non-Premium users (redirects to `/pricing`).
  - `Onboarding Module`: A one-time guide for new Premium users to set up their AI profile.
  - `Personalized Tender List`: The main feature – a curated feed of high-score tenders based on the user's profile.

---

### **Part 2: User Account & Authentication Pages**

These pages manage the entire user lifecycle, from registration to account management.

**5. Sign Up Page (`/signup`)**

- **Objective:** To provide a simple, low-friction registration process.
- **Features:**
  - `Registration Form`: Fields for Email, Password, and Password Confirmation.
  - Links to Terms of Service and Privacy Policy.
  - A link to the `/login` page for existing users.

**6. Log In Page (`/login`)**

- **Objective:** To provide secure access for registered users.
- **Features:**
  - `Login Form`: Fields for Email and Password.
  - `Forgot Password` link, directing to the `/forgot-password` page.
  - A link to the `/signup` page for new users.

**7. Forgot Password Page (`/forgot-password`)**

- **Objective:** To allow users to initiate the password reset process.
- **Features:**
  - `Email Input Form`: A single field for the user to enter their account email address.
  - `Instructions`: Clear text explaining that a reset link will be sent to their email.

**8. Reset Password Page (`/reset-password`)**

- **Objective:** To allow users with a valid token to set a new password.
- **Features:**
  - `New Password Form`: Fields for New Password and Confirm New Password.
  - The page URL will contain a unique, secure token (e.g., `/reset-password?token=...`).

**9. User Settings Page (`/settings`)**

- **Objective:** To be the central hub for all user-configurable options. This page will use a tabbed or sub-navigation layout.
- **Features / Sub-Pages:**
  - `Profile (`/settings/profile`)`: Manage email and password.
  - `Subscription (`/settings/subscription`)`: View current plan (Free/Premium), manage billing, and handle upgrades or cancellations.
  - `AI Profile (`/settings/ai-profile`)`: Locked for Free users. Premium users configure keywords and preferences here to tune their dashboard.
  - `Notifications (`/settings/notifications`)`: Granular control over all In-App and Email notifications.
  - `Bookmarks (`/settings/bookmarks`)`: A list of all tenders the user has saved.

---

### **Part 3: Conversion & Informational Pages**

These pages support marketing, sales, and user information needs.

**10. Pricing Page (`/pricing`)**

- **Objective:** To clearly communicate the value of each plan and convert users to the Premium tier.
- **Features:**
  - `Plan Comparison`: A side-by-side view of Free vs. Premium features.
  - `CTA Buttons`: "Sign Up Free" and "Go Premium".
  - `FAQ Section`: Answers common questions about the service.

**11. About Us Page (`/about`)**

- **Objective:** To build trust and communicate the product's mission and story.
- **Features:**
  - Company mission statement and product vision.

**12. Contact Page (`/contact`)**

- **Objective:** To provide a way for users to request support or give feedback.
- **Features:**
  - `Contact Form` or `mailto:` link for support inquiries.

---

### **Part 4: System & Legal Pages**

These are essential pages for legal compliance and handling application states.

**13. Terms of Service Page (`/terms-of-service`)**

- **Objective:** To display the legal terms governing the use of the service.
- **Features:**
  - Formatted legal text.

**14. Privacy Policy Page (`/privacy-policy`)**

- **Objective:** To inform users how their data is collected, used, and protected.
- **Features:**
  - Formatted legal text.

**15. 404 Not Found Page**

- **Objective:** To gracefully handle invalid URLs and guide users back to the application.
- **Features:**
  - A user-friendly message ("Sorry, we can't find that page.").
  - A prominent link or button to return to the Homepage or Dashboard.

---

### **Part 5: Key User States & Modals (Not Full Pages)**

These are critical UI states that appear over existing pages.

- **`Email Verification State`**: A dedicated screen or message shown after signup, instructing the user to check their email to verify their account before they can log in.
- **`Welcome to Premium Modal`**: A success modal that appears immediately after a user successfully upgrades. It should congratulate them and briefly highlight 2-3 key features they've just unlocked.
- **`Payment Flow Modal`**: A secure, embedded modal (e.g., Stripe Checkout) for processing subscription payments, triggered from the `/pricing` page.
