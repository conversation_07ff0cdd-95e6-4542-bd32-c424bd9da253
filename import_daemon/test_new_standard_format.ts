#!/usr/bin/env node

import { Transformer } from "./src/transformer";
import { TenderRecord } from "./src/types";
import * as fs from "fs-extra";
import * as path from "path";

interface TestDataCollection {
  description: string;
  dates: Array<{
    date: string;
    count: number;
    totalFound: number;
  }>;
  totalRecords: number;
  records: TenderRecord[];
  generatedAt: string;
}

async function testNewStandardFormat() {
  console.log("🧪 Testing New Standardized TenderData Format");
  console.log("=" .repeat(50));

  const transformer = new Transformer();

  // Load test data from our recent import
  const outputDataPath = path.join(__dirname, "../data/output/2019/01/5.4.36_10714-A.json");

  if (!fs.existsSync(outputDataPath)) {
    console.error("❌ Test data file not found:", outputDataPath);
    return;
  }

  const legacyTenderData = await fs.readJson(outputDataPath);
  console.log(`📊 Loaded legacy tender data: ${legacyTenderData.標案名稱}`);

  // Create a mock TenderRecord from the legacy data for testing
  const testRecord: TenderRecord = {
    unit_id: legacyTenderData.機關資料.機關代碼,
    unit_name: legacyTenderData.機關資料.機關名稱,
    job_number: legacyTenderData.標案編號.split('_')[1] || 'test',
    date: 20190102,
    unit_api_url: "",
    tender_api_url: "",
    unit_url: "",
    url: legacyTenderData.網址,
    brief: {
      type: "決標公告",
      title: legacyTenderData.標案名稱,
      companies: {
        ids: [],
        names: [],
        id_key: {},
        name_key: {}
      }
    },
    detail: legacyTenderData.機關資料,
    filename: "test-file"
  };
  console.log(`\n🔍 Testing record: ${testRecord.brief?.title || 'Unknown'}`);

  // Legacy format
  console.log("\n📋 Legacy Format (LegacyTenderData):");
  const legacyData = transformer.transformToTenderData(testRecord);
  console.log("- 標案編號:", legacyData.標案編號);
  console.log("- 標案名稱:", legacyData.標案名稱);
  console.log("- 機關名稱:", legacyData.機關名稱);
  console.log("- 預算金額:", legacyData.預算金額?.toLocaleString() || 'N/A');
  console.log("- 標的分類:", legacyData.標的分類);

  // New standardized format
  console.log("\n🆕 New Standardized Format (TenderData):");
  const standardData = transformer.transformToStandardTenderData(testRecord);
  console.log("- tender_id:", standardData.tender_id);
  console.log("- title:", standardData.title);
  console.log("- agency_name:", standardData.agency_name);
  console.log("- budget_amount:", standardData.budget_amount?.toLocaleString() || 'N/A');
  console.log("- category:", standardData.category);
  console.log("- category_type:", standardData.category_type);
  console.log("- status:", standardData.status);
  console.log("- tender_type:", standardData.tender_type);

  // Show database-friendly structure
  console.log("\n🗄️ Database-Friendly Fields:");
  console.log("- agency_code:", standardData.agency_code);
  console.log("- project_id:", standardData.project_id);
  console.log("- pcc_main_key:", standardData.pcc_main_key);
  console.log("- announcement_date:", standardData.announcement_date);
  console.log("- submission_deadline:", standardData.submission_deadline);
  console.log("- performance_location:", standardData.performance_location);

  // Show analytics-friendly structure
  console.log("\n📊 Analytics-Friendly Fields:");
  console.log("- procurement_method:", standardData.procurement_method);
  console.log("- award_method:", standardData.award_method);
  console.log("- is_multiple_award:", standardData.is_multiple_award);
  console.log("- is_wto_gpa:", standardData.is_wto_gpa);
  console.log("- reserve_price:", standardData.reserve_price);

  // Show history tracking
  console.log("\n📈 History Tracking:");
  console.log("- history count:", standardData.history?.length || 0);
  if (standardData.history && standardData.history.length > 0) {
    const latestHistory = standardData.history[0];
    console.log("- latest_record_date:", latestHistory.date);
    console.log("- latest_status:", latestHistory.status);
    console.log("- latest_tender_type:", latestHistory.tenderType);
  }

  // Save sample output
  const outputPath = path.join(__dirname, "sample_new_format.json");
  await fs.writeJson(outputPath, standardData, { spaces: 2 });
  console.log(`\n💾 Sample new format saved to: ${outputPath}`);

  console.log("\n✅ New standardized format test completed!");
  console.log("\n🎯 Benefits of the new format:");
  console.log("  • English field names for international compatibility");
  console.log("  • Snake_case naming for database consistency");
  console.log("  • Proper TypeScript typing for better development experience");
  console.log("  • Optimized for PostgreSQL analytics and cross-analysis");
  console.log("  • Normalized structure for better data integrity");
  console.log("  • Clear separation of concerns (basic info, agency, procurement, etc.)");
}

// Run the test
testNewStandardFormat().catch(console.error);
