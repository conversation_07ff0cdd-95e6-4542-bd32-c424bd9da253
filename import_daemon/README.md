# Import Daemon

批次匯入政府採購網標案資料的工具程式。

## 功能特色

- 🚀 批次下載指定日期範圍的標案資料
- 🔄 自動重試機制與錯誤處理
- 💾 進度追蹤與中斷後續行功能
- 📁 按年月自動分類存檔
- 📊 詳細的執行報告與統計
- 🧹 資料清理與去重複功能
- 🌐 支援 Webshare 代理

## 安裝

1. 安裝依賴：

```bash
cd bidacumen-backend/import_daemon
npm install
npm run build
```

2. 配置環境變數：

```bash
# 複製範例環境文件
cp .env.example .env

# 編輯 .env 並設置你的 Webshare 代理認證資訊
WEBSHARE_PROXY_USERNAME=your_username_here
WEBSHARE_PROXY_PASSWORD=your_password_here
WEBSHARE_PROXY_HOST=p.webshare.io
WEBSHARE_PROXY_PORT=80
```

3. 在 `config/config.json` 中配置代理設定：

```json
{
  "api": {
    "baseUrl": "https://pcc.g0v.ronny.tw/api",
    "timeout": 30000,
    "retryAttempts": 3,
    "delayBetweenRequests": 1000
  },
  "processing": {
    "batchSize": 10,
    "concurrency": 5,
    "progressSaveInterval": 10
  },
  "storage": {
    "outputDir": "./data/output",
    "progressFile": "./data/progress.json",
    "failedFile": "./data/failed.json"
  },
  "proxy": {
    "enabled": true,
    "provider": "webshare",
    "host": "p.webshare.io",
    "port": 80
  }
}
```

## 使用方式

### 基本匯入

```bash
# 匯入 2019/01/01 到 2019/01/31 的標案資料
npm start import --from 20190101 --to 20190131

# 或使用開發模式
npm run dev import --from 20190101 --to 20190131
```

### 重試失敗項目

```bash
npm start retry
```

### 查看進度狀態

```bash
npm start status
```

## Webshare Proxy

該程式支援使用 Webshare 代理進行 API 請求。要啟用：

1. 在 `WEBSHARE_PROXY_USERNAME` 和 `WEBSHARE_PROXY_PASSWORD` 環境變數中設置你的 Webshare 代理認證資訊
2. 在配置文件中將 `proxy.enabled` 設置為 `true`
3. 配置代理伺服器的主機名稱和端口設定

當代理啟用且環境變數設置後，所有 API 請求將透過 Webshare 代理網路路由。

## 設定檔

修改 `config/config.json` 可調整以下設定：

### API 設定

- `baseUrl`: PCC API 的基本 URL
- `timeout`: 請求超時時間（毫秒）
- `retryAttempts`: 失敗請求的重試次數
- `delayBetweenRequests`: 請求之間的延遲（毫秒）

### 處理設定

- `batchSize`: 每次處理的項目數量
- `concurrency`: 同時執行的工作數量
- `progressSaveInterval`: 進度保存的頻率

### 代理設定

- `enabled`: 是否使用代理
- `provider`: 代理提供者（目前支援 "webshare"）
- `host`: 代理伺服器的主機名稱
- `port`: 代理伺服器的端口

## 環境變數

- `WEBSHARE_PROXY_USERNAME`: 你的 Webshare 代理認證用戶名
- `WEBSHARE_PROXY_PASSWORD`: 你的 Webshare 代理認證密碼
- `WEBSHARE_PROXY_HOST`: 代理伺服器的主機名稱
- `WEBSHARE_PROXY_PORT`: 代理伺服器的端口

## 輸出結構

```

```
