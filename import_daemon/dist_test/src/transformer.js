"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Transformer = exports.FieldMapper = void 0;
var fs = __importStar(require("fs"));
var path = __importStar(require("path"));
var FieldMapper = /** @class */ (function () {
    function FieldMapper() {
        this.unmappedFieldsFile = path.join(process.cwd(), "unmapped-fields.json");
        this.unmappedFields = {};
        this.pendingSave = false;
        this.saveTimeout = null;
        this.isTestEnvironment = process.env.NODE_ENV === "test" || process.env.JEST_WORKER_ID !== undefined;
        this.loadUnmappedFields();
    }
    FieldMapper.prototype.loadUnmappedFields = function () {
        try {
            if (fs.existsSync(this.unmappedFieldsFile)) {
                var data = fs.readFileSync(this.unmappedFieldsFile, "utf8");
                this.unmappedFields = JSON.parse(data);
            }
        }
        catch (error) {
            console.error("Failed to load unmapped fields log:", error);
        }
    };
    FieldMapper.prototype.saveUnmappedFields = function () {
        // 在測試環境中跳過文件寫入
        if (this.isTestEnvironment) {
            return;
        }
        try {
            fs.writeFileSync(this.unmappedFieldsFile, JSON.stringify(this.unmappedFields, null, 2));
        }
        catch (error) {
            console.error("Failed to save unmapped fields log:", error);
        }
    };
    FieldMapper.prototype.debouncedSave = function () {
        var _this = this;
        // 在測試環境中跳過文件寫入
        if (this.isTestEnvironment) {
            return;
        }
        if (this.saveTimeout) {
            clearTimeout(this.saveTimeout);
        }
        this.saveTimeout = setTimeout(function () {
            _this.saveUnmappedFields();
            _this.pendingSave = false;
        }, 1000); // 1秒後保存
    };
    FieldMapper.prototype.recordUnmappedField = function (sourceKey, targetField, sampleValue) {
        var key = "".concat(sourceKey, "->").concat(targetField);
        if (this.unmappedFields[key]) {
            this.unmappedFields[key].recordCount++;
            this.unmappedFields[key].lastSeen = new Date().toISOString();
        }
        else {
            this.unmappedFields[key] = {
                sourceKey: sourceKey,
                targetField: targetField,
                sampleValue: sampleValue,
                recordCount: 1,
                lastSeen: new Date().toISOString(),
            };
        }
        // 使用防抖保存，避免頻繁的文件寫入
        if (!this.pendingSave) {
            this.pendingSave = true;
            this.debouncedSave();
        }
    };
    // 添加手動保存方法，用於測試結束後保存
    FieldMapper.prototype.flushUnmappedFields = function () {
        if (this.saveTimeout) {
            clearTimeout(this.saveTimeout);
        }
        this.saveUnmappedFields();
    };
    /**
     * 計算字符串相似度（簡單版本）
     */
    FieldMapper.prototype.calculateSimilarity = function (str1, str2) {
        // 移除特殊字符，轉為小寫
        var clean1 = str1.replace(/[^\u4e00-\u9fff\w]/g, "").toLowerCase();
        var clean2 = str2.replace(/[^\u4e00-\u9fff\w]/g, "").toLowerCase();
        // 完全匹配
        if (clean1 === clean2)
            return 1.0;
        // 包含匹配
        if (clean1.includes(clean2) || clean2.includes(clean1)) {
            return 0.8;
        }
        // 簡單的字符重疊計算
        var matches = 0;
        var minLen = Math.min(clean1.length, clean2.length);
        for (var i = 0; i < minLen; i++) {
            if (clean1[i] === clean2[i])
                matches++;
        }
        return matches / Math.max(clean1.length, clean2.length);
    };
    /**
     * 找到最佳匹配的目標字段
     */
    FieldMapper.prototype.findBestMatch = function (sourceKey, targetFields) {
        var bestMatch = { field: "", score: 0 };
        for (var _i = 0, targetFields_1 = targetFields; _i < targetFields_1.length; _i++) {
            var targetField = targetFields_1[_i];
            var score = this.calculateSimilarity(sourceKey, targetField);
            if (score > bestMatch.score) {
                bestMatch = { field: targetField, score: score };
            }
        }
        return bestMatch;
    };
    /**
     * 轉換值的類型
     */
    FieldMapper.prototype.convertValue = function (value, targetType) {
        if (value === null || value === undefined)
            return undefined;
        switch (targetType) {
            case "string":
                return String(value);
            case "number":
                if (typeof value === "string") {
                    // 移除非數字字符，保留小數點
                    var numStr = value.replace(/[^\d.-]/g, "");
                    var num = parseFloat(numStr);
                    return isNaN(num) ? 0 : num;
                }
                return Number(value) || 0;
            case "boolean":
                if (typeof value === "string") {
                    return value === "是" || value === "true" || value === "1";
                }
                return Boolean(value);
            case "Date":
                if (typeof value === "string") {
                    // 處理民國年格式 (113/06/25)
                    var rocMatch = value.match(/(\d{2,3})\/(\d{1,2})\/(\d{1,2})/);
                    if (rocMatch) {
                        var year = parseInt(rocMatch[1]) + (rocMatch[1].length === 3 ? 1911 : 2000);
                        var month = parseInt(rocMatch[2]);
                        var day = parseInt(rocMatch[3]);
                        return new Date(year, month - 1, day);
                    }
                    // 處理其他日期格式
                    var date = new Date(value);
                    return isNaN(date.getTime()) ? new Date() : date;
                }
                return new Date(value);
            default:
                return value;
        }
    };
    /**
     * 檢查是否應該覆蓋現有值
     */
    FieldMapper.prototype.shouldOverwriteValue = function (existingValue, newValue, targetType) {
        // 如果沒有現有值，總是覆蓋
        if (existingValue === null || existingValue === undefined) {
            return true;
        }
        // 如果新值是 null 或 undefined，不覆蓋
        if (newValue === null || newValue === undefined) {
            return false;
        }
        // 對於數字類型，如果新值是 0 且現有值不是 0，不覆蓋
        if (targetType === "number") {
            if (newValue === 0 && existingValue !== 0) {
                return false;
            }
        }
        // 對於字符串類型，如果新值是空字符串且現有值不是空，不覆蓋
        if (targetType === "string") {
            if (newValue === "" && existingValue !== "") {
                return false;
            }
        }
        // 其他情況，覆蓋
        return true;
    };
    /**
     * 映射字段到目標對象
     */
    FieldMapper.prototype.mapFields = function (sourceData, targetSchema, targetName) {
        if (targetName === void 0) { targetName = "TenderData"; }
        var result = {};
        var targetFields = Object.keys(targetSchema);
        var _loop_1 = function (sourceKey, sourceValue) {
            if (sourceValue === null || sourceValue === undefined)
                return "continue";
            var mapped = false;
            // 處理帶有冒號的結構化字段 (例如: "機關資料:機關代碼")
            if (sourceKey.includes(":")) {
                var _c = sourceKey.split(":", 2), prefix_1 = _c[0], fieldName_1 = _c[1];
                // 只在對應的嵌套對象中處理這個字段
                if (targetName === prefix_1) {
                    // 直接映射到當前對象中
                    var targetField = targetFields.find(function (field) { return field === fieldName_1; });
                    // 如果沒有精確匹配，嘗試模糊匹配
                    if (!targetField) {
                        var bestMatch = this_1.findBestMatch(fieldName_1, targetFields);
                        if (bestMatch.score > 0.6) {
                            targetField = bestMatch.field;
                        }
                    }
                    if (targetField) {
                        var targetType = this_1.getFieldType(targetSchema[targetField]);
                        var convertedValue = this_1.convertValue(sourceValue, targetType);
                        // 檢查是否應該覆蓋現有值
                        if (this_1.shouldOverwriteValue(result[targetField], convertedValue, targetType)) {
                            result[targetField] = convertedValue;
                        }
                        mapped = true;
                    }
                    else {
                        // 如果找不到匹配，記錄未映射字段並硬塞
                        this_1.recordUnmappedField(sourceKey, targetName, sourceValue);
                        if (this_1.shouldOverwriteValue(result[fieldName_1], sourceValue, "string")) {
                            result[fieldName_1] = sourceValue;
                        }
                        mapped = true;
                    }
                }
                else if (targetName === "TenderData") {
                    // 在頂層對象中，檢查是否有對應的嵌套對象字段
                    var nestedField = targetFields.find(function (field) { return field === prefix_1; });
                    if (nestedField &&
                        targetSchema[nestedField] &&
                        typeof targetSchema[nestedField] === "object") {
                        // 確保嵌套對象存在
                        if (!result[nestedField]) {
                            result[nestedField] = {};
                        }
                        // 獲取嵌套對象的schema
                        var nestedSchema = targetSchema[nestedField];
                        var nestedFields = Object.keys(nestedSchema);
                        // 在嵌套對象中尋找匹配的字段
                        var nestedTargetField = nestedFields.find(function (field) { return field === fieldName_1; });
                        // 如果沒有精確匹配，嘗試模糊匹配
                        if (!nestedTargetField) {
                            var bestMatch = this_1.findBestMatch(fieldName_1, nestedFields);
                            if (bestMatch.score > 0.6) {
                                nestedTargetField = bestMatch.field;
                            }
                        }
                        if (nestedTargetField) {
                            var targetType = this_1.getFieldType(nestedSchema[nestedTargetField]);
                            var convertedValue = this_1.convertValue(sourceValue, targetType);
                            // 檢查是否應該覆蓋嵌套對象中的現有值
                            if (this_1.shouldOverwriteValue(result[nestedField][nestedTargetField], convertedValue, targetType)) {
                                result[nestedField][nestedTargetField] = convertedValue;
                            }
                            mapped = true;
                            // 同時檢查是否也需要映射到頂級字段
                            // 例如：機關資料:機關名稱 -> 機關資料.機關名稱 + 機關名稱
                            var topLevelField = targetFields.find(function (field) { return field === fieldName_1; });
                            if (topLevelField) {
                                var topLevelType = this_1.getFieldType(targetSchema[topLevelField]);
                                var topLevelConvertedValue = this_1.convertValue(sourceValue, topLevelType);
                                // 檢查是否應該覆蓋頂級字段的現有值
                                if (this_1.shouldOverwriteValue(result[topLevelField], topLevelConvertedValue, topLevelType)) {
                                    result[topLevelField] = topLevelConvertedValue;
                                }
                            }
                        }
                        else {
                            // 如果在嵌套對象中找不到匹配，記錄未映射字段
                            this_1.recordUnmappedField(sourceKey, "".concat(targetName, ".").concat(prefix_1), sourceValue);
                            // 仍然硬塞到嵌套對象中
                            if (this_1.shouldOverwriteValue(result[nestedField][fieldName_1], sourceValue, "string")) {
                                result[nestedField][fieldName_1] = sourceValue;
                            }
                            mapped = true;
                        }
                    }
                }
                // 如果不是對應的嵌套對象，也不是頂層對象，則跳過這個字段
                if (!mapped) {
                    return "continue";
                }
            }
            // 如果還沒有映射，嘗試直接映射到當前層級的字段
            if (!mapped) {
                // 1. 精確匹配
                var targetField = targetFields.find(function (field) { return field === sourceKey; });
                // 2. 關鍵詞匹配（移除前綴後匹配）
                if (!targetField) {
                    var cleanSourceKey_1 = sourceKey.includes(":")
                        ? sourceKey.split(":").pop()
                        : sourceKey;
                    targetField = targetFields.find(function (field) { return field === cleanSourceKey_1; });
                }
                // 3. 包含匹配
                if (!targetField) {
                    var cleanSourceKey_2 = sourceKey.includes(":")
                        ? sourceKey.split(":").pop()
                        : sourceKey;
                    targetField = targetFields.find(function (field) {
                        return field.includes(cleanSourceKey_2) || cleanSourceKey_2.includes(field);
                    });
                }
                // 4. 模糊匹配
                if (!targetField) {
                    var bestMatch = this_1.findBestMatch(sourceKey, targetFields);
                    if (bestMatch.score > 0.6) {
                        // 相似度閾值
                        targetField = bestMatch.field;
                    }
                }
                // 5. 如果找到匹配，進行類型轉換並賦值
                if (targetField) {
                    var targetType = this_1.getFieldType(targetSchema[targetField]);
                    var convertedValue = this_1.convertValue(sourceValue, targetType);
                    // 檢查是否應該覆蓋現有值
                    if (this_1.shouldOverwriteValue(result[targetField], convertedValue, targetType)) {
                        result[targetField] = convertedValue;
                    }
                }
                else {
                    // 6. 未找到匹配，記錄到異常檔案並硬塞
                    this_1.recordUnmappedField(sourceKey, targetName, sourceValue);
                    if (this_1.shouldOverwriteValue(result[sourceKey], sourceValue, "string")) {
                        result[sourceKey] = sourceValue; // 硬塞進去
                    }
                }
            }
        };
        var this_1 = this;
        // 遍歷源數據的所有字段
        for (var _i = 0, _a = Object.entries(sourceData); _i < _a.length; _i++) {
            var _b = _a[_i], sourceKey = _b[0], sourceValue = _b[1];
            _loop_1(sourceKey, sourceValue);
        }
        return result;
    };
    /**
     * 獲取字段的預期類型
     */
    FieldMapper.prototype.getFieldType = function (schemaValue) {
        if (schemaValue === String || typeof schemaValue === "string")
            return "string";
        if (schemaValue === Number || typeof schemaValue === "number")
            return "number";
        if (schemaValue === Boolean || typeof schemaValue === "boolean")
            return "boolean";
        if (schemaValue === Date || schemaValue instanceof Date)
            return "Date";
        return "string"; // 默認為字符串
    };
    return FieldMapper;
}());
exports.FieldMapper = FieldMapper;
var Transformer = /** @class */ (function () {
    function Transformer() {
        this.fieldMapper = new FieldMapper();
    }
    /**
     * 將 API 回應轉換成 TenderRecord 格式
     */
    Transformer.prototype.transformTenderDetail = function (response, listRecord) {
        if (!response.records || response.records.length === 0) {
            throw new Error("No tender records found in API response");
        }
        return response.records.map(function (record) {
            if (!record.detail) {
                throw new Error("Missing detail for tender ".concat(record.unit_id, "_").concat(record.job_number));
            }
            return __assign(__assign({}, record), { detail: __assign(__assign({}, record.detail), { fetched_at: record.detail.fetched_at || new Date().toISOString() }) });
        });
    };
    /**
     * 從 ListRecord 創建基本的 TenderRecord 結構
     */
    Transformer.prototype.createBasicTenderRecord = function (listRecord, error) {
        return {
            date: listRecord.date,
            filename: listRecord.filename,
            brief: listRecord.brief,
            job_number: listRecord.job_number,
            unit_id: listRecord.unit_id,
            detail: {
                type: "error",
                url: "",
                機關資料: {
                    機關代碼: listRecord.unit_id,
                    機關名稱: listRecord.unit_name,
                    單位名稱: "",
                    機關地址: "",
                    聯絡人: "",
                    聯絡電話: "",
                    傳真號碼: "",
                },
                已公告資料: {
                    標案案號: listRecord.job_number,
                    招標方式: "",
                    決標方式: "",
                    是否依政府採購法施行細則第64條之2辦理: "",
                    新增公告傳輸次數: "",
                    是否依據採購法第106條第1項第1款辦理: "",
                    標案名稱: listRecord.brief.title,
                    決標資料類別: "",
                    是否屬共同供應契約採購: "",
                    是否屬二以上機關之聯合採購: "",
                    是否複數決標: "",
                    是否共同投標: "",
                    標的分類: listRecord.brief.category || "",
                    是否屬統包: "",
                    是否應依公共工程專業技師簽證規則實施技師簽證: "",
                    開標時間: "",
                    原公告日期: "",
                    採購金額級距: "",
                    辦理方式: "",
                    是否適用條約或協定之採購: {
                        是否適用WTO政府採購協定: "",
                        是否適用臺紐經濟合作協定: "",
                        是否適用臺星經濟夥伴協定: "",
                    },
                    預算金額是否公開: "",
                    預算金額: "",
                    是否受機關補助: "",
                    履約地點: "",
                    履約地點含地區: "",
                    是否含特別預算: "",
                    歸屬計畫類別: "",
                    本案採購契約是否採用主管機關訂定之範本: "",
                    是否屬災區重建工程: "",
                },
                投標廠商: {
                    投標廠商家數: "0",
                },
                決標品項: {
                    決標品項數: "0",
                },
                決標資料: {
                    決標公告序號: "",
                    決標日期: "",
                    決標公告日期: "",
                    是否刊登公報: "",
                    底價金額: "",
                    底價金額是否公開: "",
                    總決標金額: "",
                    總決標金額是否公開: "",
                    契約是否訂有依物價指數調整價金規定: "",
                    履約執行機關: "",
                    附加說明: "Error fetching details: ".concat(error),
                },
                fetched_at: new Date().toISOString(),
            },
            unit_name: listRecord.unit_name,
            unit_api_url: listRecord.unit_api_url,
            tender_api_url: listRecord.tender_api_url,
            unit_url: listRecord.unit_url,
            url: listRecord.url,
        };
    };
    /**
     * 將 TenderRecord 轉換成 TenderData 格式
     */
    Transformer.prototype.transformToTenderData = function (record) {
        var _a;
        var detail = record.detail;
        if (!detail) {
            throw new Error("Missing detail for tender ".concat(record.unit_id, "_").concat(record.job_number));
        }
        // 創建 LegacyTenderData 的模板結構
        var tenderDataSchema = {
            標案編號: "",
            標案名稱: "",
            機關名稱: "",
            預算金額: 0,
            截止投標: new Date(),
            公告日期: new Date(),
            專案編號: "",
            狀態: "bidding",
            標的分類: "",
            履約地點: "",
            標案描述: "",
            AI評分: 0,
            標案類型: "tender",
            網址: "",
            主鍵: "",
            歷史記錄: [],
        };
        // 使用字段映射器映射基本字段
        var mappedData = this.fieldMapper.mapFields(detail, tenderDataSchema, "TenderData");
        // 手動設置一些必要字段，優先使用直接匹配的值
        var tenderData = __assign({ 標案編號: mappedData.標案編號 ||
                (detail === null || detail === void 0 ? void 0 : detail["採購資料:標案案號"]) ||
                "".concat(record.unit_id, "_").concat(record.job_number), 標案名稱: mappedData.標案名稱 ||
                (detail === null || detail === void 0 ? void 0 : detail["採購資料:標案名稱"]) ||
                ((_a = record.brief) === null || _a === void 0 ? void 0 : _a.title) ||
                "", 機關名稱: mappedData.機關名稱 ||
                (detail === null || detail === void 0 ? void 0 : detail["機關資料:機關名稱"]) ||
                (record.unit_name !== "null" ? record.unit_name : "") ||
                "", 預算金額: this.parseBudgetAmount((detail === null || detail === void 0 ? void 0 : detail["採購資料:預算金額"]) ||
                (detail === null || detail === void 0 ? void 0 : detail["已公告資料:預算金額"])) ||
                mappedData.預算金額 ||
                0, 截止投標: mappedData.截止投標 ||
                this.parseDate(detail === null || detail === void 0 ? void 0 : detail["領投開標:截止投標"]) ||
                new Date(), 公告日期: mappedData.公告日期 ||
                this.parseDate(detail === null || detail === void 0 ? void 0 : detail["招標資料:公告日"]) ||
                new Date(), 專案編號: mappedData.專案編號 ||
                (detail === null || detail === void 0 ? void 0 : detail["採購資料:標案案號"]) ||
                "".concat(record.unit_id, "_").concat(record.job_number), 狀態: this.determineStatus(detail.type), 標的分類: (detail === null || detail === void 0 ? void 0 : detail["採購資料:標的分類"]) ||
                (detail === null || detail === void 0 ? void 0 : detail["已公告資料:標的分類"]) ||
                mappedData.標的分類 ||
                "", 履約地點: mappedData.履約地點 || (detail === null || detail === void 0 ? void 0 : detail["其他:履約地點"]) || "", 標案描述: this.generateDescription(record), AI評分: this.calculateAiScore(record), 標案類型: this.determineTenderType(detail.type), 網址: this.generateWebPccUrl(detail), 主鍵: this.extractPkPmsMainFromUrl(detail.url) ||
                detail.pkPmsMain ||
                "", 
            // 新增必要的儲存字段
            projectId: "".concat(record.unit_id, "_").concat(record.job_number), publishDate: this.formatRecordDateToISO(record.date), 歷史記錄: [], 
            // 嵌套對象
            機關資料: this.transformAgencyInfo(detail), 採購資料: this.transformProcurementInfo(detail), 招標資料: this.transformTenderInfo(detail), 領投開標: this.transformBiddingInfo(detail), 其他資料: this.transformOtherInfo(detail), 決標資料: detail.決標資料 ? this.transformAwardInfo(detail) : undefined, 流標資料: detail.type === "無法決標公告"
                ? this.transformFailedTenderInfo(detail)
                : undefined, 處理單位: this.transformHandlingUnits(detail) }, mappedData);
        return tenderData;
    };
    Transformer.prototype.transformAgencyInfo = function (detail) {
        var schema = {
            機關代碼: "",
            機關名稱: "",
            單位名稱: "",
            機關地址: "",
            聯絡人: "",
            聯絡電話: "",
            傳真號碼: "",
            電子郵件信箱: "",
        };
        return this.fieldMapper.mapFields(detail, schema, "機關資料");
    };
    Transformer.prototype.transformProcurementInfo = function (detail) {
        var schema = {
            標案案號: "",
            標案名稱: "",
            標的分類: "",
            採購金額級距: "",
            預算金額: 0,
            預算金額是否公開: false,
            專案編號: "",
            是否為工程採購: false,
            財物採購性質: "",
            辦理方式: "",
            依據法條: "",
            是否適用WTO政府採購協定: false,
            是否適用臺紐經濟合作協定: false,
            是否適用臺星經濟夥伴協定: false,
            是否採用電子競價: false,
            是否為商業財物或服務: false,
            是否屬敏感性或國安疑慮: false,
            是否涉及國家安全: false,
            預估金額: 0,
            預估金額是否公開: false,
            後續擴充: false,
            後續擴充說明: "",
            是否受機關補助: false,
            補助機關: [],
            是否為政策及業務宣導業務: false,
        };
        return this.fieldMapper.mapFields(detail, schema, "採購資料");
    };
    Transformer.prototype.transformTenderInfo = function (detail) {
        var schema = {
            招標方式: "",
            決標方式: "",
            公告傳輸次數: 0,
            招標狀態: "",
            公告日: new Date(),
            原公告日: new Date(),
            是否複數決標: false,
            是否訂有底價: false,
            價格是否納入評選: false,
            所占配分或權重是否為20百分比以上: false,
            是否屬特殊採購: false,
            是否已辦理公開閱覽: false,
            是否屬統包: false,
            功能需求: "",
            是否屬共同供應契約採購: false,
            是否屬聯合採購: false,
            是否需要技師簽證: false,
            是否採行協商措施: false,
            是否適用採購法第104條或105條或招標期限標準第10條或第4條之1: false,
            是否依據採購法第106條第1項第1款辦理: false,
            是否依據政府採購法施行細則第64條之2辦理: false,
            評選委員會名單: "",
        };
        return this.fieldMapper.mapFields(detail, schema, "招標資料");
    };
    Transformer.prototype.transformBiddingInfo = function (detail) {
        var schema = {
            是否提供電子領標: false,
            文件費用: {
                機關文件費: 0,
                系統使用費: 0,
                文件代收費: 0,
                總計: 0,
                指定收款機關: "",
            },
            是否提供現場領標: false,
            是否提供電子投標: false,
            截止投標: new Date(),
            收受投標文件地點: "",
            開標時間: new Date(),
            開標地點: "",
            是否須繳納押標金: false,
            押標金額度: 0,
            投標文字: "",
            是否提供線上押標金: false,
        };
        return this.fieldMapper.mapFields(detail, schema, "領投開標");
    };
    Transformer.prototype.transformOtherInfo = function (detail) {
        var schema = {
            履約地點: "",
            履約期限: "",
            履約地點詳細: "",
            是否依據採購法第99條: false,
            是否刊登公報: false,
            是否採用主管機關範本: false,
            是否訂有物價指數調整條款: false,
            物價指數調整說明: "",
            是否為災後重建: false,
            是否優先身心障礙機構: false,
            是否刊登英文公告: false,
            履約機關: {
                機關代碼: "",
                機關名稱: "",
            },
            執行機關: {
                機關代碼: "",
                機關名稱: "",
            },
            廠商資格摘要: "",
            是否訂有與履約能力有關之基本資格: false,
            廠商基本資格證明文件: "",
            確認無資格限制競爭情形: false,
            附加說明: "",
            疑義異議申訴及檢舉受理單位: {
                疑義異議受理單位: "",
                申訴受理單位: "",
                檢舉受理單位: "",
            },
        };
        return this.fieldMapper.mapFields(detail, schema, "其他資料");
    };
    Transformer.prototype.transformAwardInfo = function (detail) {
        var schema = {
            決標分類: "",
            決標序號: "",
            決標日期: new Date(),
            決標公告日期: new Date(),
            決標公告序號: "",
            決標資料分類: "",
            契約編號: "",
            底價: 0,
            底價是否公開: false,
            決標總金額: 0,
            決標總金額是否公開: false,
            投標廠商家數: 0,
            開標時間: new Date(),
            原公告日期: new Date(),
            採購金額級距: "",
            處理方式: "",
            是否屬敏感性或國安疑慮: false,
            是否涉及國家安全: false,
            預算金額是否公開: false,
            預算金額: 0,
            是否受機關補助: false,
            補助機關: [],
            履約地點: "",
            履約地點詳細: "",
            是否優先身心障礙機構: false,
            是否採用主管機關範本: false,
            是否為政策及業務宣導業務: false,
            投標廠商: [],
            決標品項: [],
        };
        return this.fieldMapper.mapFields(detail, schema, "決標資料");
    };
    Transformer.prototype.transformFailedTenderInfo = function (detail) {
        var schema = {
            廢標序號: "",
            原廢標日期: new Date(),
            廢標公告日期: new Date(),
            廢標理由: "",
            是否續用相同案號: false,
            限制性招標法條依據: "",
            招標方式: "",
            開標時間: new Date(),
            採購金額級距: "",
            預算金額: 0,
            預算金額是否公開: false,
            履約地點: "",
            附加說明: "",
        };
        return this.fieldMapper.mapFields(detail, schema, "流標資料");
    };
    Transformer.prototype.transformHandlingUnits = function (detail) {
        var schema = {
            疑義異議受理單位: {
                名稱: "",
                地址: "",
                電話: "",
                傳真: "",
            },
            申訴受理單位: {
                名稱: "",
                地址: "",
                電話: "",
                傳真: "",
            },
            檢舉受理單位: [
                {
                    名稱: "",
                    地址: "",
                    電話: "",
                    傳真: "",
                    信箱: "",
                },
            ],
        };
        return this.fieldMapper.mapFields(detail, schema, "處理單位");
    };
    // 輔助方法
    Transformer.prototype.calculateAiScore = function (record) {
        var _a, _b, _c, _d, _e;
        var score = 5;
        var budgetStr = ((_a = record.detail) === null || _a === void 0 ? void 0 : _a["採購資料:預算金額"]) ||
            ((_b = record.detail) === null || _b === void 0 ? void 0 : _b["已公告資料:預算金額"]) ||
            ((_d = (_c = record.detail) === null || _c === void 0 ? void 0 : _c.已公告資料) === null || _d === void 0 ? void 0 : _d.預算金額) ||
            "";
        var budget = parseInt(budgetStr.replace(/[^\d]/g, "")) || 0;
        if (budget > 1000000)
            score += 2;
        else if (budget > 100000)
            score += 1;
        var title = ((_e = record.brief) === null || _e === void 0 ? void 0 : _e.title) || "";
        if (title.includes("資訊") ||
            title.includes("系統") ||
            title.includes("軟體")) {
            score += 1;
        }
        return Math.min(10, Math.max(1, score));
    };
    Transformer.prototype.determineStatus = function (type) {
        if (type.includes("決標"))
            return "awarded";
        if (type.includes("無法決標"))
            return "failed";
        if (type.includes("公開招標"))
            return "open";
        return "bidding";
    };
    Transformer.prototype.generateDescription = function (record) {
        var _a, _b, _c, _d;
        var parts = [];
        var title = ((_a = record.brief) === null || _a === void 0 ? void 0 : _a.title) || "";
        if (title)
            parts.push(title);
        var method = ((_b = record.detail) === null || _b === void 0 ? void 0 : _b["招標資料:招標方式"]) || "";
        if (method)
            parts.push("\u62DB\u6A19\u65B9\u5F0F: ".concat(method));
        var budget = ((_c = record.detail) === null || _c === void 0 ? void 0 : _c["採購資料:預算金額"]) ||
            ((_d = record.detail) === null || _d === void 0 ? void 0 : _d["已公告資料:預算金額"]) ||
            "";
        if (budget)
            parts.push("\u9810\u7B97: ".concat(budget));
        return parts.join(" | ");
    };
    Transformer.prototype.generateWebPccUrl = function (detail) {
        if (detail.url && detail.url.includes("web.pcc.gov.tw")) {
            return detail.url;
        }
        var pkPmsMain = detail.pkPmsMain || this.extractPkPmsMainFromUrl(detail.url);
        if (pkPmsMain) {
            return "https://web.pcc.gov.tw/tps/QueryTender/query/searchTenderDetail?pkPmsMain=".concat(encodeURIComponent(pkPmsMain));
        }
        return detail.url || "";
    };
    Transformer.prototype.extractPkPmsMainFromUrl = function (url) {
        if (!url)
            return undefined;
        var match = url.match(/pkPmsMain=([^&]+)/);
        return match ? decodeURIComponent(match[1]) : undefined;
    };
    /**
     * 將多筆 TenderRecord 合併成一個 TenderData
     */
    Transformer.prototype.mergeRecordsToTenderData = function (records) {
        var _this = this;
        if (!records || records.length === 0) {
            throw new Error("No records to merge");
        }
        var sortedRecords = records.sort(function (a, b) { return b.date - a.date; });
        var latestRecord = sortedRecords[0];
        var tenderData = this.transformToTenderData(latestRecord);
        // 確保必要字段存在
        if (!tenderData.projectId || tenderData.projectId === "undefined") {
            tenderData.projectId = "".concat(latestRecord.unit_id, "_").concat(latestRecord.job_number);
            console.warn("Fixed missing projectId:", tenderData.projectId);
        }
        if (!tenderData.publishDate || tenderData.publishDate === "undefined") {
            tenderData.publishDate = this.formatRecordDateToISO(latestRecord.date);
            console.warn("Fixed missing publishDate:", tenderData.publishDate);
        }
        // 建立歷史記錄
        var history = sortedRecords
            .reverse()
            .map(function (record) { return _this.createHistoryRecord(record); });
        tenderData.歷史記錄 = history;
        return tenderData;
    };
    Transformer.prototype.createHistoryRecord = function (record) {
        var detail = record.detail;
        var tenderType = this.determineTenderType((detail === null || detail === void 0 ? void 0 : detail.type) || "");
        var status = this.determineStatus((detail === null || detail === void 0 ? void 0 : detail.type) || "");
        return {
            date: this.formatRecordDate(record.date),
            status: status,
            tenderType: tenderType,
            metadata: {
                filename: record.filename,
                recordDate: record.date,
                fetchedAt: detail === null || detail === void 0 ? void 0 : detail.fetched_at,
            },
        };
    };
    Transformer.prototype.formatRecordDate = function (date) {
        var dateStr = date.toString();
        var year = dateStr.substring(0, 4);
        var month = dateStr.substring(4, 6);
        var day = dateStr.substring(6, 8);
        return "".concat(year, "-").concat(month, "-").concat(day);
    };
    Transformer.prototype.formatRecordDateToISO = function (date) {
        var dateStr = date.toString();
        if (dateStr.length !== 8) {
            // 如果日期格式不正確，返回當前日期的 ISO 字串
            return new Date().toISOString();
        }
        var year = dateStr.substring(0, 4);
        var month = dateStr.substring(4, 6);
        var day = dateStr.substring(6, 8);
        try {
            var dateObj = new Date("".concat(year, "-").concat(month, "-").concat(day));
            return dateObj.toISOString();
        }
        catch (error) {
            // 如果日期無效，返回當前日期的 ISO 字串
            return new Date().toISOString();
        }
    };
    /**
     * 解析預算金額字串，將 "193,200元" 轉換為數字
     */
    Transformer.prototype.parseBudgetAmount = function (budgetStr) {
        if (!budgetStr || typeof budgetStr !== "string")
            return 0;
        // 移除所有非數字字符（除了小數點）
        var cleanStr = budgetStr.replace(/[^\d.]/g, "");
        var amount = parseFloat(cleanStr);
        return isNaN(amount) ? 0 : amount;
    };
    /**
     * 解析日期字串，支援多種格式
     */
    Transformer.prototype.parseDate = function (dateStr) {
        if (!dateStr || typeof dateStr !== "string")
            return new Date();
        try {
            // 如果是 ISO 格式或標準日期格式
            var date = new Date(dateStr);
            if (!isNaN(date.getTime())) {
                return date;
            }
            // 嘗試解析民國年格式 (113/12/31)
            var rocMatch = dateStr.match(/(\d{2,3})\/(\d{1,2})\/(\d{1,2})/);
            if (rocMatch) {
                var year = parseInt(rocMatch[1]) + (rocMatch[1].length === 3 ? 1911 : 2000);
                var month = parseInt(rocMatch[2]) - 1; // JavaScript months are 0-based
                var day = parseInt(rocMatch[3]);
                return new Date(year, month, day);
            }
            // 嘗試解析其他格式 (2024-12-31, 2024/12/31)
            var standardMatch = dateStr.match(/(\d{4})[-\/](\d{1,2})[-\/](\d{1,2})/);
            if (standardMatch) {
                var year = parseInt(standardMatch[1]);
                var month = parseInt(standardMatch[2]) - 1;
                var day = parseInt(standardMatch[3]);
                return new Date(year, month, day);
            }
            return new Date();
        }
        catch (error) {
            return new Date();
        }
    };
    // 保持原有的其他方法
    Transformer.prototype.validateTenderRecord = function (record) {
        try {
            if (!record.unit_id || !record.job_number)
                return false;
            if (!record.detail)
                return false;
            return true;
        }
        catch (error) {
            console.error("Validation error:", error);
            return false;
        }
    };
    Transformer.prototype.cleanTenderRecord = function (record) {
        var _a;
        var cleanString = function (str) { var _a; return ((_a = str === null || str === void 0 ? void 0 : str.trim) === null || _a === void 0 ? void 0 : _a.call(str)) || ""; };
        var cleaned = JSON.parse(JSON.stringify(record));
        if ((_a = cleaned.detail) === null || _a === void 0 ? void 0 : _a.機關資料) {
            Object.keys(cleaned.detail.機關資料).forEach(function (key) {
                if (typeof cleaned.detail.機關資料[key] === "string") {
                    cleaned.detail.機關資料[key] = cleanString(cleaned.detail.機關資料[key]);
                }
            });
        }
        return cleaned;
    };
    Transformer.prototype.isValidTenderType = function (record) {
        var _a;
        var validTypes = [
            "公開招標公告",
            "決標公告",
            "無法決標公告",
            "更正公告",
            "撤銷公告",
        ];
        return validTypes.includes(((_a = record.detail) === null || _a === void 0 ? void 0 : _a.type) || "");
    };
    Transformer.prototype.extractKeyInfo = function (record) {
        var _a, _b, _c, _d;
        return {
            id: "".concat(record.unit_id, "_").concat(record.job_number),
            title: ((_a = record.brief) === null || _a === void 0 ? void 0 : _a.title) || "Unknown",
            agency: record.unit_name || "Unknown",
            amount: ((_b = record.detail) === null || _b === void 0 ? void 0 : _b["採購資料:預算金額"]) ||
                ((_c = record.detail) === null || _c === void 0 ? void 0 : _c["已公告資料:預算金額"]) ||
                "Unknown",
            type: ((_d = record.detail) === null || _d === void 0 ? void 0 : _d.type) || "Unknown",
            publishDate: record.date.toString(),
        };
    };
    /**
     * 將 TenderRecord 轉換成新的標準化 TenderData 格式
     * 適合進入 PostgreSQL 數據庫並進行分析
     */
    Transformer.prototype.transformToStandardTenderData = function (record) {
        var _a;
        var detail = record.detail;
        if (!detail) {
            throw new Error("Missing detail for record: ".concat(record.unit_id, "_").concat(record.job_number));
        }
        var now = new Date();
        // Extract category information
        var fullCategory = (detail === null || detail === void 0 ? void 0 : detail["採購資料:標的分類"]) ||
            (detail === null || detail === void 0 ? void 0 : detail["已公告資料:標的分類"]) ||
            "";
        var categoryMatch = fullCategory.match(/<(.+?)>(\d+)(.+)/);
        var categoryType = this.determineCategoryType(fullCategory);
        var categoryCode = categoryMatch ? categoryMatch[2] : "";
        // Extract budget amount
        var budgetStr = (detail === null || detail === void 0 ? void 0 : detail["採購資料:預算金額"]) ||
            (detail === null || detail === void 0 ? void 0 : detail["已公告資料:預算金額"]) ||
            "";
        var budgetAmount = this.parseBudgetAmount(budgetStr);
        // Extract dates
        var announcementDate = this.parseDate(detail === null || detail === void 0 ? void 0 : detail["招標資料:公告日"]) || new Date(record.date);
        var submissionDeadline = this.parseDate(detail === null || detail === void 0 ? void 0 : detail["領投開標:截止投標"]);
        var openingTime = this.parseDate(detail === null || detail === void 0 ? void 0 : detail["領投開標:開標時間"]);
        return {
            // === Primary Identifiers ===
            tender_id: "".concat(record.unit_id, "_").concat(record.job_number),
            case_number: (detail === null || detail === void 0 ? void 0 : detail["採購資料:標案案號"]) || "".concat(record.unit_id, "_").concat(record.job_number),
            project_id: "".concat(record.unit_id, "_").concat(record.job_number),
            pcc_main_key: detail.pkPmsMain || "",
            // === Basic Information ===
            title: (detail === null || detail === void 0 ? void 0 : detail["採購資料:標案名稱"]) || ((_a = record.brief) === null || _a === void 0 ? void 0 : _a.title) || "Unknown",
            description: this.generateDescription(record),
            category: fullCategory,
            category_code: categoryCode,
            category_type: categoryType,
            // === Agency Information ===
            agency_code: record.unit_id || "",
            agency_name: (detail === null || detail === void 0 ? void 0 : detail["機關資料:機關名稱"]) || record.unit_name || "",
            agency_unit: (detail === null || detail === void 0 ? void 0 : detail["機關資料:單位名稱"]) || "",
            agency_address: (detail === null || detail === void 0 ? void 0 : detail["機關資料:機關地址"]) || "",
            contact_person: (detail === null || detail === void 0 ? void 0 : detail["機關資料:聯絡人"]) || "",
            contact_phone: (detail === null || detail === void 0 ? void 0 : detail["機關資料:聯絡電話"]) || "",
            contact_fax: (detail === null || detail === void 0 ? void 0 : detail["機關資料:傳真號碼"]) || "",
            contact_email: (detail === null || detail === void 0 ? void 0 : detail["機關資料:電子郵件信箱"]) || "",
            // === Financial Information ===
            budget_amount: budgetAmount,
            budget_disclosed: this.parseBoolean(detail === null || detail === void 0 ? void 0 : detail["採購資料:預算金額是否公開"]),
            budget_range: (detail === null || detail === void 0 ? void 0 : detail["採購資料:採購金額級距"]) || "",
            award_amount: undefined,
            award_disclosed: undefined,
            reserve_price: undefined,
            reserve_disclosed: undefined,
            // === Dates ===
            announcement_date: announcementDate,
            original_announcement_date: undefined,
            submission_deadline: submissionDeadline,
            opening_time: openingTime,
            award_date: undefined,
            // === Status and Type ===
            status: this.determineStatus(detail.type),
            tender_type: this.determineTenderType(detail.type),
            procurement_method: (detail === null || detail === void 0 ? void 0 : detail["招標資料:招標方式"]) || "",
            award_method: (detail === null || detail === void 0 ? void 0 : detail["招標資料:決標方式"]) || "",
            // === Location and Performance ===
            performance_location: (detail === null || detail === void 0 ? void 0 : detail["其他:履約地點"]) || "",
            performance_location_detail: "",
            performance_period: (detail === null || detail === void 0 ? void 0 : detail["其他:履約期限"]) || "",
            // === Compliance and Regulations ===
            is_wto_gpa: this.parseBoolean(detail === null || detail === void 0 ? void 0 : detail["採購資料:是否適用條約或協定之採購:是否適用WTO政府採購協定(GPA)"]),
            is_anztec: this.parseBoolean(detail === null || detail === void 0 ? void 0 : detail["採購資料:是否適用條約或協定之採購:是否適用臺紐經濟合作協定(ANZTEC)"]),
            is_astep: this.parseBoolean(detail === null || detail === void 0 ? void 0 : detail["採購資料:是否適用條約或協定之採購:是否適用臺星經濟夥伴協定(ASTEP)"]),
            is_multiple_award: this.parseBoolean(detail === null || detail === void 0 ? void 0 : detail["招標資料:是否複數決標"]),
            is_joint_procurement: this.parseBoolean(detail === null || detail === void 0 ? void 0 : detail["招標資料:是否屬二以上機關之聯合採購(不適用共同供應契約規定)"]),
            is_electronic_bidding: this.parseBoolean(detail === null || detail === void 0 ? void 0 : detail["領投開標:是否提供電子投標"]),
            requires_deposit: this.parseBoolean(detail === null || detail === void 0 ? void 0 : detail["領投開標:是否須繳納押標金"]),
            deposit_amount: this.parseNumber(detail === null || detail === void 0 ? void 0 : detail["領投開標:是否須繳納押標金:押標金額度"]),
            // === Technical Requirements ===
            is_turnkey: this.parseBoolean(detail === null || detail === void 0 ? void 0 : detail["招標資料:是否屬統包"]),
            requires_engineer_cert: this.parseBoolean(detail === null || detail === void 0 ? void 0 : detail["招標資料:是否應依公共工程專業技師簽證規則實施技師簽證"]),
            is_special_procurement: this.parseBoolean(detail === null || detail === void 0 ? void 0 : detail["招標資料:是否屬特殊採購"]),
            // === Metadata ===
            ai_score: this.calculateAiScore(record),
            source_url: detail.url || "",
            publish_date: new Date(record.date),
            created_at: now,
            updated_at: now,
            data_version: "1.0",
            // === Bidding Information ===
            bidder_count: undefined,
            document_fee: this.parseNumber(detail === null || detail === void 0 ? void 0 : detail["領投開標:是否提供電子領標:機關文件費(機關實收)"]),
            // === Additional Flags ===
            is_government_subsidy: this.parseBoolean(detail === null || detail === void 0 ? void 0 : detail["採購資料:是否受機關補助"]),
            is_disaster_reconstruction: false, // TODO: 需要從其他字段推斷
            is_sensitive_security: this.parseBoolean(detail === null || detail === void 0 ? void 0 : detail["採購資料:本採購是否屬「具敏感性或國安(含資安)疑慮之業務範疇」採購"]),
            is_published_gazette: this.parseBoolean(detail === null || detail === void 0 ? void 0 : detail["其他:是否刊登公報"]),
            // === History and Tracking ===
            history: [{
                    date: record.date.toString(),
                    status: this.determineStatus(detail.type),
                    tenderType: this.determineTenderType(detail.type),
                    metadata: {
                        filename: record.filename || "",
                        recordDate: parseInt(record.date.toString().replace(/-/g, "")),
                        fetchedAt: detail.fetched_at || new Date().toISOString()
                    }
                }],
            // === Related Data ===
            bidders: [],
            award_items: []
        };
    };
    /**
     * 輔助方法：確定採購類型
     */
    Transformer.prototype.determineCategoryType = function (category) {
        if (category.includes('工程類'))
            return 'construction';
        if (category.includes('勞務類'))
            return 'services';
        if (category.includes('財物類'))
            return 'goods';
        return 'other';
    };
    /**
     * 輔助方法：確定標案類型
     */
    Transformer.prototype.determineTenderType = function (type) {
        if (type.includes('決標'))
            return 'award';
        if (type.includes('流標') || type.includes('廢標'))
            return 'failure';
        if (type.includes('更正') || type.includes('變更'))
            return 'amendment';
        if (type.includes('取消'))
            return 'cancellation';
        return 'tender';
    };
    /**
     * 輔助方法：解析布林值
     */
    Transformer.prototype.parseBoolean = function (value) {
        if (!value)
            return false;
        return value === '是' || value === 'Y' || value === 'true' || value === '1';
    };
    /**
     * 輔助方法：解析數字
     */
    Transformer.prototype.parseNumber = function (value) {
        if (!value || typeof value !== "string")
            return undefined;
        var num = parseInt(value.replace(/[^\d]/g, ""));
        return isNaN(num) ? undefined : num;
    };
    return Transformer;
}());
exports.Transformer = Transformer;
