#!/usr/bin/env node
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var transformer_1 = require("./src/transformer");
var fs = __importStar(require("fs-extra"));
var path = __importStar(require("path"));
function testNewStandardFormat() {
    return __awaiter(this, void 0, void 0, function () {
        var transformer, outputDataPath, legacyTenderData, testRecord, legacyData, standardData, latestHistory, outputPath;
        var _a, _b, _c, _d;
        return __generator(this, function (_e) {
            switch (_e.label) {
                case 0:
                    console.log("🧪 Testing New Standardized TenderData Format");
                    console.log("=".repeat(50));
                    transformer = new transformer_1.Transformer();
                    outputDataPath = path.join(__dirname, "../data/output/2019/01/5.4.36_10714-A.json");
                    if (!fs.existsSync(outputDataPath)) {
                        console.error("❌ Test data file not found:", outputDataPath);
                        return [2 /*return*/];
                    }
                    return [4 /*yield*/, fs.readJson(outputDataPath)];
                case 1:
                    legacyTenderData = _e.sent();
                    console.log("\uD83D\uDCCA Loaded legacy tender data: ".concat(legacyTenderData.標案名稱));
                    testRecord = {
                        unit_id: legacyTenderData.機關資料.機關代碼,
                        unit_name: legacyTenderData.機關資料.機關名稱,
                        job_number: legacyTenderData.標案編號.split('_')[1] || 'test',
                        date: 20190102,
                        unit_api_url: "",
                        tender_api_url: "",
                        unit_url: "",
                        url: legacyTenderData.網址,
                        brief: {
                            type: "決標公告",
                            title: legacyTenderData.標案名稱,
                            companies: {
                                ids: [],
                                names: [],
                                id_key: {},
                                name_key: {}
                            }
                        },
                        detail: legacyTenderData.機關資料,
                        filename: "test-file"
                    };
                    console.log("\n\uD83D\uDD0D Testing record: ".concat(((_a = testRecord.brief) === null || _a === void 0 ? void 0 : _a.title) || 'Unknown'));
                    // Legacy format
                    console.log("\n📋 Legacy Format (LegacyTenderData):");
                    legacyData = transformer.transformToTenderData(testRecord);
                    console.log("- 標案編號:", legacyData.標案編號);
                    console.log("- 標案名稱:", legacyData.標案名稱);
                    console.log("- 機關名稱:", legacyData.機關名稱);
                    console.log("- 預算金額:", ((_b = legacyData.預算金額) === null || _b === void 0 ? void 0 : _b.toLocaleString()) || 'N/A');
                    console.log("- 標的分類:", legacyData.標的分類);
                    // New standardized format
                    console.log("\n🆕 New Standardized Format (TenderData):");
                    standardData = transformer.transformToStandardTenderData(testRecord);
                    console.log("- tender_id:", standardData.tender_id);
                    console.log("- title:", standardData.title);
                    console.log("- agency_name:", standardData.agency_name);
                    console.log("- budget_amount:", ((_c = standardData.budget_amount) === null || _c === void 0 ? void 0 : _c.toLocaleString()) || 'N/A');
                    console.log("- category:", standardData.category);
                    console.log("- category_type:", standardData.category_type);
                    console.log("- status:", standardData.status);
                    console.log("- tender_type:", standardData.tender_type);
                    // Show database-friendly structure
                    console.log("\n🗄️ Database-Friendly Fields:");
                    console.log("- agency_code:", standardData.agency_code);
                    console.log("- project_id:", standardData.project_id);
                    console.log("- pcc_main_key:", standardData.pcc_main_key);
                    console.log("- announcement_date:", standardData.announcement_date);
                    console.log("- submission_deadline:", standardData.submission_deadline);
                    console.log("- performance_location:", standardData.performance_location);
                    // Show analytics-friendly structure
                    console.log("\n📊 Analytics-Friendly Fields:");
                    console.log("- procurement_method:", standardData.procurement_method);
                    console.log("- award_method:", standardData.award_method);
                    console.log("- is_multiple_award:", standardData.is_multiple_award);
                    console.log("- is_wto_gpa:", standardData.is_wto_gpa);
                    console.log("- reserve_price:", standardData.reserve_price);
                    // Show history tracking
                    console.log("\n📈 History Tracking:");
                    console.log("- history count:", ((_d = standardData.history) === null || _d === void 0 ? void 0 : _d.length) || 0);
                    if (standardData.history && standardData.history.length > 0) {
                        latestHistory = standardData.history[0];
                        console.log("- latest_record_date:", latestHistory.date);
                        console.log("- latest_status:", latestHistory.status);
                        console.log("- latest_tender_type:", latestHistory.tenderType);
                    }
                    outputPath = path.join(__dirname, "sample_new_format.json");
                    return [4 /*yield*/, fs.writeJson(outputPath, standardData, { spaces: 2 })];
                case 2:
                    _e.sent();
                    console.log("\n\uD83D\uDCBE Sample new format saved to: ".concat(outputPath));
                    console.log("\n✅ New standardized format test completed!");
                    console.log("\n🎯 Benefits of the new format:");
                    console.log("  • English field names for international compatibility");
                    console.log("  • Snake_case naming for database consistency");
                    console.log("  • Proper TypeScript typing for better development experience");
                    console.log("  • Optimized for PostgreSQL analytics and cross-analysis");
                    console.log("  • Normalized structure for better data integrity");
                    console.log("  • Clear separation of concerns (basic info, agency, procurement, etc.)");
                    return [2 /*return*/];
            }
        });
    });
}
// Run the test
testNewStandardFormat().catch(console.error);
