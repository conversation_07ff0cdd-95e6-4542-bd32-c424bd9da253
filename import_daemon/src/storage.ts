import * as fs from "fs-extra";
import * as path from "path";
import { format } from "date-fns";
import {
  TenderRecord,
  ProgressData,
  FailedItem,
  Config,
  TenderData,
} from "./types";

export class Storage {
  private config: Config;

  constructor(config: Config) {
    this.config = config;
  }

  async ensureDirectories(): Promise<void> {
    await fs.ensureDir(this.config.storage.outputDir);
    await fs.ensureDir(path.dirname(this.config.storage.progressFile));
    await fs.ensureDir(path.dirname(this.config.storage.failedFile));
  }

  /**
   * 保存 TenderData 格式的檔案
   * 使用 unique_key 作為檔名，根據 publishDate 組織目錄結構
   */
  async saveTenderData(tenderData: TenderData): Promise<void> {
    try {
      // 檢查必要字段
      if (!tenderData.unique_key) {
        throw new Error(`Missing unique_key for tender data`);
      }

      if (!tenderData.publish_date) {
        console.warn(
          "Invalid publish_date:",
          tenderData.publish_date,
          "using current date"
        );
        tenderData.publish_date = new Date();
      }

      // 解析發布日期來決定存檔路徑
      const { year, month } = this.parseDateFromString(
        tenderData.publish_date.toISOString()
      );

      const dirPath = path.join(this.config.storage.outputDir, year, month);
      await fs.ensureDir(dirPath);

      const filename = `${tenderData.unique_key}.json`;
      const filePath = path.join(dirPath, filename);

      // 直接儲存，因為每個 unique_key 都是唯一的
      await fs.writeFile(filePath, JSON.stringify(tenderData, null, 2), "utf8");
      console.log(`✓ Saved: ${filename}`);
    } catch (error) {
      console.error(
        `✗ Failed to save tender data ${tenderData.unique_key || "unknown"}:`,
        error
      );
      throw error;
    }
  }

  /**
   * 從 ISO 日期字串解析年月
   */
  private parseDateFromString(dateStr: string): {
    year: string;
    month: string;
  } {
    try {
      // 檢查輸入是否為空或無效
      if (!dateStr || dateStr === "undefined" || dateStr === "null") {
        console.warn("Invalid date string provided:", dateStr);
        const now = new Date();
        return {
          year: now.getFullYear().toString(),
          month: (now.getMonth() + 1).toString().padStart(2, "0"),
        };
      }

      const date = new Date(dateStr);

      // 檢查日期是否有效
      if (isNaN(date.getTime())) {
        console.warn("Invalid date created from string:", dateStr);
        const now = new Date();
        return {
          year: now.getFullYear().toString(),
          month: (now.getMonth() + 1).toString().padStart(2, "0"),
        };
      }

      const year = date.getFullYear().toString();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");

      return { year, month };
    } catch (error) {
      console.warn("Failed to parse date string:", dateStr, error);
      // 如果解析失敗，使用當前日期
      const now = new Date();
      return {
        year: now.getFullYear().toString(),
        month: (now.getMonth() + 1).toString().padStart(2, "0"),
      };
    }
  }

  // 保留原有的 saveTender 方法以向後相容
  async saveTender(tender: TenderRecord): Promise<void> {
    try {
      // 使用原公告日期決定存檔路徑，如果沒有就用 date
      const publishDate = this.extractPublishDate(tender);
      const { year, month } = this.parseDate(publishDate);

      const dirPath = path.join(this.config.storage.outputDir, year, month);
      await fs.ensureDir(dirPath);

      const filename = `${tender.unit_id}_${tender.job_number}_${tender.date}.json`;
      const filePath = path.join(dirPath, filename);

      await fs.writeFile(filePath, JSON.stringify(tender, null, 2), "utf8");
      console.log(`✓ Saved: ${filename}`);
    } catch (error) {
      console.error(
        `✗ Failed to save tender ${tender.unit_id}_${tender.job_number}:`,
        error
      );
      throw error;
    }
  }

  async saveProgress(progress: ProgressData): Promise<void> {
    try {
      // Convert Sets to Arrays for JSON serialization
      const serializable = {
        ...progress,
        processedIds: Array.from(progress.processedIds),
        pendingIds: Array.from(progress.pendingIds),
      };

      await fs.writeFile(
        this.config.storage.progressFile,
        JSON.stringify(serializable, null, 2),
        "utf8"
      );
    } catch (error) {
      console.error("Failed to save progress:", error);
      throw error;
    }
  }

  async loadProgress(): Promise<ProgressData | null> {
    try {
      if (!(await fs.pathExists(this.config.storage.progressFile))) {
        return null;
      }

      const data = await fs.readFile(this.config.storage.progressFile, "utf8");
      const parsed = JSON.parse(data);

      // Convert Arrays back to Sets
      return {
        ...parsed,
        processedIds: new Set(parsed.processedIds || []),
        pendingIds: new Set(parsed.pendingIds || []),
      };
    } catch (error) {
      console.error("Failed to load progress:", error);
      return null;
    }
  }

  async saveFailedItems(failedItems: FailedItem[]): Promise<void> {
    try {
      await fs.writeFile(
        this.config.storage.failedFile,
        JSON.stringify(failedItems, null, 2),
        "utf8"
      );
    } catch (error) {
      console.error("Failed to save failed items:", error);
      throw error;
    }
  }

  async loadFailedItems(): Promise<FailedItem[]> {
    try {
      if (!(await fs.pathExists(this.config.storage.failedFile))) {
        return [];
      }

      const data = await fs.readFile(this.config.storage.failedFile, "utf8");
      return JSON.parse(data);
    } catch (error) {
      console.error("Failed to load failed items:", error);
      return [];
    }
  }

  async clearProgress(): Promise<void> {
    try {
      if (await fs.pathExists(this.config.storage.progressFile)) {
        await fs.remove(this.config.storage.progressFile);
      }
    } catch (error) {
      console.error("Failed to clear progress:", error);
    }
  }

  async clearFailedItems(): Promise<void> {
    try {
      if (await fs.pathExists(this.config.storage.failedFile)) {
        await fs.remove(this.config.storage.failedFile);
      }
    } catch (error) {
      console.error("Failed to clear failed items:", error);
    }
  }

  private extractPublishDate(tender: TenderRecord): number {
    // 嘗試從詳細資料中提取原公告日期
    try {
      const originalDate = tender.detail?.已公告資料?.原公告日期;
      if (originalDate) {
        // 轉換民國年格式 (107/10/19) 到西元年格式 (20181019)
        const rocDate = originalDate.split("/");
        if (rocDate.length === 3) {
          const year = parseInt(rocDate[0]) + 1911;
          const month = rocDate[1].padStart(2, "0");
          const day = rocDate[2].padStart(2, "0");
          return parseInt(`${year}${month}${day}`);
        }
      }
    } catch (error) {
      console.warn("Failed to extract publish date, using tender date:", error);
    }

    // 如果無法提取原公告日期，使用 tender.date
    return tender.date;
  }

  private parseDate(dateNumber: number): { year: string; month: string } {
    const dateStr = dateNumber.toString();
    const year = dateStr.substring(0, 4);
    const month = dateStr.substring(4, 6);
    return { year, month };
  }

  generateUniqueId(unitId: string, jobNumber: string): string {
    return `${unitId}_${jobNumber}`;
  }

  async getStorageStats(): Promise<{
    totalFiles: number;
    totalSize: number;
    oldestFile: Date | null;
    newestFile: Date | null;
  }> {
    try {
      let totalFiles = 0;
      let totalSize = 0;
      let oldestFile: Date | null = null;
      let newestFile: Date | null = null;

      const walk = async (dir: string) => {
        if (!(await fs.pathExists(dir))) return;

        const files = await fs.readdir(dir);
        for (const file of files) {
          const fullPath = path.join(dir, file);
          const stat = await fs.stat(fullPath);

          if (stat.isDirectory()) {
            await walk(fullPath);
          } else if (path.extname(file) === ".json") {
            totalFiles++;
            totalSize += stat.size;

            if (!oldestFile || stat.mtime < oldestFile) {
              oldestFile = stat.mtime;
            }
            if (!newestFile || stat.mtime > newestFile) {
              newestFile = stat.mtime;
            }
          }
        }
      };

      await walk(this.config.storage.outputDir);

      return {
        totalFiles,
        totalSize,
        oldestFile,
        newestFile,
      };
    } catch (error) {
      console.error("Failed to get storage stats:", error);
      return {
        totalFiles: 0,
        totalSize: 0,
        oldestFile: null,
        newestFile: null,
      };
    }
  }
}
