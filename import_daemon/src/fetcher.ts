import axios, { AxiosInstance, AxiosResponse } from "axios";
import { ListByDateResponse, TenderDetailResponse, Config } from "./types";
import { HttpsProxyAgent } from "https-proxy-agent";

export class Fetcher {
  private client: AxiosInstance;
  private config: Config;

  constructor(config: Config) {
    this.config = config;

    // Prepare axios config
    const axiosConfig: any = {
      baseURL: config.api.baseUrl,
      timeout: config.api.timeout,
      headers: {
        accept: "application/json, text/javascript, */*; q=0.01",
        "accept-language": "zh-TW,zh;q=0.9,en-US;q=0.8,en;q=0.7",
        "sec-ch-ua":
          '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"macOS"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "cross-site",
        referrer: "https://openfunltd.github.io/",
        referrerPolicy: "strict-origin-when-cross-origin",
      },
    };

    // Add proxy support if webshare credentials are available
    const proxyUsername = process.env.WEBSHARE_PROXY_USERNAME;
    const proxyPassword = process.env.WEBSHARE_PROXY_PASSWORD;
    const proxyHost = process.env.WEBSHARE_PROXY_HOST;
    const proxyPort = process.env.WEBSHARE_PROXY_PORT;

    if (proxyUsername && proxyHost && proxyPort) {
      const proxyUrl = `http://${proxyUsername}:${
        proxyPassword || ""
      }@${proxyHost}:${proxyPort}`;
      const proxyAgent = new HttpsProxyAgent(proxyUrl);

      axiosConfig.httpsAgent = proxyAgent;
      axiosConfig.httpAgent = proxyAgent;

      console.log(
        `🌐 Using webshare proxy: ${proxyUsername}@${proxyHost}:${proxyPort}`
      );
    } else if (proxyUsername) {
      console.warn(
        "⚠️ WEBSHARE_PROXY_USERNAME is set but missing WEBSHARE_PROXY_HOST or WEBSHARE_PROXY_PORT"
      );
    }

    this.client = axios.create(axiosConfig);

    // 添加響應攔截器來處理錯誤
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response) {
          // 服務器響應了錯誤狀態碼
          console.error(
            `API Error: ${error.response.status} - ${error.response.statusText}`
          );
          if (error.response.data) {
            console.error("Error details:", error.response.data);
          }
        } else if (error.request) {
          // 請求被發出但沒有收到響應
          console.error("No response received:", error.request);
        } else {
          // 設置請求時發生了錯誤
          console.error("Request setup error:", error.message);
        }
        return Promise.reject(error);
      }
    );
  }

  async fetchListByDate(date: string): Promise<ListByDateResponse> {
    return this.withRetry(async () => {
      const response: AxiosResponse<ListByDateResponse> = await this.client.get(
        `/listbydate?date=${date}`
      );
      return response.data;
    }, `fetchListByDate(${date})`);
  }

  async fetchTenderDetail(
    unitId: string,
    jobNumber: string
  ): Promise<TenderDetailResponse> {
    return this.withRetry(async () => {
      const response: AxiosResponse<TenderDetailResponse> =
        await this.client.get(
          `/tender?unit_id=${unitId}&job_number=${encodeURIComponent(
            jobNumber
          )}`
        );
      return response.data;
    }, `fetchTenderDetail(${unitId}, ${jobNumber})`);
  }

  /**
   * 併發抓取多個標案細節 - 使用 batchSize 控制同時發送的請求數量
   */
  async fetchTenderDetailsConcurrently(
    tenders: Array<{ unitId: string; jobNumber: string }>
  ): Promise<
    Array<{
      success: boolean;
      data?: TenderDetailResponse;
      error?: Error;
      unitId: string;
      jobNumber: string;
    }>
  > {
    if (tenders.length === 0) {
      return [];
    }

    const batchSize = this.config.processing.batchSize;
    console.log(
      `🚀 Starting batch fetch of ${tenders.length} tenders with batch size ${batchSize}`
    );

    const results: Array<{
      success: boolean;
      data?: TenderDetailResponse;
      error?: Error;
      unitId: string;
      jobNumber: string;
    }> = [];

    // 分批處理
    for (let i = 0; i < tenders.length; i += batchSize) {
      const batch = tenders.slice(i, i + batchSize);
      const batchNumber = Math.floor(i / batchSize) + 1;
      const totalBatches = Math.ceil(tenders.length / batchSize);

      console.log(
        `📦 Processing batch ${batchNumber}/${totalBatches} (${batch.length} items)`
      );

      // 同時發送這一批的所有請求
      const batchPromises = batch.map(async (tender) => {
        try {
          const data = await this.fetchTenderDetail(
            tender.unitId,
            tender.jobNumber
          );
          return {
            success: true,
            data,
            unitId: tender.unitId,
            jobNumber: tender.jobNumber,
          };
        } catch (error) {
          return {
            success: false,
            error: error as Error,
            unitId: tender.unitId,
            jobNumber: tender.jobNumber,
          };
        }
      });

      // 等待這一批全部完成
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);

      const successCount = batchResults.filter((r) => r.success).length;
      const failureCount = batchResults.length - successCount;
      console.log(
        `✓ Batch ${batchNumber} completed: ${successCount} success, ${failureCount} failed`
      );

      // 批次之間的間隔（如果不是最後一批）
      if (
        i + batchSize < tenders.length &&
        this.config.api.delayBetweenRequests > 0
      ) {
        console.log(
          `⏳ Waiting ${this.config.api.delayBetweenRequests}ms before next batch...`
        );
        await this.delay(this.config.api.delayBetweenRequests);
      }
    }

    const totalSuccess = results.filter((r) => r.success).length;
    const totalFailure = results.length - totalSuccess;
    console.log(
      `🎯 All batches completed: ${totalSuccess} success, ${totalFailure} failed`
    );

    return results;
  }

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  private async withRetry<T>(
    operation: () => Promise<T>,
    operationName: string
  ): Promise<T> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.config.api.retryAttempts; attempt++) {
      try {
        if (this.config.processing.batchSize <= 1) {
          console.log(
            `[${operationName}] Attempt ${attempt}/${this.config.api.retryAttempts}`
          );
        }

        const result = await operation();

        if (attempt > 1) {
          console.log(`[${operationName}] Succeeded on attempt ${attempt}`);
        }

        // 不再在這裡添加延遲，因為我們現在是批次處理
        return result;
      } catch (error) {
        lastError = error as Error;

        console.warn(
          `[${operationName}] Attempt ${attempt} failed:`,
          error instanceof Error ? error.message : error
        );

        if (attempt < this.config.api.retryAttempts) {
          const delayMs = Math.pow(2, attempt - 1) * 1000; // Exponential backoff: 1s, 2s, 4s
          console.log(`[${operationName}] Retrying in ${delayMs}ms...`);
          await this.delay(delayMs);
        }
      }
    }

    throw new Error(
      `Operation ${operationName} failed after ${this.config.api.retryAttempts} attempts: ${lastError?.message}`
    );
  }
}
