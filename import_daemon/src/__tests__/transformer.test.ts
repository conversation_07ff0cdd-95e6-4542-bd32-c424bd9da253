import { Transformer } from "../transformer";
import { TenderData, TenderRecord } from "../types";
import * as fs from "fs-extra";
import * as path from "path";

interface TestDataCollection {
  description: string;
  dates: Array<{
    date: string;
    count: number;
    totalFound: number;
  }>;
  totalRecords: number;
  records: TenderRecord[];
  generatedAt: string;
}

describe("Transformer", () => {
  let transformer: Transformer;
  let testData: TestDataCollection;
  let sampleRecords2022: TenderRecord[];
  let sampleRecords2025: TenderRecord[];

  // 緩存轉換結果以提高測試性能
  let transformedResults2022: TenderData[];
  let transformedResults2025: TenderData[];
  let allTransformedResults: TenderData[];

  beforeAll(async () => {
    transformer = new Transformer();

    // Load the combined test data with 40 records
    const combinedDataPath = path.resolve(
      __dirname,
      "../../test-data/combined-test-data.json"
    );
    testData = await fs.readJson(combinedDataPath);

    // Load individual date collections
    const data2022Path = path.resolve(
      __dirname,
      "../../test-data/sample-data-20220412.json"
    );
    const data2025Path = path.resolve(
      __dirname,
      "../../test-data/sample-data-20240625.json"
    );

    const data2022 = await fs.readJson(data2022Path);
    const data2025 = await fs.readJson(data2025Path);

    sampleRecords2022 = data2022.records;
    sampleRecords2025 = data2025.records;

    console.log(
      `📊 Loaded test data: ${testData.totalRecords} records from ${testData.dates.length} dates`
    );
    for (const date of testData.dates) {
      console.log(
        `  • ${date.date}: ${date.count} records (${date.totalFound} available)`
      );
    }

    // 預先轉換所有記錄以提高測試性能
    console.log("🚀 Pre-transforming records for better test performance...");
    const startTime = Date.now();

    transformedResults2022 = sampleRecords2022.map((record) =>
      transformer.transformToTenderData(record)
    );

    transformedResults2025 = sampleRecords2025.map((record) =>
      transformer.transformToTenderData(record)
    );

    allTransformedResults = [
      ...transformedResults2022,
      ...transformedResults2025,
    ];

    const endTime = Date.now();
    console.log(`✅ Pre-transformation completed in ${endTime - startTime}ms`);
  });

  describe("Test Data Loading", () => {
    it("should load test data correctly", () => {
      expect(testData).toBeDefined();
      expect(testData.totalRecords).toBe(40);
      expect(testData.records).toHaveLength(40);
      expect(sampleRecords2022).toHaveLength(20);
      expect(sampleRecords2025).toHaveLength(20);
    });
  });

  describe("transformToTenderData - 2022 Data", () => {
    it("should transform all 2022 records successfully", () => {
      // 使用預先轉換的結果
      const results = transformedResults2022;

      for (let i = 0; i < results.length; i++) {
        const result = results[i];

        // Basic structure checks - 使用英文字段名稱
        expect(result).toBeDefined();
        expect(result.tender_id).toBeTruthy();
        expect(result.title).toBeTruthy();

        // Debug information for failing test
        if (!result.agency_name) {
          console.log(`\n❌ 2022 Record ${i} missing agency_name:`);
          console.log(`  tender_id: ${result.tender_id}`);
          console.log(`  title: ${result.title}`);
          console.log(`  agency_name: "${result.agency_name}"`);
          console.log(
            `  Original record unit_name: "${sampleRecords2022[i]?.unit_name}"`
          );

          // Check if there's any agency data in the detail
          const detail = sampleRecords2022[i]?.detail;
          if (detail) {
            console.log(
              `  Detail keys with agency:`,
              Object.keys(detail).filter((k) => k.includes("機關"))
            );
          }

          // Only show first failure to avoid spam
          break;
        }

        expect(result.agency_name).toBeTruthy();

        // Budget should be parsed correctly
        expect(result.budget_amount).toBeGreaterThanOrEqual(0);

        // Date fields
        expect(result.publish_date).toBeTruthy();
        expect(result.project_id).toBeTruthy();

        // AI score should be within valid range
        expect(result.ai_score).toBeGreaterThanOrEqual(1);
        expect(result.ai_score).toBeLessThanOrEqual(10);

        // Status and type should be valid
        expect([
          "招標中",
          "已決標",
          "流標",
          "開標",
          "截標",
          "第一次公開招標",
          "awarded",
          "open",
        ]).toContain(result.status);
        expect([
          "招標",
          "決標",
          "流標",
          "tender",
          "award",
          "failure",
        ]).toContain(result.tender_type);
      }

      console.log(
        `✅ Successfully validated ${results.length} pre-transformed records from 2022/04/12`
      );

      // Log some sample results
      console.log("\\n🔍 Sample 2022 Records:");
      for (let i = 0; i < Math.min(3, results.length); i++) {
        const result = results[i];
        console.log(
          `  ${i + 1}. ${result.tender_id} - ${
            result.title
          } (Budget: $${result.budget_amount.toLocaleString()})`
        );
      }
    });

    it("should handle specific 2022 record transformation", () => {
      // Test the first record from 2022 in detail
      const firstRecord = sampleRecords2022[0];
      const result = transformedResults2022[0];

      // Should use actual case number from data when available, not constructed format
      expect(result.tender_id).toBe("HP11038P038");
      // Should use actual case number from data when available, not constructed format
      expect(result.project_id).toBe("HP11038P038");

      // Should have proper agency info
      expect(result.agency_name).toBeTruthy();
    });
  });

  describe("transformToTenderData - 2025 Data", () => {
    it("should transform all 2025 records successfully", () => {
      // 使用預先轉換的結果
      const results = transformedResults2025;

      for (let i = 0; i < results.length; i++) {
        const result = results[i];

        // Basic structure checks - 使用英文字段名稱
        expect(result).toBeDefined();
        expect(result.tender_id).toBeTruthy();
        expect(result.title).toBeTruthy();

        // Debug information for failing test
        if (!result.agency_name) {
          console.log(`\n❌ 2025 Record ${i} missing agency_name:`);
          console.log(`  tender_id: ${result.tender_id}`);
          console.log(`  title: ${result.title}`);
          console.log(`  agency_name: "${result.agency_name}"`);
          console.log(
            `  Original record unit_name: "${sampleRecords2025[i]?.unit_name}"`
          );

          // Check if there's any agency data in the detail
          const detail = sampleRecords2025[i]?.detail;
          if (detail) {
            console.log(
              `  Detail keys with agency:`,
              Object.keys(detail).filter((k) => k.includes("機關"))
            );
          }

          // Only show first failure to avoid spam
          break;
        }

        expect(result.agency_name).toBeTruthy();

        // Budget should be parsed correctly
        expect(result.budget_amount).toBeGreaterThanOrEqual(0);

        // Date fields
        expect(result.publish_date).toBeTruthy();
        expect(result.project_id).toBeTruthy();

        // AI score should be within valid range
        expect(result.ai_score).toBeGreaterThanOrEqual(1);
        expect(result.ai_score).toBeLessThanOrEqual(10);

        // Status and type should be valid
        expect([
          "招標中",
          "已決標",
          "流標",
          "開標",
          "截標",
          "第一次公開招標",
          "awarded",
          "open",
        ]).toContain(result.status);
        expect([
          "招標",
          "決標",
          "流標",
          "tender",
          "award",
          "failure",
        ]).toContain(result.tender_type);
      }

      console.log(
        `✅ Successfully validated ${results.length} pre-transformed records from 2025/06/25`
      );

      // Log some sample results
      console.log("\\n🔍 Sample 2025 Records:");
      for (let i = 0; i < Math.min(3, results.length); i++) {
        const result = results[i];
        console.log(
          `  ${i + 1}. ${result.tender_id} - ${
            result.title
          } (Budget: $${result.budget_amount.toLocaleString()})`
        );
      }
    });
  });

  describe("FieldMapper Functionality", () => {
    it("should create unmapped-fields.json when encountering unmapped fields", async () => {
      // 轉換已經在 beforeAll 中完成，這裡只需要檢查結果

      // Check if unmapped-fields.json was created (在非測試環境中)
      const unmappedFieldsPath = path.resolve(
        __dirname,
        "../../unmapped-fields.json"
      );
      const exists = await fs.pathExists(unmappedFieldsPath);

      if (exists) {
        const unmappedData = await fs.readJson(unmappedFieldsPath);
        console.log(
          "\\n📝 Unmapped fields found:",
          Object.keys(unmappedData).length
        );

        // Should be an object with field mappings
        expect(typeof unmappedData).toBe("object");

        // Each entry should have the required structure
        for (const [fieldName, fieldInfo] of Object.entries(unmappedData)) {
          expect(fieldInfo).toHaveProperty("sampleValue");
          expect(fieldInfo).toHaveProperty("recordCount");
          expect(fieldInfo).toHaveProperty("lastSeen");
          expect(typeof (fieldInfo as any).recordCount).toBe("number");
        }
      } else {
        console.log(
          "\\n📝 No unmapped-fields.json found (expected in test environment)"
        );
      }
    });

    it("should handle date conversion correctly", () => {
      const result = transformedResults2025[0]; // Use pre-transformed result

      // Check if dates are properly converted
      if (result.publish_date) {
        expect(result.publish_date).toBeInstanceOf(Date);
      }

      if (result.submission_deadline) {
        expect(result.submission_deadline).toBeInstanceOf(Date);
      }
    });

    it("should handle boolean conversion correctly", () => {
      const result = transformedResults2025[0]; // Use pre-transformed result

      // Check boolean fields - note these fields are now flattened in TenderData
      // We'll check if any boolean fields exist and are properly typed
      const booleanFields = Object.values(result).filter(
        (value) => typeof value === "boolean"
      );

      // If there are boolean fields, they should be properly typed
      booleanFields.forEach((field) => {
        expect(typeof field).toBe("boolean");
      });
    });

    it("should handle number conversion correctly", () => {
      const result = transformedResults2025[0]; // Use pre-transformed result

      // Check number fields
      expect(typeof result.budget_amount).toBe("number");
      expect(result.budget_amount).toBeGreaterThanOrEqual(0);

      // Check AI score
      expect(typeof result.ai_score).toBe("number");
      expect(result.ai_score).toBeGreaterThanOrEqual(1);
      expect(result.ai_score).toBeLessThanOrEqual(10);
    });
  });

  describe("Comprehensive Data Analysis", () => {
    it("should handle different tender types correctly", () => {
      // 使用預先轉換的結果
      const tenderTypes = new Set<string>();
      const statuses = new Set<string>();

      for (const result of allTransformedResults) {
        tenderTypes.add(result.tender_type);
        statuses.add(result.status);
      }

      console.log(
        `\\n📊 Found tender types: ${Array.from(tenderTypes).join(", ")}`
      );
      console.log(`📊 Found statuses: ${Array.from(statuses).join(", ")}`);

      // Should have at least one tender type
      expect(tenderTypes.size).toBeGreaterThan(0);
      expect(statuses.size).toBeGreaterThan(0);
    });

    it("should parse budgets correctly across all records", () => {
      // 使用預先轉換的結果
      const budgets: number[] = [];
      let nonZeroBudgets = 0;

      for (const result of allTransformedResults) {
        budgets.push(result.budget_amount);
        if (result.budget_amount > 0) {
          nonZeroBudgets++;
        }
      }

      console.log(`\\n💰 Budget Analysis:`);
      console.log(`  • Total records: ${budgets.length}`);
      console.log(`  • Records with budget > 0: ${nonZeroBudgets}`);
      console.log(`  • Max budget: $${Math.max(...budgets).toLocaleString()}`);
      console.log(`  • Min budget: $${Math.min(...budgets).toLocaleString()}`);

      // At least some records should have budgets
      expect(nonZeroBudgets).toBeGreaterThan(0);
    });

    it("should generate comprehensive agency info", () => {
      // 使用預先轉換的結果
      const agencies = new Set<string>();
      let completeAgencyInfo = 0;

      for (const result of allTransformedResults) {
        agencies.add(result.agency_name);

        // Check if we have complete agency info
        if (result.agency_name && result.agency_code && result.agency_address) {
          completeAgencyInfo++;
        }
      }

      console.log(`\\n🏛️ Agency Analysis:`);
      console.log(`  • Unique agencies: ${agencies.size}`);
      console.log(
        `  • Records with complete agency info: ${completeAgencyInfo}`
      );

      // Should have multiple agencies
      expect(agencies.size).toBeGreaterThan(1);

      // Most records should have agency names
      const recordsWithAgencyNames = allTransformedResults.filter(
        (r) => r.agency_name
      ).length;
      expect(recordsWithAgencyNames).toBeGreaterThan(
        allTransformedResults.length * 0.5
      ); // At least 50%
    });

    it("should extract key field data correctly from flat structure", () => {
      // Test that we're correctly extracting from the flat key structure
      const sampleRecord = sampleRecords2025[0]; // Use 2025 data which has the new structure
      const result = transformedResults2025[0]; // Use pre-transformed result

      // Log the structure for debugging
      console.log("\\n🔍 Debugging key extraction:");
      console.log("Detail keys:", Object.keys(sampleRecord.detail || {}));
      console.log(
        "Agency name key value:",
        (sampleRecord.detail as any)?.["機關資料:機關名稱"]
      );
      console.log(
        "Case name key value:",
        (sampleRecord.detail as any)?.["採購資料:標案名稱"]
      );
      console.log("Result title:", result.title);
      console.log("Result agency:", result.agency_name);

      // Verify correct extraction
      expect(result.title).toBe(
        (sampleRecord.detail as any)?.["採購資料:標案名稱"] ||
          sampleRecord.brief?.title
      );
      expect(result.agency_name).toBe(
        (sampleRecord.detail as any)?.["機關資料:機關名稱"] ||
          sampleRecord.unit_name
      );
    });

    it("should handle missing detail gracefully", () => {
      const sampleRecord = sampleRecords2025[0];
      const recordWithoutDetail = {
        ...sampleRecord,
        detail: undefined,
      } as any;

      expect(() =>
        transformer.transformToTenderData(recordWithoutDetail)
      ).toThrow("Missing detail for record");
    });

    it("should generate valid publish dates", () => {
      // 使用預先轉換的結果
      for (const result of allTransformedResults) {
        expect(result.publish_date).toBeDefined();
        // Should be a Date object or a valid date string
        if (result.publish_date instanceof Date) {
          expect(result.publish_date.getTime()).not.toBeNaN();
        } else {
          expect(result.publish_date).toMatch(/^\d{4}-\d{2}-\d{2}$/); // YYYY-MM-DD format
        }
      }
    });
  });

  describe("Error Handling", () => {
    it("should handle malformed data gracefully", () => {
      const malformedRecord = {
        date: 20220412,
        filename: "test",
        job_number: "test",
        unit_id: "test",
        detail: {
          type: "test",
        },
      } as any;

      const result = transformer.transformToTenderData(malformedRecord);

      // Should not throw but should have default values
      expect(result).toBeDefined();
      expect(result.tender_id).toBeTruthy();
      expect(result.agency_name).toBeDefined();
    });
  });

  describe("Type Validation", () => {
    it("should ensure all date fields are Date objects when converted", () => {
      const result = transformedResults2025[0]; // Use pre-transformed result

      // Check all date fields are proper Date objects when they exist
      const dateFields = [
        result.publish_date,
        result.submission_deadline,
        result.opening_time,
        result.award_date,
      ].filter(Boolean);

      dateFields.forEach((dateField) => {
        if (dateField instanceof Date) {
          expect(dateField.getTime()).not.toBeNaN();
        }
      });
    });

    it("should ensure all boolean fields are proper booleans when converted", () => {
      const result = transformedResults2025[0]; // Use pre-transformed result

      // Check boolean fields in the flattened structure
      const booleanFields = Object.values(result).filter(
        (value) => typeof value === "boolean"
      );

      booleanFields.forEach((booleanField) => {
        expect(typeof booleanField).toBe("boolean");
      });
    });

    it("should ensure all number fields are proper numbers when converted", () => {
      const result = transformedResults2025[0]; // Use pre-transformed result

      // Check number fields
      expect(typeof result.budget_amount).toBe("number");
      expect(result.budget_amount).not.toBeNaN();

      expect(typeof result.ai_score).toBe("number");
      expect(result.ai_score).not.toBeNaN();

      // Check other numeric fields if they exist
      const numericFields = Object.values(result).filter(
        (value) => typeof value === "number"
      );

      numericFields.forEach((numericField) => {
        expect(typeof numericField).toBe("number");
        expect(numericField).not.toBeNaN();
      });
    });
  });
});
