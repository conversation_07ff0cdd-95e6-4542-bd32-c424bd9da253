{"name": "import_daemon", "version": "1.0.0", "description": "Import tender data from PCC API", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "npm run build && node dist/index.js", "dev": "ts-node src/index.ts", "retry": "npm run build && node dist/index.js --retry", "status": "npm run build && node dist/index.js --status", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"axios": "^1.6.0", "commander": "^11.1.0", "date-fns": "^2.30.0", "dotenv": "^16.3.1", "fs-extra": "^11.1.1", "https-proxy-agent": "^7.0.2"}, "devDependencies": {"@types/fs-extra": "^11.0.2", "@types/jest": "^30.0.0", "@types/node": "^20.8.0", "jest": "^30.0.3", "ts-jest": "^29.4.0", "ts-node": "^10.9.1", "typescript": "^5.2.2"}}