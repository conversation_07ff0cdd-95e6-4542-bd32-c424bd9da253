import { Transformer } from './src/transformer';
import { TenderRecord } from './src/types';
import * as fs from 'fs';
import * as path from 'path';

// 測試新的標準化 TenderData 格式
async function testNewFormat() {
  console.log('🧪 Testing new standardized TenderData format...\n');
  
  const transformer = new Transformer();
  
  // 讀取一個測試文件
  const testFile = 'import_daemon/data/output/2020/01/3.13.30.53_1091601.json';
  
  if (!fs.existsSync(testFile)) {
    console.log('❌ Test file not found:', testFile);
    return;
  }
  
  try {
    const legacyData = JSON.parse(fs.readFileSync(testFile, 'utf8'));
    
    // 創建一個模擬的 TenderRecord
    const mockRecord: TenderRecord = {
      unit_id: '3.13.30.53',
      job_number: '1091601',
      unit_name: '臺灣糖業股份有限公司善化糖廠',
      date: '2020-01-03',
      brief: {
        title: '全自動種蔗機採購',
        category: '',
        url: 'https://web.pcc.gov.tw/prkms/tender/common/noticeDate/redirectPublic?ds=20200103&fn=PPW-1-50119504.xml'
      },
      detail: {
        type: '公開徵求廠商提供參考資料',
        url: 'https://web.pcc.gov.tw/prkms/tender/common/noticeDate/redirectPublic?ds=20200103&fn=PPW-1-50119504.xml',
        fetched_at: '2020-01-03T06:40:00+08:00',
        pkPmsMain: '',
        '機關資料:機關名稱': '臺灣糖業股份有限公司善化糖廠',
        '採購資料:標案名稱': '全自動種蔗機採購',
        '採購資料:標的分類': '<財物類>4321農業機械',
        '採購資料:預算金額': '5,000,000元',
        '採購資料:預算金額是否公開': '是',
        '招標資料:招標方式': '公開招標',
        '招標資料:決標方式': '最低標',
        '其他:履約地點': '臺南市善化區',
        '其他:履約期限': '契約簽訂後60日曆天'
      }
    };
    
    // 測試新格式轉換
    const standardData = transformer.transformToStandardTenderData(mockRecord);
    
    console.log('✅ New standardized format generated successfully!\n');
    
    // 顯示關鍵字段對比
    console.log('📊 Key Fields Comparison:');
    console.log('========================');
    console.log(`Legacy 標案編號: "${legacyData.標案編號}"`);
    console.log(`New tender_id: "${standardData.tender_id}"`);
    console.log();
    console.log(`Legacy 標案名稱: "${legacyData.標案名稱}"`);
    console.log(`New title: "${standardData.title}"`);
    console.log();
    console.log(`Legacy 機關名稱: "${legacyData.機關名稱}"`);
    console.log(`New agency_name: "${standardData.agency_name}"`);
    console.log();
    console.log(`Legacy 預算金額: ${legacyData.預算金額}`);
    console.log(`New budget_amount: ${standardData.budget_amount}`);
    console.log();
    console.log(`Legacy 標的分類: "${legacyData.標的分類}"`);
    console.log(`New category: "${standardData.category}"`);
    console.log(`New category_code: "${standardData.category_code}"`);
    console.log(`New category_type: "${standardData.category_type}"`);
    console.log();
    
    // 顯示新增的分析友好字段
    console.log('🆕 New Analytics-Friendly Fields:');
    console.log('=================================');
    console.log(`agency_code: "${standardData.agency_code}"`);
    console.log(`procurement_method: "${standardData.procurement_method}"`);
    console.log(`award_method: "${standardData.award_method}"`);
    console.log(`performance_location: "${standardData.performance_location}"`);
    console.log(`is_wto_gpa: ${standardData.is_wto_gpa}`);
    console.log(`is_multiple_award: ${standardData.is_multiple_award}`);
    console.log(`is_electronic_bidding: ${standardData.is_electronic_bidding}`);
    console.log(`budget_disclosed: ${standardData.budget_disclosed}`);
    console.log(`data_version: "${standardData.data_version}"`);
    console.log();
    
    // 保存新格式示例
    const outputPath = 'import_daemon/sample_new_format.json';
    fs.writeFileSync(outputPath, JSON.stringify(standardData, null, 2));
    console.log(`💾 New format sample saved to: ${outputPath}`);
    
    // 顯示數據庫友好的字段統計
    console.log('\n📈 Database-Friendly Structure:');
    console.log('===============================');
    const fields = Object.keys(standardData);
    const englishFields = fields.filter(f => /^[a-z_]+$/.test(f));
    const booleanFields = fields.filter(f => typeof standardData[f as keyof typeof standardData] === 'boolean');
    const numberFields = fields.filter(f => typeof standardData[f as keyof typeof standardData] === 'number');
    const dateFields = fields.filter(f => standardData[f as keyof typeof standardData] instanceof Date);
    
    console.log(`Total fields: ${fields.length}`);
    console.log(`English snake_case fields: ${englishFields.length}`);
    console.log(`Boolean fields: ${booleanFields.length}`);
    console.log(`Number fields: ${numberFields.length}`);
    console.log(`Date fields: ${dateFields.length}`);
    
  } catch (error) {
    console.error('❌ Error testing new format:', error);
  }
}

// 運行測試
testNewFormat().catch(console.error);
